import { ConfigService } from '@nestjs/config';
export declare class UploaddService {
    private configService;
    private s3Client;
    private bucketName;
    constructor(configService: ConfigService);
    uploadFile(file: Express.Multer.File, folder?: string): Promise<string>;
    uploadFiles(files: Express.Multer.File[], folder?: string): Promise<string[]>;
    deleteFile(fileUrl: string): Promise<boolean>;
}
