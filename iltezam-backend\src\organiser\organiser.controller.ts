import {
  Controller,
  Query,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  Body,
  Get,
  HttpException,
  Param,
  Post,
  Put,
  UnauthorizedException,
  UseGuards,
} from '@nestjs/common';
import { organiserService } from './organiser.service';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
import { CreateOrganizationDto } from './dto/organiser.dto';
import { Public } from 'src/shared/decorators/publicRoute.decorator';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('organiser')
export class OrganiserController {
  constructor(private readonly orgService: organiserService) {}

  @Post('create')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async createOrganization(
    @Body() createUserDto: CreateOrganizationDto,
    @Req() req: any,
  ) {
    try {
      const user = req.user;
      return await this.orgService.createOrganiser(createUserDto, user);
    } catch (e) {
      if (e.status === 409) {
        return { message: 'User already has an organization' };
      }
      throw new HttpException(e.message, e.status || 403);
    }
  }

  @Get('getorganiser')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getOrganization(@Req() req: any) {
    try {
      const userId = req.user.id;
      const organisers = await this.orgService.getOrganiser(userId);
      return {
        statusCode: 200,
        message: 'success',
        data: organisers,
      };
    } catch (e) {
      throw new HttpException(e.message, 403);
    }
  }

  @Get('organization/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getOrganizationProfile(@Param('id') id: number, @Req() req: any) {
    try {
      return await this.orgService.getOrganizationProfile(+id, req.user.id); // pass user ID
    } catch (e) {
      throw new HttpException(e.message, e.status || 403);
    }
  }

  @Post(':id/follow')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async followOrganization(@Param('id') id: number, @Req() req: any) {
    try {
      return await this.orgService.followOrganization(id, req.user);
    } catch (e) {
      throw new HttpException(e.message, e.status || 403);
    }
  }

  @Public()
  @Get(':organizationId/project/:projectId')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
async getProjectCategoryByOrgAndProject(
  @Param('organizationId') organizationId: number,
  @Param('projectId') projectId: number,
) {
  try {
    return await this.orgService.getProjectCategoryByOrgAndProject(
      +organizationId,
      +projectId,
    );
  } catch (e) {
    throw new HttpException(e.message, e.status || 403);
  }
}

}
