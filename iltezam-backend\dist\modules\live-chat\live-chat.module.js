"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LiveChatModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const live_chat_service_1 = require("./live-chat.service");
const live_chat_controller_1 = require("./live-chat.controller");
const chat_room_entity_1 = require("./entities/chat-room.entity");
const message_entity_1 = require("./entities/message.entity");
const user_entity_1 = require("../auth/entities/user.entity");
let LiveChatModule = class LiveChatModule {
};
exports.LiveChatModule = LiveChatModule;
exports.LiveChatModule = LiveChatModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([chat_room_entity_1.ChatRoom, message_entity_1.Message, user_entity_1.User])],
        providers: [live_chat_service_1.LiveChatService],
        controllers: [live_chat_controller_1.LiveChatController],
        exports: [live_chat_service_1.LiveChatService],
    })
], LiveChatModule);
//# sourceMappingURL=live-chat.module.js.map