import { User } from 'src/modules/auth/entities/user.entity';
import { Course } from './course.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
export declare class CourseProgress extends BaseEntityClass {
    user: User;
    course: Course;
    completedChapterIds: number[];
    videoProgress: {
        [chapterId: string]: number;
    };
    completedQuizIds: number[];
    lastUpdated: Date;
    lastWatchedChapterId: number | null;
    isCompleted: boolean;
}
