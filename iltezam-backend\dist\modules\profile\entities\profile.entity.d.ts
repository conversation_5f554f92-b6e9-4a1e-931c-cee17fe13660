import { User } from '../../auth/entities/user.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { ProfileConnection } from './profile.connection.entity';
export declare class Profile extends BaseEntityClass {
    fullName: string;
    organizationName: string;
    email: string;
    phone: string;
    bio: string;
    profilePicture: string;
    location: string;
    designation: string;
    skills: string[];
    user: User;
    sentConnections: ProfileConnection[];
    receivedConnections: ProfileConnection[];
}
