import { Test, TestingModule } from '@nestjs/testing';
import { CryptopaymentController } from './cryptopayment.controller';

describe('CryptopaymentController', () => {
  let controller: CryptopaymentController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CryptopaymentController],
    }).compile();

    controller = module.get<CryptopaymentController>(CryptopaymentController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
