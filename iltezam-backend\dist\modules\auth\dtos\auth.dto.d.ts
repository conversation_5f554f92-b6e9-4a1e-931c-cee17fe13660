export declare class AuthLoginDto {
    readonly email: string;
    readonly password: string;
}
export declare class RegisterDto {
    readonly email: string;
    readonly password: string;
    readonly confirmPassword: string;
    readonly isOrganization: boolean;
    readonly organizationName?: string;
    readonly FullName: string;
    readonly designation?: string;
}
export declare class ForgotPasswordDto {
    readonly email: string;
}
export declare class ResetNewPasswordDto {
    token: string;
    password: string;
}
export declare class verifyEmaildDto {
    email: string;
    code: string;
}
export declare class VerifyEmailDto {
    readonly code: string;
}
export declare class ResendVerificationDto {
    readonly email: string;
}
export declare class CompanyDto {
    readonly email: string;
    readonly name: string;
    readonly companyName: string;
}
