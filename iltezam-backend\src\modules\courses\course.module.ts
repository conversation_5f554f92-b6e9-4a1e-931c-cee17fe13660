import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Course } from './entities/course.entity';
import { Topic } from './entities/topic.entity';
import { Chapter } from './entities/chapter.entity';
import { CourseService } from './course.service';
import { CourseController } from './course.controller';
import { User } from 'src/modules/auth/entities/user.entity';
import { CourseProgress } from './entities/course-progress.entity';
import { Quiz } from './entities/quiz.entity';
import { QuizAttempt } from './entities/quiz-attempt.entity';
import { Certificate } from './entities/certificate.entity';
import { Question } from './entities/question.entity';
import { SavedCourse } from './entities/savedCourse.entity';
import { Enrollment } from './entities/enrollment.entity';
import { Organization } from 'src/organiser/entities/organiser.entities';
import { ChapterProgress } from './entities/chapter-progress.entity';


@Module({
  imports: [
    TypeOrmModule.forFeature([
      Course,
      Topic,
      Chapter,
      CourseProgress,
      ChapterProgress,
      Certificate,
      User,
      Quiz,
      QuizAttempt,
      Question,
      SavedCourse,
      Enrollment,
      Organization,
    ]),
  ],
  providers: [CourseService],
  controllers: [CourseController],
})
export class CourseModule {}
