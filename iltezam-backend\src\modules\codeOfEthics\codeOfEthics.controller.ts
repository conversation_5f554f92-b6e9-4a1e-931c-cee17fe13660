import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';
import { CodeOfEthicsService } from './codeOfEthics.services';
import { CreateCodeOfEthicsDto } from './DTO/codeOfEthics.dto';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('Code of Ethics')
@Controller('code-of-ethics')
export class CodeOfEthicsController {
  constructor(private readonly codeOfEthicsService: CodeOfEthicsService) {}

  @Post('create')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async create(@Body() createCodeOfEthicsDto: CreateCodeOfEthicsDto) {
    return await this.codeOfEthicsService.create(createCodeOfEthicsDto);
  }

  @Get('all')
  async findAll() {
    return await this.codeOfEthicsService.findAll();
  }

  //   @Get(':id')
  //   async findOne(@Param('id') id: string) {
  //     return await this.codeOfEthicsService.findOne(+id);
  //   }

  //   @Delete(':id')
  //   @UseGuards(JwtAuthGuard)
  //   @ApiBearerAuth()
  //   async remove(@Param('id') id: string) {
  //     return await this.codeOfEthicsService.remove(+id);
  //   }
}
