import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { CodeOfEthicsService } from './codeOfEthics.services';
import { CreateCodeOfEthicsDto } from './DTO/codeOfEthics.dto';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Public } from 'src/shared/decorators/publicRoute.decorator';

@ApiTags('Code of Ethics')
@Controller('code-of-ethics')
export class CodeOfEthicsController {
  constructor(private readonly codeOfEthicsService: CodeOfEthicsService) {}

  @Public()
  @Get('all')
  @ApiOperation({ summary: 'Get all codes of ethics' })
  @ApiResponse({ status: 200, description: 'Return all codes of ethics' })
  async findAll() {
    try {
      const result = await this.codeOfEthicsService.findAllCodeOfEthics();
      return {
        statusCode: 200,
        message: 'success',
        data: result,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('create')
  // @UseGuards(JwtAuthGuard)
  // @ApiBearerAuth()
  async create(@Body() createCodeOfEthicsDto: CreateCodeOfEthicsDto) {
    return await this.codeOfEthicsService.create(createCodeOfEthicsDto);
  }

  //   @Get(':id')
  //   async findOne(@Param('id') id: string) {
  //     return await this.codeOfEthicsService.findOne(+id);
  //   }

  //   @Delete(':id')
  //   @UseGuards(JwtAuthGuard)
  //   @ApiBearerAuth()
  //   async remove(@Param('id') id: string) {
  //     return await this.codeOfEthicsService.remove(+id);
  //   }
}
