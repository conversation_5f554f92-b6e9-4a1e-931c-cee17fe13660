"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChapterProgress = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../auth/entities/user.entity");
const chapter_entity_1 = require("./chapter.entity");
const course_entity_1 = require("./course.entity");
const base_entity_1 = require("../../auth/entities/base.entity");
let ChapterProgress = class ChapterProgress extends base_entity_1.BaseEntityClass {
    user;
    chapter;
    course;
    progressInSeconds;
    isCompleted;
    isLocked;
};
exports.ChapterProgress = ChapterProgress;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.chapterProgress, { eager: false }),
    __metadata("design:type", user_entity_1.User)
], ChapterProgress.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => chapter_entity_1.Chapter, { eager: false }),
    __metadata("design:type", chapter_entity_1.Chapter)
], ChapterProgress.prototype, "chapter", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => course_entity_1.Course, { eager: false }),
    __metadata("design:type", course_entity_1.Course)
], ChapterProgress.prototype, "course", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'double precision', default: 0 }),
    __metadata("design:type", Number)
], ChapterProgress.prototype, "progressInSeconds", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], ChapterProgress.prototype, "isCompleted", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: true }),
    __metadata("design:type", Boolean)
], ChapterProgress.prototype, "isLocked", void 0);
exports.ChapterProgress = ChapterProgress = __decorate([
    (0, typeorm_1.Entity)()
], ChapterProgress);
//# sourceMappingURL=chapter-progress.entity.js.map