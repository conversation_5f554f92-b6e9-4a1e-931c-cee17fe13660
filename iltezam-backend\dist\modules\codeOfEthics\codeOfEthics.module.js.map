{"version": 3, "file": "codeOfEthics.module.js", "sourceRoot": "", "sources": ["../../../src/modules/codeOfEthics/codeOfEthics.module.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAAwC;AACxC,6CAAgD;AAChD,uEAAmE;AACnE,mEAA8D;AAC9D,sEAA4D;AAQrD,IAAM,kBAAkB,GAAxB,MAAM,kBAAkB;CAAG,CAAA;AAArB,gDAAkB;6BAAlB,kBAAkB;IAN9B,IAAA,eAAM,EAAC;QACN,OAAO,EAAE,CAAC,uBAAa,CAAC,UAAU,CAAC,CAAC,kCAAY,CAAC,CAAC,CAAC;QACnD,WAAW,EAAE,CAAC,gDAAsB,CAAC;QACrC,SAAS,EAAE,CAAC,2CAAmB,CAAC;QAChC,OAAO,EAAE,CAAC,2CAAmB,CAAC;KAC/B,CAAC;GACW,kBAAkB,CAAG"}