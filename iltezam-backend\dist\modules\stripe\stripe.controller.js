"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeController = void 0;
const common_1 = require("@nestjs/common");
const stripe_service_1 = require("./stripe.service");
const stripe_payment_dto_1 = require("./dtos/stripe-payment.dto");
const save_card_dto_1 = require("./dtos/save-card.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const publicRoute_decorator_1 = require("../../shared/decorators/publicRoute.decorator");
let StripeController = class StripeController {
    stripeService;
    constructor(stripeService) {
        this.stripeService = stripeService;
    }
    async createPaymentIntent(createPaymentIntentDto) {
        return this.stripeService.createPaymentIntent(createPaymentIntentDto);
    }
    async saveCardDetails(saveCardDetailsDto, req) {
        const userId = req.user.id;
        return this.stripeService.saveCardDetails(saveCardDetailsDto, userId);
    }
    async getSavedCards(req) {
        const userId = req.user.id;
        return this.stripeService.getSavedCards(userId);
    }
    async handleWebhook(req, res, signature) {
        try {
            console.log('🔔 Webhook received, verifying...');
            const event = this.stripeService.constructEvent(req.rawBody, signature);
            const response = await this.stripeService.handleWebhook(event);
            return res.status(200).json(response);
        }
        catch (err) {
            console.error('❌ Webhook error:', err.message);
            return res.status(400).send(`Webhook Error: ${err.message}`);
        }
    }
};
exports.StripeController = StripeController;
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Post)('create-payment-intent'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [stripe_payment_dto_1.CreatePaymentIntentDto]),
    __metadata("design:returntype", Promise)
], StripeController.prototype, "createPaymentIntent", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('save-card'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [save_card_dto_1.SaveCardDetailsDto, Object]),
    __metadata("design:returntype", Promise)
], StripeController.prototype, "saveCardDetails", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Get)('saved-cards'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], StripeController.prototype, "getSavedCards", null);
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Post)('webhook'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Headers)('stripe-signature')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String]),
    __metadata("design:returntype", Promise)
], StripeController.prototype, "handleWebhook", null);
exports.StripeController = StripeController = __decorate([
    (0, common_1.Controller)('stripe'),
    __metadata("design:paramtypes", [stripe_service_1.StripeService])
], StripeController);
//# sourceMappingURL=stripe.controller.js.map