import { ProjectStatus, ProjectCategory } from '../entities/project.entity';
export declare class CreateProjectDto {
    projectName: string;
    projectCategory: ProjectCategory;
    status?: ProjectStatus;
    images?: string[];
    thumbnailImage?: string;
    fundRaisingGoal?: number;
    fundsCollected?: number;
    location?: string;
    startDate: string;
    finishDate: string;
    projectDescription: string;
    requiredVolunteers?: number;
    totalFilledPositions?: string[];
    positions?: CreatePositionDto[];
}
export declare class PositionDto {
    title: string;
    description: string;
    requiredSkills?: string;
    volunteersNeeded?: number;
}
export declare class UpdateProjectDto {
    projectName?: string;
    projectCategory?: ProjectCategory;
    status?: ProjectStatus;
    images?: string[];
    fundRaisingGoal?: number;
    location?: string;
    startDate?: string;
    finishDate?: string;
    projectDescription?: string;
    requiredVolunteers?: number;
    positions?: PositionDto[];
    ProjectStatus: ProjectStatus;
}
export declare class CreatePositionDto {
    positionName: string;
    requiredVolunteers: number;
    positionDescription: string;
}
