import { Repository } from 'typeorm';
import { Post } from './entities/post.entities';
import { CreateCommentDto, CreatePostDto } from './dto/post.dto';
import { User } from '../auth/entities/user.entity';
import { Comment } from './entities/comment.entities';
import { Profile } from '../profile/entities/profile.entity';
export declare class PostService {
    private readonly postRepo;
    private readonly userRepo;
    private readonly commentRepo;
    private readonly profileRepo;
    constructor(postRepo: Repository<Post>, userRepo: Repository<User>, commentRepo: Repository<Comment>, profileRepo: Repository<Profile>);
    createPost(createPostDto: CreatePostDto, userId: number): Promise<any>;
    editPost(postId: number, updateDto: CreatePostDto, userId: number): Promise<any>;
    deletePost(postId: number, userId: number): Promise<any>;
    like(params: {
        post_id: number;
    }, userId: number): Promise<any>;
    likess(params: {
        post_id: number;
    }, userId: number): Promise<any>;
    share(params: {
        post_id: number;
    }, userId: number): Promise<any>;
    createComment(createCommentDto: CreateCommentDto): Promise<any>;
    getAllPosts(): Promise<any>;
    getAllPostss(): Promise<any>;
    getPostsByUssserId(userId: number): Promise<any>;
    getPostsByUserId(userId: number): Promise<any>;
}
