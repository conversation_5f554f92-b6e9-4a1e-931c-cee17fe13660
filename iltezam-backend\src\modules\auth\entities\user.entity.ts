import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
  OneToOne,
  JoinColumn,
} from 'typeorm';

import { BaseEntityClass } from './base.entity';
import { Post } from 'src/modules/post/entities/post.entities';
import { Organization } from 'src/organiser/entities/organiser.entities';
import { Enrollment } from 'src/modules/courses/entities/enrollment.entity';
import { Profile } from 'src/modules/profile/entities/profile.entity';
import { Notification } from 'src/modules/notification/entities/notification.entity';
import { CourseProgress } from 'src/modules/courses/entities/course-progress.entity';
import { Course } from 'src/modules/courses/entities/course.entity';
import { Quiz } from 'src/modules/courses/entities/quiz.entity';
import { ProfileConnection } from 'src/modules/profile/entities/profile.connection.entity';
import { ChapterProgress } from 'src/modules/courses/entities/chapter-progress.entity';

@Entity({ name: 'users' })
export class User extends BaseEntityClass {
  @Column({ unique: true })
  email: string;

  @Column({ default: false, nullable: true })
  @Column({ nullable: true })
  FullName: string;

  @Column()
  password: string;

  @Column({ default: false })
  designation: string;

  @Column({ nullable: true })
  profilePicture: string;

  @Column({ default: false })
  isOrganization: boolean;

  @Column({ default: false })
  isFormFillUp: boolean;

  @Column({ nullable: true })
  organizationName?: string;

  @Column({ default: false })
  isEmailVerified: boolean;

  @Column({ nullable: true })
  verificationCode: string;

  @Column({ nullable: true })
  resetPasswordToken: string;

  @Column({ default: false })
  forgotPasswordTokenMatch: boolean;

  @Column({ type: 'timestamptz', nullable: true })
  resetPasswordTokenExpiry: Date;

  @OneToMany(() => CourseProgress, (progress) => progress.user)
  courseProgress: CourseProgress[];

  @ManyToMany(() => Course)
  @JoinTable()
  savedCourses: Course[];

  @OneToMany(() => Course, (course) => course.createdBy)
  courses: Course[];

  @OneToMany(() => ChapterProgress, (chapterProgress) => chapterProgress.user)
  chapterProgress: ChapterProgress[];

  @OneToMany(() => Quiz, (quiz) => quiz.createdBy)
  quizzes: Quiz[];

  @OneToMany(() => Enrollment, (enrollment) => enrollment.user)
  enrollments: Enrollment[];

  @OneToMany(() => Post, (post) => post.user)
  posts: Post[];

  @ManyToMany(() => Post, (post) => post.likedUsers)
  likedPosts: Post[];

  @ManyToMany(() => Post, (post) => post.sharedUsers)
  sharedPosts: Post[];

  @OneToOne(() => Organization, (org) => org.user)
  organizationDetails: Organization;

  @OneToOne(() => Profile, (profile) => profile.user)
  @JoinColumn()
  profile: Profile;

  @OneToMany(() => Notification, (notification) => notification.user)
  notifications: Notification[];

  @OneToMany(() => ProfileConnection, (connection) => connection.requester)
  sentConnections: ProfileConnection[];
  @OneToMany(() => ProfileConnection, (connection) => connection.receiver)
  receivedConnections: ProfileConnection[];
}
