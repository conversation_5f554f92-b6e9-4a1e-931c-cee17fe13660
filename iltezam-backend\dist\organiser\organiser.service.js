"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.organiserService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_entity_1 = require("../modules/auth/entities/user.entity");
const organiser_entities_1 = require("./../organiser/entities/organiser.entities");
const organization_follow_entities_1 = require("./../organiser/entities/organization.follow.entities");
const common_2 = require("@nestjs/common");
let organiserService = class organiserService {
    userRepo;
    orgRepo;
    followRepo;
    projectRepo;
    constructor(userRepo, orgRepo, followRepo) {
        this.userRepo = userRepo;
        this.orgRepo = orgRepo;
        this.followRepo = followRepo;
    }
    async createOrganiser(createUserDto, user) {
        try {
            const fullUser = await this.userRepo.findOne({ where: { id: user.id } });
            if (!fullUser) {
                throw new common_2.HttpException('User not found', common_2.HttpStatus.NOT_FOUND);
            }
            const existingOrg = await this.orgRepo.findOne({
                where: { user: { id: user.id } },
            });
            if (existingOrg) {
                throw new common_2.HttpException('Organization already exists for this user', common_2.HttpStatus.CONFLICT);
            }
            fullUser.isFormFillUp = true;
            const newOrg = this.orgRepo.create({
                ...createUserDto,
                user: fullUser,
            });
            return await this.orgRepo.save(newOrg);
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || common_2.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrganiser(userId) {
        try {
            const organizations = await this.orgRepo.find({
                relations: ['user', 'followers', 'followers.follower'],
            });
            const data = organizations.map((organization) => {
                const isFollowing = organization.followers?.some((follow) => follow.follower?.id === userId && follow.status === true);
                return {
                    id: organization.id,
                    created_at: organization.created_at,
                    updated_at: organization.updated_at,
                    email: organization.email,
                    organizationName: organization.organizationName,
                    organizationNameArabic: organization.organizationNameArabic,
                    country: organization.country,
                    website: organization.website,
                    licenseFilePath: organization.licenseFilePath,
                    contactName: organization.contactName,
                    contactPhone: organization.contactPhone,
                    contactTitle: organization.contactTitle,
                    contactEmail: organization.contactEmail,
                    goodGovernance: organization.goodGovernance,
                    transparencyReporting: organization.transparencyReporting,
                    sustainableFunding: organization.sustainableFunding,
                    impactMeasurement: organization.impactMeasurement,
                    shortBio: organization.shortBio,
                    organizationImage: organization.organizationImage,
                    organizationTags: organization.organizationTags,
                    status: isFollowing,
                    user: {
                        id: organization.user.id,
                        created_at: organization.user.created_at,
                        updated_at: organization.user.updated_at,
                        email: organization.user.email,
                        FullName: organization.user.FullName,
                        designation: organization.user.designation,
                        profilePicture: organization.user.profilePicture,
                        isOrganization: organization.user.isOrganization,
                        organizationName: organization.user.organizationName,
                        isEmailVerified: organization.user.isEmailVerified,
                    },
                    followers: organization.followers?.map((f) => ({
                        id: f.id,
                        created_at: f.created_at,
                        updated_at: f.updated_at,
                        followedAt: f.followedAt,
                        status: f.status,
                        follower: {
                            email: f.follower?.email,
                            profilePicture: f.follower?.profilePicture,
                            isOrganization: f.follower?.isOrganization,
                            organizationName: f.follower?.organizationName,
                        },
                    })) || [],
                };
            });
            return data;
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || common_2.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrganiserById(id) {
        try {
            const org = await this.orgRepo.findOne({
                where: { id },
                relations: ['user'],
            });
            if (!org) {
                throw new common_2.HttpException('Organization not found', common_2.HttpStatus.NOT_FOUND);
            }
            return org;
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || common_2.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async followOrganization(orgId, user) {
        try {
            const organization = await this.orgRepo.findOne({ where: { id: orgId } });
            const fullUser = await this.userRepo.findOne({ where: { id: user.id } });
            if (!organization || !fullUser) {
                throw new common_2.HttpException('Organization or user not found', common_2.HttpStatus.NOT_FOUND);
            }
            const existingFollow = await this.followRepo.findOne({
                where: {
                    organization: { id: orgId },
                    follower: { id: user.id },
                },
            });
            if (existingFollow) {
                existingFollow.status = !existingFollow.status;
                existingFollow.followedAt = new Date();
                const updatedFollow = await this.followRepo.save(existingFollow);
                return {
                    statusCode: 200,
                    message: updatedFollow.status
                        ? 'Organization followed successfully'
                        : 'Organization unfollowed successfully',
                    data: {
                        status: updatedFollow.status,
                        follow: updatedFollow,
                    },
                };
            }
            const follow = this.followRepo.create({
                organization,
                follower: fullUser,
                status: true,
                followedAt: new Date(),
            });
            const savedFollow = await this.followRepo.save(follow);
            return {
                statusCode: 200,
                message: 'Organization followed successfully',
                data: {
                    status: true,
                    follow: savedFollow,
                },
            };
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || common_2.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrganizationProfile(orgId, userId) {
        try {
            const organization = await this.orgRepo.findOne({
                where: { id: orgId },
                relations: ['user', 'followers', 'followers.follower'],
            });
            if (!organization) {
                throw new common_2.HttpException('Organization not found', common_2.HttpStatus.NOT_FOUND);
            }
            const isFollowing = organization.followers?.some((follow) => follow.follower?.id === userId && follow.status === true);
            return {
                id: organization.id,
                email: organization.email,
                organizationName: organization.organizationName,
                organizationNameArabic: organization.organizationNameArabic,
                country: organization.country,
                website: organization.website,
                licenseFilePath: organization.licenseFilePath,
                contactName: organization.contactName,
                contactPhone: organization.contactPhone,
                contactTitle: organization.contactTitle,
                contactEmail: organization.contactEmail,
                goodGovernance: organization.goodGovernance,
                transparencyReporting: organization.transparencyReporting,
                sustainableFunding: organization.sustainableFunding,
                impactMeasurement: organization.impactMeasurement,
                shortBio: organization.shortBio,
                organizationImage: organization.organizationImage,
                organizationTags: organization.organizationTags,
                user: {
                    id: organization.user.id,
                    name: organization.user.organizationName,
                    email: organization.user.email,
                },
                followers: organization.followers?.map((f) => ({
                    id: f.follower?.id,
                    email: f.follower?.email,
                    fullName: f.follower?.FullName || null,
                    status: f.status,
                })) || [],
                status: isFollowing,
            };
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || common_2.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getProjectCategoryByOrgAndProject(orgId, projectId) {
        try {
            const project = await this.projectRepo.findOne({
                where: {
                    id: projectId,
                    organization: { id: orgId },
                },
                relations: ['organization', 'positions', 'createdBy'],
            });
            if (!project) {
                throw new common_2.HttpException('Project not found under this organization', common_2.HttpStatus.NOT_FOUND);
            }
            return {
                organizationId: orgId,
                projectId: project.id,
                projectName: project.projectName,
                projectCategory: project.projectCategory,
                status: project.status,
                images: project.images,
                fundRaisingGoal: project.fundRaisingGoal,
                fundsCollected: project.fundsCollected,
                location: project.location,
                startDate: project.startDate,
                finishDate: project.finishDate,
                projectDescription: project.projectDescription,
                thumbnailImage: project.thumbnailImage,
                totalClicks: project.totalClicks,
                totalFilledPositions: project.totalFilledPositions,
                positions: project.positions,
                organization: {
                    id: project.organization?.id,
                    organizationName: project.organization?.organizationName,
                },
            };
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || common_2.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.organiserService = organiserService;
exports.organiserService = organiserService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(1, (0, typeorm_1.InjectRepository)(organiser_entities_1.Organization)),
    __param(2, (0, typeorm_1.InjectRepository)(organization_follow_entities_1.OrganizationFollow)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], organiserService);
//# sourceMappingURL=organiser.service.js.map