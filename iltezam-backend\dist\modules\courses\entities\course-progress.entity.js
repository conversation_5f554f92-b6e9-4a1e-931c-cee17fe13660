"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseProgress = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../auth/entities/user.entity");
const course_entity_1 = require("./course.entity");
const base_entity_1 = require("../../auth/entities/base.entity");
let CourseProgress = class CourseProgress extends base_entity_1.BaseEntityClass {
    user;
    course;
    completedChapterIds;
    videoProgress;
    completedQuizIds;
    lastUpdated;
    lastWatchedChapterId;
    isCompleted;
};
exports.CourseProgress = CourseProgress;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.courseProgress, { eager: false }),
    __metadata("design:type", user_entity_1.User)
], CourseProgress.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => course_entity_1.Course, { eager: false }),
    __metadata("design:type", course_entity_1.Course)
], CourseProgress.prototype, "course", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { array: true, default: [] }),
    __metadata("design:type", Array)
], CourseProgress.prototype, "completedChapterIds", void 0);
__decorate([
    (0, typeorm_1.Column)('jsonb', { default: {} }),
    __metadata("design:type", Object)
], CourseProgress.prototype, "videoProgress", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { array: true, default: [] }),
    __metadata("design:type", Array)
], CourseProgress.prototype, "completedQuizIds", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], CourseProgress.prototype, "lastUpdated", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Object)
], CourseProgress.prototype, "lastWatchedChapterId", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], CourseProgress.prototype, "isCompleted", void 0);
exports.CourseProgress = CourseProgress = __decorate([
    (0, typeorm_1.Entity)()
], CourseProgress);
//# sourceMappingURL=course-progress.entity.js.map