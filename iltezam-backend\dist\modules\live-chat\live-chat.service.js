"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LiveChatService = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const chat_room_entity_1 = require("./entities/chat-room.entity");
const message_entity_1 = require("./entities/message.entity");
const user_entity_1 = require("../auth/entities/user.entity");
let LiveChatService = class LiveChatService {
    chatRoomRepository;
    messageRepository;
    userRepository;
    io;
    constructor(chatRoomRepository, messageRepository, userRepository) {
        this.chatRoomRepository = chatRoomRepository;
        this.messageRepository = messageRepository;
        this.userRepository = userRepository;
    }
    setIo(io) {
        this.io = io;
        this.setupSocketEvents();
    }
    async createRoom(senderId, receiverId) {
        if (!senderId || !receiverId) {
            throw new Error('Sender and Receiver required');
        }
        const userIds = [senderId, receiverId];
        userIds.sort((a, b) => a - b);
        const room = await this.chatRoomRepository
            .createQueryBuilder('room')
            .where('room.users = :userIds', { userIds })
            .getOne();
        if (room) {
            return {
                roomId: room.roomId,
                senderId: senderId,
                receiverId: receiverId,
            };
        }
        const newRoom = this.chatRoomRepository.create({
            roomId: (0, uuid_1.v4)(),
            users: userIds,
        });
        const savedRoom = await this.chatRoomRepository.save(newRoom);
        return {
            roomId: savedRoom.roomId,
            senderId: senderId,
            receiverId: receiverId,
        };
    }
    async sendMessage(senderId, roomId, receiverId, messageText) {
        if (!roomId || !senderId || !receiverId || !messageText) {
            throw new Error('All fields (roomId, sender, receiver, message) are required');
        }
        const sender = await this.userRepository.findOne({
            where: { id: senderId },
        });
        const receiver = await this.userRepository.findOne({
            where: { id: receiverId },
        });
        if (!sender || !receiver) {
            throw new Error('Sender or Receiver not found');
        }
        const msg = new message_entity_1.Message();
        msg.roomId = roomId;
        msg.sender = sender;
        msg.receiver = receiver;
        msg.message = messageText;
        const savedMessage = await this.messageRepository.save(msg);
        console.log('saved message', savedMessage);
        if (this.io) {
            this.io.to(roomId).emit('receiveMessage', msg);
        }
        return { success: true, message: 'Message sent' };
    }
    async createGroupRoom(creatorId, userIds, groupName, groupPicture) {
        if (!creatorId || !userIds || userIds.length === 0 || !groupName) {
            throw new Error('Creator and at least one user required');
        }
        if (!userIds.includes(creatorId)) {
            userIds.push(creatorId);
        }
        const newRoom = this.chatRoomRepository.create({
            roomId: (0, uuid_1.v4)(),
            users: userIds,
            isGroup: true,
            groupName: groupName || 'New Group',
            groupPicture: groupPicture,
        });
        const savedRoom = await this.chatRoomRepository.save(newRoom);
        const creatorUser = await this.userRepository.findOne({
            where: { id: creatorId },
        });
        if (creatorUser) {
            const systemMessage = new message_entity_1.Message();
            systemMessage.roomId = savedRoom.roomId;
            systemMessage.sender = creatorUser;
            systemMessage.message = `Group "${groupName}" created by ${creatorUser.FullName || creatorUser.email}`;
            await this.messageRepository.save(systemMessage);
        }
        return { roomId: savedRoom.roomId, groupName: savedRoom.groupName, groupImage: savedRoom.groupPicture };
    }
    async sendGroupMessage(senderId, roomId, messageText) {
        if (!roomId || !senderId || !messageText) {
            throw new Error('All fields (roomId, sender, message) are required');
        }
        const room = await this.chatRoomRepository.findOne({
            where: { roomId },
        });
        if (!room) {
            throw new Error('Room not found');
        }
        if (!room.users.includes(senderId)) {
            throw new Error('Sender is not a member of this group');
        }
        const sender = await this.userRepository.findOne({
            where: { id: senderId },
        });
        if (!sender) {
            throw new Error('Sender not found');
        }
        const msg = new message_entity_1.Message();
        msg.roomId = roomId;
        msg.sender = sender;
        msg.message = messageText;
        msg.readUsers = [senderId];
        const savedMessage = await this.messageRepository.save(msg);
        if (this.io) {
            this.io.to(roomId).emit('receiveMessage', msg);
        }
        return { success: true, message: 'Message sent' };
    }
    async getMessages(roomId, userId) {
        if (!roomId) {
            throw new Error('Room ID is required');
        }
        const room = await this.chatRoomRepository.findOne({
            where: { roomId },
        });
        if (!room) {
            throw new Error('Room not found');
        }
        if (room.isGroup) {
            const messages = await this.messageRepository.find({
                where: { roomId },
                relations: ['sender'],
                order: { timestamp: 'ASC' },
            });
            for (const msg of messages) {
                if (!Array.isArray(msg.readUsers))
                    msg.readUsers = [];
                if (!msg.readUsers.includes(userId)) {
                    msg.readUsers.push(userId);
                }
                const allRead = room.users.every((uid) => msg.readUsers.includes(uid));
                if (allRead && !msg.isRead) {
                    msg.isRead = true;
                }
                await this.messageRepository.save(msg);
            }
            return messages;
        }
        else {
            await this.messageRepository.update({ roomId: room.roomId, receiver: { id: userId } }, { isRead: true });
            return await this.messageRepository.find({
                where: { roomId },
                relations: ['sender', 'receiver'],
                order: { timestamp: 'ASC' },
            });
        }
    }
    async getInbox(userId) {
        if (!userId) {
            throw new Error('User ID is required');
        }
        const rooms = await this.chatRoomRepository
            .createQueryBuilder('room')
            .where(':userId = ANY(room.users)', { userId })
            .getMany();
        const roomIds = rooms.map((room) => room.roomId);
        const latestMessages = [];
        const unreadCounts = {};
        for (const room of rooms) {
            const latestMessage = await this.messageRepository.findOne({
                where: { roomId: room.roomId },
                relations: ['sender'],
                order: { timestamp: 'DESC' },
            });
            if (latestMessage) {
                const unreadCount = await this.messageRepository.count({
                    where: {
                        roomId: room.roomId,
                        isRead: false,
                        sender: { id: (0, typeorm_2.Not)(userId) },
                    },
                });
                unreadCounts[room.roomId] = unreadCount;
                if (room.isGroup) {
                    let unreadCount = 0;
                    const groupMessages = await this.messageRepository.find({
                        where: { roomId: room.roomId },
                        select: ['readUsers'],
                    });
                    unreadCount = groupMessages.reduce((count, msg) => {
                        if (!Array.isArray(msg.readUsers) || !msg.readUsers.includes(userId)) {
                            return count + 1;
                        }
                        return count;
                    }, 0);
                    unreadCounts[room.roomId] = unreadCount;
                    latestMessages.push({
                        roomId: room.roomId,
                        isGroup: true,
                        groupName: room.groupName,
                        groupImage: room.groupPicture,
                        message: latestMessage.message,
                        senderName: latestMessage.sender.FullName || latestMessage.sender.email,
                        timestamp: latestMessage.timestamp,
                        profilePicture: latestMessage.sender.profilePicture,
                        unreadMessages: unreadCount,
                    });
                }
                else {
                    const otherUserId = room.users.find((id) => id !== userId);
                    const otherUser = await this.userRepository.findOne({
                        where: { id: otherUserId },
                    });
                    console.log('Other User:', otherUser?.id);
                    latestMessages.push({
                        roomId: room.roomId,
                        userId: otherUser?.id,
                        isGroup: false,
                        senderId: otherUserId,
                        senderName: otherUser?.FullName || otherUser?.email || 'Unknown',
                        profilePicture: otherUser?.profilePicture || null,
                        message: latestMessage.sender.id === userId
                            ? `You: ${latestMessage.message}`
                            : latestMessage.message,
                        timestamp: latestMessage.timestamp,
                        unreadMessages: unreadCount,
                    });
                }
            }
        }
        return latestMessages.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    }
    async getRoom(userId, partnerId) {
        if (!userId) {
            throw new Error('User ID is required');
        }
        const room = await this.chatRoomRepository
            .createQueryBuilder('room')
            .innerJoin('room.users', 'user1')
            .innerJoin('room.users', 'user2')
            .where('user1.id = :userId', { userId })
            .andWhere('user2.id = :partnerId', { partnerId })
            .getOne();
        if (!room) {
            throw new Error('No rooms found');
        }
        await this.messageRepository.update({ roomId: room.roomId, receiver: { id: userId } }, { isRead: true });
        return { roomId: room.roomId };
    }
    async getAllGroups(userId) {
        const groups = await this.chatRoomRepository.find({
            where: { isGroup: true },
            select: ['roomId', 'groupName', 'groupPicture', 'users'],
        });
        const grps = groups.map((group) => ({
            ...group,
            joined: group.users.includes(userId),
        }));
        const sortedGroups = grps.sort((a, b) => {
            if (a.joined && !b.joined)
                return -1;
            if (!a.joined && b.joined)
                return 1;
            return 0;
        });
        return sortedGroups;
    }
    async getInboxData(userId) {
        const messages = await this.messageRepository.find({
            where: { receiver: { id: userId } },
            relations: ['sender'],
            order: { timestamp: 'DESC' },
        });
        const latestMessages = {};
        const senderIds = new Set();
        const unreadCounts = {};
        messages.forEach((msg) => {
            const senderId = msg.sender.id;
            if (!unreadCounts[senderId])
                unreadCounts[senderId] = 0;
            if (!msg.isRead)
                unreadCounts[senderId] += 1;
            if (!latestMessages[senderId] ||
                new Date(msg.timestamp) > new Date(latestMessages[senderId].timestamp)) {
                latestMessages[senderId] = {
                    roomId: msg.roomId,
                    senderId: senderId,
                    message: msg.message,
                    isRead: msg.isRead,
                    timestamp: msg.timestamp,
                };
            }
            senderIds.add(senderId);
        });
        return Object.values(latestMessages).map((msg) => ({
            ...msg,
            unreadMessages: unreadCounts[msg.senderId] || 0,
        }));
    }
    async joinGroup(roomId, userId) {
        const room = await this.chatRoomRepository.findOne({ where: { roomId } });
        if (!room)
            throw new Error('Group not found');
        if (!room.isGroup)
            throw new Error('Not a group room');
        if (!room.users.includes(userId)) {
            room.users.push(userId);
            await this.chatRoomRepository.save(room);
        }
        return { success: true, message: 'Joined group', roomId };
    }
    setupSocketEvents() {
        this.io.on('connection', (socket) => {
            console.log(`User connected: ${socket.id}`);
            socket.on('joinRoom', async ({ roomId }) => {
                socket.join(roomId);
                console.log(`User joined room: ${roomId}`);
            });
            socket.on('sendMessage', async ({ roomId, sender, receiver, message, mediaUrl }) => {
                console.log(mediaUrl);
                try {
                    const room = await this.chatRoomRepository.findOne({
                        where: { roomId },
                    });
                    if (!room) {
                        console.log('Room not found');
                        return;
                    }
                    const senderUser = await this.userRepository.findOne({
                        where: { id: sender },
                    });
                    if (!senderUser) {
                        console.log('Sender not found');
                        return;
                    }
                    const newMessage = new message_entity_1.Message();
                    newMessage.roomId = roomId;
                    newMessage.sender = senderUser;
                    if (!room.isGroup && receiver) {
                        const receiverUser = await this.userRepository.findOne({
                            where: { id: receiver },
                        });
                        if (!receiverUser) {
                            console.log('Receiver not found');
                            return;
                        }
                        newMessage.receiver = receiverUser;
                    }
                    console.log("i am new message", newMessage);
                    if (room.isGroup) {
                        newMessage.readUsers = [sender];
                    }
                    newMessage.message = mediaUrl ? mediaUrl : message;
                    await this.messageRepository.save(newMessage);
                    const senderName = senderUser.FullName || senderUser.email || 'Unknown';
                    const title = `New Message from ${senderName}`;
                    const content = message
                        ? message
                        : mediaUrl
                            ? 'Sent an Attachment'
                            : 'No content';
                    this.io.to(roomId).emit('receiveMessage', newMessage);
                    if (room.isGroup) {
                        for (const userId of room.users) {
                            if (userId !== sender) {
                                const latestMessages = await this.getInboxData(userId);
                                this.io
                                    .to(userId.toString())
                                    .emit('updateInbox', latestMessages);
                            }
                        }
                    }
                    else if (receiver) {
                        const latestMessages = await this.getInboxData(receiver);
                        this.io
                            .to(receiver.toString())
                            .emit('updateInbox', latestMessages);
                    }
                }
                catch (error) {
                    console.error('Error in sendMessage:', error);
                }
            });
            socket.on('getOnlineUsers', ({ userIds }, callback) => {
                const onlineStatus = {};
                console.log('userIds from client:', userIds);
                for (const id of userIds) {
                    console.log("looping this id", id);
                    onlineStatus[id] = global.onlineUsers.has(id);
                }
                if (callback) {
                    callback(onlineStatus);
                }
                else {
                    socket.emit('onlineUsersStatus', onlineStatus);
                }
            });
            socket.on("getInbox", async (userId, callback) => {
                try {
                    const inbox = await this.getInbox(userId);
                    console.log("get inbox event");
                    if (callback) {
                        callback({ success: true, data: inbox });
                    }
                    else {
                        socket.emit("inbox", { success: true, data: inbox });
                    }
                }
                catch (error) {
                    if (callback) {
                        callback({ success: false, message: error.message });
                    }
                    else {
                        socket.emit("inbox", { success: false, message: error.message });
                    }
                }
            });
            socket.on('joinInbox', ({ userId }) => {
                socket.join(userId.toString());
            });
            socket.on('disconnect', () => {
                console.log(`User disconnected: ${socket.id}`);
            });
        });
    }
};
exports.LiveChatService = LiveChatService;
exports.LiveChatService = LiveChatService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(chat_room_entity_1.ChatRoom)),
    __param(1, (0, typeorm_1.InjectRepository)(message_entity_1.Message)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], LiveChatService);
//# sourceMappingURL=live-chat.service.js.map