import { Repository } from 'typeorm';
import { CodeOfEthics } from './Entity/codeOfEthics.entity';
import { CreateCodeOfEthicsDto } from './DTO/codeOfEthics.dto';
export declare class CodeOfEthicsService {
    private readonly codeOfEthicsRepository;
    constructor(codeOfEthicsRepository: Repository<CodeOfEthics>);
    create(createCodeOfEthicsDto: CreateCodeOfEthicsDto): Promise<CodeOfEthics>;
    findAllCodeOfEthics(): Promise<CodeOfEthics[]>;
}
