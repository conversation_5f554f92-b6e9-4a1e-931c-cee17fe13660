import { Repository } from 'typeorm';
import { Project } from './entities/project.entity';
import { Position } from './entities/position.entity';
import { Donation } from './entities/donation.entity';
import { VolunteerApplication, ApplicationStatus } from './entities/volunteer-application.entity';
import { User } from '../auth/entities/user.entity';
import { CreateProjectDto, UpdateProjectDto } from './dtos/project.dto';
import { CreateDonationDto } from './dtos/donation.dto';
import { CreateVolunteerApplicationDto } from './dtos/volunteer-app.dto';
import { Organization } from 'src/organiser/entities/organiser.entities';
export declare class ProjectService {
    private readonly projectRepo;
    private readonly organizationRepo;
    private readonly positionRepo;
    private readonly donationRepo;
    private readonly applicationRepo;
    private readonly userRepo;
    constructor(projectRepo: Repository<Project>, organizationRepo: Repository<Organization>, positionRepo: Repository<Position>, donationRepo: Repository<Donation>, applicationRepo: Repository<VolunteerApplication>, userRepo: Repository<User>);
    create(createProjectDto: CreateProjectDto, userId: number): Promise<Project>;
    findAll(category?: string, location?: string): Promise<any[]>;
    getOrganizationProjects(userId: number): Promise<Project[]>;
    findOne(id: string): Promise<any>;
    update(id: string, updateProjectDto: UpdateProjectDto, userId: string): Promise<any>;
    remove(id: string, userId: string): Promise<void>;
    getProjectsByOrganization(organizationId: string): Promise<any[]>;
    closeProject(projectId: string, userId: string): Promise<Project>;
    createDonation(createDonationDto: CreateDonationDto, userId: string): Promise<any>;
    getDonationsByUser(userId: string): Promise<any[]>;
    getDonationsByProject(projectId: string): Promise<any[]>;
    getDonationsById(id: string): Promise<Donation | null>;
    getVolunteerProjectWithPositions(): Promise<Project[]>;
    getVolunteerProjectPositionsById(projectId: string, positionId: string): Promise<any>;
    createApplication(projectId: string, positionId: string, createApplicationDto: CreateVolunteerApplicationDto, userId: string): Promise<VolunteerApplication>;
    getAllApplicationAgainstProject(projectId: string): Promise<any[]>;
    getApplicationsByUser(userId: string): Promise<VolunteerApplication[]>;
    getApplicationsByProject(projectId: string): Promise<VolunteerApplication[]>;
    updateApplicationStatus(applicationId: string, status: ApplicationStatus, organizationId: string): Promise<VolunteerApplication>;
    getOrganizationDonations(userId: string): Promise<any[]>;
}
