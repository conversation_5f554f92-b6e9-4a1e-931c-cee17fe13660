"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploaddService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const client_s3_1 = require("@aws-sdk/client-s3");
const uuid_1 = require("uuid");
let UploaddService = class UploaddService {
    configService;
    s3Client;
    bucketName;
    constructor(configService) {
        this.configService = configService;
        const bucketName = this.configService.get('aws.bucketName');
        const region = this.configService.get('aws.region');
        const accessKeyId = this.configService.get('aws.accessKeyId');
        const secretAccessKey = this.configService.get('aws.secretAccessKey');
        if (!bucketName || !region || !accessKeyId || !secretAccessKey) {
            throw new Error('AWS configuration is incomplete');
        }
        this.bucketName = bucketName;
        this.s3Client = new client_s3_1.S3Client({
            region: region,
            credentials: {
                accessKeyId: accessKeyId,
                secretAccessKey: secretAccessKey,
            },
        });
    }
    async uploadFile(file, folder = 'uploads') {
        if (!file) {
            throw new common_1.BadRequestException('No file provided');
        }
        const allowedMimeTypes = [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'application/pdf',
            'video/mp4',
            'video/webm',
            'video/ogg',
            'video/x-matroska',
            'video/quicktime',
            'video/3gpp',
        ];
        if (!allowedMimeTypes.includes(file.mimetype)) {
            throw new common_1.BadRequestException('Only images and videos  files are allowed');
        }
        const fileExtension = file.originalname.split('.').pop();
        const fileName = `${folder}/${(0, uuid_1.v4)()}.${fileExtension}`;
        const uploadParams = {
            Bucket: this.bucketName,
            Key: fileName,
            Body: file.buffer,
            ContentType: file.mimetype,
        };
        try {
            await this.s3Client.send(new client_s3_1.PutObjectCommand(uploadParams));
            return `https://${this.bucketName}.s3.${this.configService.get('aws.region')}.amazonaws.com/${fileName}`;
        }
        catch (error) {
            throw new common_1.BadRequestException(`Upload failed: ${error.message}`);
        }
    }
    async uploadFiles(files, folder = 'uploads') {
        return Promise.all(files.map((file) => this.uploadFile(file, folder)));
    }
    async deleteFile(fileUrl) {
        try {
            const key = fileUrl.split('amazonaws.com/')[1];
            const deleteParams = {
                Bucket: this.bucketName,
                Key: key,
            };
            await this.s3Client.send(new client_s3_1.DeleteObjectCommand(deleteParams));
            return true;
        }
        catch (error) {
            throw new common_1.BadRequestException(`Delete failed: ${error.message}`);
        }
    }
};
exports.UploaddService = UploaddService;
exports.UploaddService = UploaddService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], UploaddService);
//# sourceMappingURL=uploadd.service.js.map