"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionStatusResponseDto = exports.SupportedCurrenciesResponseDto = exports.SupportedCurrencyDto = exports.CryptoPaymentResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CryptoPaymentResponseDto {
    success;
    transactionId;
    amount;
    currency;
    address;
    confirmsNeeded;
    timeout;
    checkoutUrl;
    statusUrl;
    qrcodeUrl;
    message;
}
exports.CryptoPaymentResponseDto = CryptoPaymentResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Success status' }),
    __metadata("design:type", Boolean)
], CryptoPaymentResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction ID from CoinPayments' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Amount to pay in cryptocurrency' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Cryptocurrency code' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Payment address to send funds to' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of confirmations needed' }),
    __metadata("design:type", Number)
], CryptoPaymentResponseDto.prototype, "confirmsNeeded", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Payment timeout in seconds' }),
    __metadata("design:type", Number)
], CryptoPaymentResponseDto.prototype, "timeout", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Checkout URL for payment page' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "checkoutUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Status URL to check payment progress' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "statusUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'QR code URL for easy payment' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "qrcodeUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Response message' }),
    __metadata("design:type", String)
], CryptoPaymentResponseDto.prototype, "message", void 0);
class SupportedCurrencyDto {
    code;
    name;
    rateBtc;
    txFee;
    status;
    confirms;
    capabilities;
}
exports.SupportedCurrencyDto = SupportedCurrencyDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency code' }),
    __metadata("design:type", String)
], SupportedCurrencyDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency name' }),
    __metadata("design:type", String)
], SupportedCurrencyDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Exchange rate to BTC' }),
    __metadata("design:type", String)
], SupportedCurrencyDto.prototype, "rateBtc", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction fee' }),
    __metadata("design:type", String)
], SupportedCurrencyDto.prototype, "txFee", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Currency status' }),
    __metadata("design:type", String)
], SupportedCurrencyDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Required confirmations' }),
    __metadata("design:type", Number)
], SupportedCurrencyDto.prototype, "confirms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Available capabilities', type: [String] }),
    __metadata("design:type", Array)
], SupportedCurrencyDto.prototype, "capabilities", void 0);
class SupportedCurrenciesResponseDto {
    success;
    currencies;
    message;
}
exports.SupportedCurrenciesResponseDto = SupportedCurrenciesResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Success status' }),
    __metadata("design:type", Boolean)
], SupportedCurrenciesResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'List of supported currencies', type: [SupportedCurrencyDto] }),
    __metadata("design:type", Array)
], SupportedCurrenciesResponseDto.prototype, "currencies", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Response message' }),
    __metadata("design:type", String)
], SupportedCurrenciesResponseDto.prototype, "message", void 0);
class TransactionStatusResponseDto {
    success;
    transactionId;
    status;
    statusText;
    type;
    coin;
    amount;
    received;
    receivedConfirms;
    paymentAddress;
    timeCreated;
    timeExpires;
    message;
}
exports.TransactionStatusResponseDto = TransactionStatusResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Success status' }),
    __metadata("design:type", Boolean)
], TransactionStatusResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction ID' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "transactionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Payment status code' }),
    __metadata("design:type", Number)
], TransactionStatusResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Payment status text' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "statusText", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction type' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Cryptocurrency code' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "coin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Expected amount' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Received amount' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "received", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Received confirmations' }),
    __metadata("design:type", Number)
], TransactionStatusResponseDto.prototype, "receivedConfirms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Payment address' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "paymentAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction creation time' }),
    __metadata("design:type", Date)
], TransactionStatusResponseDto.prototype, "timeCreated", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Transaction expiration time' }),
    __metadata("design:type", Date)
], TransactionStatusResponseDto.prototype, "timeExpires", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Response message' }),
    __metadata("design:type", String)
], TransactionStatusResponseDto.prototype, "message", void 0);
//# sourceMappingURL=crypto-payment-response.dto.js.map