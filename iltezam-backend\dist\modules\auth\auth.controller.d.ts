import { AuthService } from './auth.service';
import { AuthLoginDto, verifyEmaildDto } from './dtos/auth.dto';
import { RegisterDto } from './dtos/auth.dto';
import { ResendVerificationDto } from './dtos/auth.dto';
import { ForgotPasswordDto } from './dtos/auth.dto';
import { CompanyDto } from './dtos/auth.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(authenticateRequest: AuthLoginDto): Promise<{
        user: {
            email: string;
            FullName: string;
            designation: string;
            profilePicture: string;
            isOrganization: boolean;
            isFormFillUp: boolean;
            organizationName?: string;
            isEmailVerified: boolean;
            verificationCode: string;
            resetPasswordToken: string;
            forgotPasswordTokenMatch: boolean;
            resetPasswordTokenExpiry: Date;
            courseProgress: import("../courses/entities/course-progress.entity").CourseProgress[];
            savedCourses: import("../courses/entities/course.entity").Course[];
            courses: import("../courses/entities/course.entity").Course[];
            chapterProgress: import("../courses/entities/chapter-progress.entity").ChapterProgress[];
            quizzes: import("../courses/entities/quiz.entity").Quiz[];
            enrollments: import("../courses/entities/enrollment.entity").Enrollment[];
            posts: import("../post/entities/post.entities").Post[];
            likedPosts: import("../post/entities/post.entities").Post[];
            sharedPosts: import("../post/entities/post.entities").Post[];
            organizationDetails: import("../../organiser/entities/organiser.entities").Organization;
            profile: import("../profile/entities/profile.entity").Profile;
            notifications: import("../notification/entities/notification.entity").Notification[];
            sentConnections: import("../profile/entities/profile.connection.entity").ProfileConnection[];
            receivedConnections: import("../profile/entities/profile.connection.entity").ProfileConnection[];
            id: number;
            created_at: Date;
            updated_at: Date;
        };
        organization: import("../../organiser/entities/organiser.entities").Organization | null;
        token: string;
    }>;
    register(signUpUserDto: RegisterDto): Promise<{
        message: string;
        data: {
            user: {
                isOrganization: boolean;
                organizationName: string | undefined;
                email: string;
                password: string;
                confirmPassword: string;
                FullName?: undefined;
                designation?: undefined;
            } | {
                isOrganization: boolean;
                FullName: string;
                email: string;
                password: string;
                confirmPassword: string;
                designation: string | undefined;
                organizationName?: undefined;
            };
        };
    }>;
    verifyEmail(params: verifyEmaildDto): Promise<{
        message: string;
    }>;
    forgotPassword(dto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resendVerification(dto: ResendVerificationDto): Promise<{
        message: any;
    }>;
    getUsers(req: any): Promise<any[]>;
    createCompany(companyDto: CompanyDto, req: any): Promise<{
        message: string;
        data: import("./entities/company.entity").Company;
    }>;
    subscribeEmail(body: {
        email: string;
    }): Promise<{
        message: any;
    }>;
}
