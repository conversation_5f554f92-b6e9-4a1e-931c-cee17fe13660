import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Request,
  HttpStatus,
  Patch,
  Query,
  Req,
} from '@nestjs/common';
import { ProjectService } from './project.service';
import { CreateProjectDto, UpdateProjectDto } from './dtos/project.dto';
import { CreateDonationDto } from './dtos/donation.dto';
import { CreateVolunteerApplicationDto } from './dtos/volunteer-app.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
} from '@nestjs/swagger';
import { ApplicationStatus } from './entities/volunteer-application.entity';

@ApiTags('Projects')
@Controller('projects')
export class ProjectController {
  constructor(private readonly projectService: ProjectService) {}

  @ApiTags('create-project')
  @Post('create-project')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new project' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Project created successfully',
  })
  async createProject(
    @Body() createProjectDto: CreateProjectDto,
    @Request() req,
  ) {
    return this.projectService.create(createProjectDto, req.user.id);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all projects' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return all projects',
  })
  async findAll(
    @Query('category') category?: string,
    @Query('location') location?: string,
  ) {
    return this.projectService.findAll(category, location);
  }

  @ApiTags('Projects')
  @Get('organization/my-projects')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all projects created by the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return all projects by user',
  })
  async getMyProjects(@Request() req) {
    return this.projectService.getProjectsByOrganization(req.user.id);
  }

  @ApiTags('organizationProjects')
  @Get('organizationProjects')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get projects created by the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns projects created by the logged-in user',
  })
  async getOrganizationProjects(@Request() req) {
    console.log('Decoded user:', req.user);
    return this.projectService.getOrganizationProjects(req.user.id);
  }

  @Get('organization/:organizationUserId')
  async getProjectsByOrganizationId(
    @Param('organizationUserId') organizationUserId: string,
  ) {
    const projects =
      await this.projectService.getProjectsByOrganization(organizationUserId);
    return {
      statusCode: HttpStatus.OK,
      message: 'success',
      data: [...projects],
    };
  }

  @ApiTags('Projects')
  @Get(':id')
  @ApiOperation({ summary: 'Get a project by ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return the project',
  })
  async findOne(@Param('id') id: string) {
    return this.projectService.findOne(id);
  }

  @ApiTags('Projects')
  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a project' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Project updated successfully',
  })
  async update(
    @Param('id') id: string,
    @Body() updateProjectDto: UpdateProjectDto,
    @Request() req,
  ) {
    return this.projectService.update(id, updateProjectDto, req.user.id);
  }

  @ApiTags('Projects')
  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a project' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Project deleted successfully',
  })
  async remove(@Param('id') id: string, @Request() req) {
    return this.projectService.remove(id, req.user.id);
  }

  @Patch(':id/status/close')
  @ApiTags('Projects Close')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Close a project (only for project creator)' })
  @ApiParam({ name: 'id', description: 'ID of the project to close' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Project closed successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Project not found',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Not authorized to close this project',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Project is already closed',
  })
  async closeProject(@Param('id') id: string, @Request() req) {
    return this.projectService.closeProject(id, req.user.id);
  }

  // DONATION ENDPOINTS
  @ApiTags('Donations')
  @Post('donations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Make a donation to a project' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Donation processed successfully',
  })
  async createDonation(
    @Body() createDonationDto: CreateDonationDto,
    @Request() req,
  ) {
    return this.projectService.createDonation(createDonationDto, req.user.id);
  }

  @ApiTags('Donations')
  @Get('donations/my-donations')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all donations made by the authenticated user' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return all donations by user',
  })
  async getMyDonations(@Request() req) {
    return this.projectService.getDonationsByUser(req.user.id);
  }

  @ApiTags('Donations')
  @Get('donations/project/:projectId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all donations for a specific project' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return all donations for the project',
  })
  async getProjectDonations(@Param('projectId') projectId: string) {
    return this.projectService.getDonationsByProject(projectId);
  }

  @ApiTags('Donations')
  @Get('donations/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a single donation by ID' })
  @ApiParam({ name: 'id', description: 'ID of the donation' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return the donation',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Donation not found',
  })
  async getDonationById(@Param('id') donationId: string) {
    return this.projectService.getDonationsById(donationId);
  }

  // VOLUNTEER APPLICATION ENDPOINTS

  @Get('volunteer-projects/positions')
  @ApiOperation({ summary: 'Get all volunteer projects with their positions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns all volunteer projects with positions',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
  })
  async getVolunteerProjectsWithPositions() {
    return this.projectService.getVolunteerProjectWithPositions();
  }

  @Get(':projectId/positions/:positionId')
  @ApiOperation({
    summary: 'Get position details by project ID and position ID',
  })
  @ApiParam({ name: 'projectId', description: 'ID of the project' })
  @ApiParam({ name: 'positionId', description: 'ID of the position' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Returns position details',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Position not found in this project',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Position is not from a volunteer project',
  })
  async getVolunteerPosition(
    @Param('projectId') projectId: string,
    @Param('positionId') positionId: string,
  ) {
    return this.projectService.getVolunteerProjectPositionsById(
      projectId,
      positionId,
    );
  }

  @ApiTags('Volunteer Applications')
  @Post(':projectId/positions/:positionId/apply')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Apply as a volunteer for a project position' })
  @ApiParam({ name: 'projectId', description: 'ID of the project' })
  @ApiParam({ name: 'positionId', description: 'ID of the position' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Application submitted successfully.',
  })
  async createApplication(
    @Param('projectId') projectId: string,
    @Param('positionId') positionId: string,
    @Body() createApplicationDto: CreateVolunteerApplicationDto,
    @Request() req,
  ) {
    return this.projectService.createApplication(
      projectId,
      positionId,
      createApplicationDto,
      req.user.id,
    );
  }

  @Get('project/:projectId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all applications for a project' })
  async getAllApplicationAgainstProject(@Param('projectId') projectId: string) {
    return this.projectService.getAllApplicationAgainstProject(projectId);
  }

  @ApiTags('Volunteer Applications')
  @Get('volunteer-applications/my-applications')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all applications made by the authenticated user',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return all applications by user',
  })
  async getMyApplications(@Request() req) {
    return this.projectService.getApplicationsByUser(req.user.id);
  }

  @ApiTags('Volunteer Applications')
  @Get('volunteer-applications/project/:projectId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get all applications for a specific project',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Return all applications for the project',
  })
  async getProjectApplications(@Param('projectId') projectId: string) {
    return this.projectService.getApplicationsByProject(projectId);
  }

  @ApiTags('Volunteer Applications')
  @Put('volunteer-applications/:id/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update application status (for organization)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Application status updated successfully',
  })
  async updateApplicationStatus(
    @Param('id') id: string,
    @Body('status') status: ApplicationStatus,
    @Request() req,
  ) {
    return this.projectService.updateApplicationStatus(id, status, req.user.id);
  }

  @Get('donations/organization')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async getOrganizationDonations(@Request() req) {
    return this.projectService.getOrganizationDonations(req.user.id);
  }
}
