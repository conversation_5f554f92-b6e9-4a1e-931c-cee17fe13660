import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Organization } from './entities/organiser.entities';
import { OrganiserController } from './organiser.controller';
import { organiserService } from './organiser.service';
import { User } from 'src/modules/auth/entities/user.entity';
import { OrganizationFollow } from './entities/organization.follow.entities';
import { Project } from 'src/modules/project/entities/project.entity';
@Module({
  imports: [TypeOrmModule.forFeature([Organization, User, OrganizationFollow,Project])],
  controllers: [OrganiserController],
  providers: [organiserService],
  exports: [organiserService],
})
export class OrganiserModule {}
