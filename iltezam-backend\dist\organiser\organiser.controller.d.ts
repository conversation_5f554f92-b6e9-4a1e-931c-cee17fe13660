import { organiserService } from './organiser.service';
import { CreateOrganizationDto } from './dto/organiser.dto';
export declare class OrganiserController {
    private readonly orgService;
    constructor(orgService: organiserService);
    createOrganization(createUserDto: CreateOrganizationDto, req: any): Promise<import("./entities/organiser.entities").Organization | {
        message: string;
    }>;
    getOrganization(req: any): Promise<{
        statusCode: number;
        message: string;
        data: {
            id: number;
            created_at: Date;
            updated_at: Date;
            email: string;
            organizationName: string;
            organizationNameArabic: string;
            country: string;
            website: string;
            licenseFilePath: string;
            contactName: string;
            contactPhone: string;
            contactTitle: string;
            contactEmail: string;
            goodGovernance: boolean;
            transparencyReporting: boolean;
            sustainableFunding: boolean;
            impactMeasurement: string;
            shortBio: string;
            organizationImage: string;
            organizationTags: string[];
            status: boolean;
            user: {
                id: number;
                created_at: Date;
                updated_at: Date;
                email: string;
                FullName: string;
                designation: string;
                profilePicture: string;
                isOrganization: boolean;
                organizationName: string | undefined;
                isEmailVerified: boolean;
            };
            followers: {
                id: number;
                created_at: Date;
                updated_at: Date;
                followedAt: Date;
                status: boolean;
                follower: {
                    email: string;
                    profilePicture: string;
                    isOrganization: boolean;
                    organizationName: string | undefined;
                };
            }[];
        }[];
    }>;
    getOrganizationProfile(id: number, req: any): Promise<{
        id: number;
        email: string;
        organizationName: string;
        organizationNameArabic: string;
        country: string;
        website: string;
        licenseFilePath: string;
        contactName: string;
        contactPhone: string;
        contactTitle: string;
        contactEmail: string;
        goodGovernance: boolean;
        transparencyReporting: boolean;
        sustainableFunding: boolean;
        impactMeasurement: string;
        shortBio: string;
        organizationImage: string;
        organizationTags: string[];
        user: {
            id: number;
            name: string | undefined;
            email: string;
        };
        followers: {
            id: number;
            email: string;
            fullName: string | null;
            status: boolean;
        }[];
        status: boolean;
    }>;
    followOrganization(id: number, req: any): Promise<{
        statusCode: number;
        message: string;
        data: {
            status: boolean;
            follow: import("./entities/organization.follow.entities").OrganizationFollow;
        };
    }>;
    getProjectCategoryByOrgAndProject(organizationId: number, projectId: number): Promise<{
        organizationId: number;
        projectId: any;
        projectName: any;
        projectCategory: any;
        status: any;
        images: any;
        fundRaisingGoal: any;
        fundsCollected: any;
        location: any;
        startDate: any;
        finishDate: any;
        projectDescription: any;
        thumbnailImage: any;
        totalClicks: any;
        totalFilledPositions: any;
        positions: any;
        organization: {
            id: any;
            organizationName: any;
        };
    }>;
}
