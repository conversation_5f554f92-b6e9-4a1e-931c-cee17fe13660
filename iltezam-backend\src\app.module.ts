import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './core/database/database.module';
import { ConfigModule } from '@nestjs/config';
import databaseConfig from './config/database.config';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { TransformInterceptor } from './shared/interceptors/transform.interceptor';
import { AuthModule } from './modules/auth/auth.module';
import { JwtAuthGuard } from './modules/auth/guards/jwt-auth.guard';
import { OrganiserController } from './organiser/organiser.controller';
import { OrganiserModule } from './organiser/organiser.module';
import { organiserService } from './organiser/organiser.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './modules/auth/entities/user.entity';
import { Organization } from './organiser/entities/organiser.entities';
import { PostModule } from './modules/post/post.module';
import { UploaddModule } from './modules/uploadd/uploadd.module';
import s3config from './core/database/s3.config';
import { Project } from './modules/project/entities/project.entity';
import { ProjectModule } from './modules/project/project.module';
import { Course } from './modules/courses/entities/course.entity';
import { CourseModule } from './modules/courses/course.module';
import { Profile } from './modules/profile/entities/profile.entity';
import { ProfileModule } from './modules/profile/profile.module';
import { NotificationModule } from './modules/notification/notification.module';
import { LiveChatModule } from './modules/live-chat/live-chat.module';
import { CodeOfEthics } from './modules/codeOfEthics/Entity/codeOfEthics.entity';
import { CodeOfEthicsModule } from './modules/codeOfEthics/codeOfEthics.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Organization,
      Project,
      Course,
      Profile,
      CodeOfEthics,
    ]),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [databaseConfig, s3config],
    }),
    DatabaseModule,
    AuthModule,
    OrganiserModule,
    PostModule,
    UploaddModule,
    ProjectModule,
    CourseModule,
    ProfileModule,
    NotificationModule,
    LiveChatModule,
    CodeOfEthicsModule,
  ],
  controllers: [AppController, OrganiserController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
  ],
})
export class AppModule {}
