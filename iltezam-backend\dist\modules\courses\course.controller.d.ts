import { CourseService } from './course.service';
import { CreateCourseDto, UpdateCourseDto, QuizAttemptDto } from './dtos/create.course.dto';
import { User } from 'src/modules/auth/entities/user.entity';
import { Chapter } from './entities/chapter.entity';
import { Repository } from 'typeorm';
export declare class CourseController {
    private readonly courseService;
    private readonly userRepo;
    private readonly chapterRepo;
    constructor(courseService: CourseService, userRepo: Repository<User>, chapterRepo: Repository<Chapter>);
    createCourse(createCourseDto: CreateCourseDto, req: any): Promise<{
        message: string;
        course: {
            message: string;
            course: any;
        };
    }>;
    CourseById(id: string): Promise<{
        statusCode: number;
        message: string;
        data: any;
    }>;
    findAllCourses(req: any, language?: string, tags?: string, organizationName?: string, courseName?: string, searchQuery?: string): Promise<any[]>;
    getCourseById(courseId: string, req: any): Promise<any>;
    updateProgressAndReturnCourse(req: any, body: {
        courseId: number;
        topicId: number;
        chapterId: number;
        progressInSeconds: number;
        duration: number;
    }): Promise<any>;
    getUserProgress(req: any): Promise<{
        courseId: number;
        courseName: string;
        isEnrolled: boolean;
        totalChapters: number;
        completedChapters: number;
        progressPercentage: number;
        lastWatchedChapter: {
            id: number;
            title: string;
        } | null;
        thumbnailUrl: string;
        whatYouLearn: string;
        createdBy: {
            id: number;
            profilePicture: string;
            organizationName: string | undefined;
        };
    }[]>;
    getCompletedCourses(userId: number): Promise<{
        courseId: number;
        courseTitle: string;
        thumbnailUrl: string;
        whatTheyLearn: string;
        userFullName: string;
        organizationName?: string;
        completionDate: Date;
        isEnrolled: boolean;
        totalChapters: number;
        completedChapters: number;
        progressPercentage: number;
        createdBy: {
            id: number;
            profilePicture?: string;
            organizationName?: string;
        };
    }[]>;
    findByStatus(status: 'active' | 'closed', req: any): Promise<any[]>;
    updateCourse(id: string, body: UpdateCourseDto): Promise<import("./entities/course.entity").Course>;
    updateTopic(id: string, body: {
        topicTitle?: string;
        numberOfChapters?: number;
    }): Promise<import("./entities/topic.entity").Topic>;
    updateChapter(id: string, body: {
        chapterTitle?: string;
        description?: string;
        mediaUrl?: string;
        prerequisiteChapterIds?: string[];
    }): Promise<Chapter>;
    attemptQuiz(body: QuizAttemptDto, req: any): Promise<{
        quizId: number;
        user: {
            id: number;
            name: string;
            email: string;
            designation: string;
        };
        questions: {
            questionText: string;
            options: string[];
            correctOptionIndex: number;
            userSelectedIndex: number;
            isCorrect: boolean;
        }[];
        score: number;
        total: number;
        attemptNumber: number;
    }>;
    getQuizById(quizId: number): Promise<any>;
    getLatestAttempt(quizId: number, req: any): Promise<{
        quizId: number;
        user: {
            id: number;
            name: string;
            email: string;
        };
        questions: {
            questionText: string;
            options: string[];
            correctOptionIndex: number;
            userSelectedIndex: number;
            isCorrect: boolean;
        }[];
        score: number;
        total: number;
        attemptNumber: number;
        attemptedAt: Date;
    }>;
    toggleSaveCourse(courseId: string, userId: number): Promise<{
        success: boolean;
        message: string;
    }>;
    getSavedCourses(userId: string): Promise<{
        user: {
            userId: number;
            fullName: string;
            email: string;
        };
        course: {
            id: number;
            courseName: string;
            language: string;
            whatTheyLearn: string;
            tags: string[];
            thumbnailUrl: string;
            status: "closed" | "active";
            estimatedDuration: string;
            previewVideoUrl: string;
            isEnrolled: boolean;
            createdBy: {
                id: number;
                organizationName: string | undefined;
                profilePicture: string;
            };
            topics: {
                topicId: number;
                topicTitle: string;
                chapters: {
                    chapterId: number;
                    chapterTitle: string;
                    description: string;
                    mediaUrl: string;
                }[];
            }[];
        };
    }[]>;
    enrollUser(userId: number, courseId: number): Promise<any>;
    getEnrolledUsers(courseId: string): Promise<{
        courseId: number;
        userId: number;
        fullName: string;
        email: string;
        enrolledAt: Date;
    }[]>;
    getEnrollmentCount(courseId: string): Promise<{
        courseId: number;
        enrollmentCount: number;
    }>;
    assignCertificateToUser(userId: number, courseId: number, pdfUrl: string): Promise<{
        message: string;
        certificate: import("./entities/certificate.entity").Certificate;
    }>;
}
