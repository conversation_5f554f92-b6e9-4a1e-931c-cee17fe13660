"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const course_entity_1 = require("./entities/course.entity");
const topic_entity_1 = require("./entities/topic.entity");
const chapter_entity_1 = require("./entities/chapter.entity");
const course_service_1 = require("./course.service");
const course_controller_1 = require("./course.controller");
const user_entity_1 = require("../auth/entities/user.entity");
const course_progress_entity_1 = require("./entities/course-progress.entity");
const quiz_entity_1 = require("./entities/quiz.entity");
const quiz_attempt_entity_1 = require("./entities/quiz-attempt.entity");
const certificate_entity_1 = require("./entities/certificate.entity");
const question_entity_1 = require("./entities/question.entity");
const savedCourse_entity_1 = require("./entities/savedCourse.entity");
const enrollment_entity_1 = require("./entities/enrollment.entity");
const organiser_entities_1 = require("../../organiser/entities/organiser.entities");
const chapter_progress_entity_1 = require("./entities/chapter-progress.entity");
let CourseModule = class CourseModule {
};
exports.CourseModule = CourseModule;
exports.CourseModule = CourseModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                course_entity_1.Course,
                topic_entity_1.Topic,
                chapter_entity_1.Chapter,
                course_progress_entity_1.CourseProgress,
                chapter_progress_entity_1.ChapterProgress,
                certificate_entity_1.Certificate,
                user_entity_1.User,
                quiz_entity_1.Quiz,
                quiz_attempt_entity_1.QuizAttempt,
                question_entity_1.Question,
                savedCourse_entity_1.SavedCourse,
                enrollment_entity_1.Enrollment,
                organiser_entities_1.Organization,
            ]),
        ],
        providers: [course_service_1.CourseService],
        controllers: [course_controller_1.CourseController],
    })
], CourseModule);
//# sourceMappingURL=course.module.js.map