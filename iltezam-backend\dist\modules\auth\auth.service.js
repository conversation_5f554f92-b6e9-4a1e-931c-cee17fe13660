"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const user_entity_1 = require("./entities/user.entity");
const typeorm_1 = require("typeorm");
const typeorm_2 = require("@nestjs/typeorm");
const bcrypt = require("bcryptjs");
const sendemail_1 = require("../../core/utils/sendemail");
const CustomError_1 = require("../../core/utils/CustomError");
const profile_entity_1 = require("../profile/entities/profile.entity");
const profile_connection_entity_1 = require("../profile/entities/profile.connection.entity");
const company_entity_1 = require("./entities/company.entity");
const subscribe_entity_1 = require("./entities/subscribe.entity");
let AuthService = class AuthService {
    jwtService;
    userRepository;
    profileRepository;
    companyRepository;
    mailService;
    subscribedEmailRepository;
    transporter;
    constructor(jwtService, userRepository, profileRepository, companyRepository, mailService, subscribedEmailRepository) {
        this.jwtService = jwtService;
        this.userRepository = userRepository;
        this.profileRepository = profileRepository;
        this.companyRepository = companyRepository;
        this.mailService = mailService;
        this.subscribedEmailRepository = subscribedEmailRepository;
    }
    generateVerificationCode() {
        return Math.random().toString(36).substring(2, 6).toUpperCase();
    }
    async loginUser(payload) {
        try {
            const user = await this.userRepository.findOne({
                where: { email: payload.email },
            });
            if (!user) {
                throw new common_1.HttpException('Account does not exist', 404);
            }
            const isPasswordValid = await bcrypt.compare(payload.password, user.password);
            if (!isPasswordValid) {
                throw new common_1.HttpException('Incorrect password', 401);
            }
            if (!user.isEmailVerified) {
                const newCode = this.generateVerificationCode();
                user.verificationCode = newCode;
                await this.userRepository.save(user);
                await this.mailService.sendVerificationEmail(user.email, newCode);
                throw new common_1.HttpException('Email is not verified. Please verify your email.', 400);
            }
            const token = await this.signToken(user);
            const { password, ...userWithoutPassword } = user;
            return {
                user: userWithoutPassword,
                token,
            };
        }
        catch (error) {
            if (error instanceof common_1.HttpException) {
                throw error;
            }
            throw new common_1.HttpException('Internal server error', 500);
        }
    }
    signToken(checkUserExist) {
        const jwtPayload = {
            id: checkUserExist.id,
            email: checkUserExist.email,
        };
        return this.jwtService.sign(jwtPayload);
    }
    async registerUser(payload) {
        try {
            const { email, password, confirmPassword, isOrganization, organizationName, FullName, designation, } = payload;
            if (!email || !password || !confirmPassword) {
                throw new common_1.HttpException('Email, password and confirm password are required', common_1.HttpStatus.BAD_REQUEST);
            }
            if (password !== confirmPassword) {
                throw new common_1.HttpException('Passwords do not match', common_1.HttpStatus.BAD_REQUEST);
            }
            const existingUser = await this.userRepository.findOne({
                where: { email },
            });
            if (existingUser) {
                throw new common_1.HttpException('User email already exists', common_1.HttpStatus.BAD_REQUEST);
            }
            if (isOrganization) {
                if (!organizationName || organizationName.trim() === '') {
                    throw new common_1.HttpException('Organization name is required for organizers', common_1.HttpStatus.BAD_REQUEST);
                }
            }
            else {
                if (!FullName || FullName.trim() === '') {
                    throw new common_1.HttpException('Full name is required for individuals', common_1.HttpStatus.BAD_REQUEST);
                }
                if (!designation || designation.trim() === '') {
                    throw new common_1.HttpException('Designation is required for individuals', common_1.HttpStatus.BAD_REQUEST);
                }
            }
            const verificationCode = this.generateVerificationCode();
            console.log('Verification Code:', verificationCode);
            const userData = {
                email: email.toLowerCase(),
                password: await bcrypt.hash(password, 8),
                isOrganization,
                verificationCode,
                FullName: FullName,
            };
            console.log('userData:', userData);
            if (isOrganization) {
                userData.organizationName = organizationName;
            }
            else {
                userData.designation = designation;
            }
            const newUser = this.userRepository.create(userData);
            await this.userRepository.save(newUser);
            const newProfile = this.profileRepository.create({
                fullName: FullName,
                organizationName,
                email: email.toLowerCase(),
                user: newUser,
                bio: '',
                profilePicture: '',
                location: '',
                phone: '',
            });
            console.log('newProfile:', newProfile);
            await this.profileRepository.save(newProfile);
            newUser.profile = newProfile;
            await this.userRepository.save(newUser);
            await this.mailService.sendVerificationEmail(email, verificationCode);
            console.log('Verification email sent to:', email);
            const responseData = isOrganization
                ? {
                    isOrganization: true,
                    organizationName,
                    email,
                    password,
                    confirmPassword,
                }
                : {
                    isOrganization: false,
                    FullName,
                    email,
                    password,
                    confirmPassword,
                    designation,
                };
            const dataaa = {
                user: responseData,
            };
            console.log('Response Data:', responseData);
            return {
                data: dataaa,
                message: 'Registered successfully.',
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async forgotPassword(email) {
        const user = await this.userRepository.findOne({ where: { email } });
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        const resetToken = this.generateVerificationCode();
        const expiry = new Date(Date.now() + 1000 * 60 * 30);
        console.log('Reset Token:', resetToken);
        user.resetPasswordToken = resetToken;
        user.resetPasswordTokenExpiry = expiry;
        await this.userRepository.save(user);
        await this.mailService.sendResetPasswordEmail(email, resetToken);
        return { message: 'Reset password email sent', data: resetToken };
    }
    async verifyResetCode(email, code) {
        const user = await this.userRepository.findOne({
            where: {
                email,
                resetPasswordToken: code,
            },
        });
        if (!user) {
            throw new common_1.BadRequestException('Invalid or expired reset code');
        }
        user.forgotPasswordTokenMatch = true;
        await this.userRepository.save(user);
    }
    async resetPassword(email, newPassword) {
        const user = await this.userRepository.findOne({ where: { email } });
        if (!user || !user.forgotPasswordTokenMatch) {
            throw new common_1.BadRequestException('Reset code not verified');
        }
        user.password = await bcrypt.hash(newPassword, 10);
        user.resetPasswordToken = '';
        user.forgotPasswordTokenMatch = false;
        await this.userRepository.save(user);
    }
    async verifyEmail(payload) {
        const { email, code } = payload;
        console.log('Verifying email:', email, 'with code:', code);
        const user = await this.userRepository.findOne({
            where: {
                email,
                verificationCode: code,
            },
        });
        console.log('User found:', user);
        if (!user) {
            throw new CustomError_1.CustomError('Invalid verification code or email', 400);
        }
        if (user.isEmailVerified) {
            throw new CustomError_1.CustomError('Email is already verified', 400);
        }
        user.isEmailVerified = true;
        user.verificationCode = '';
        await this.userRepository.save(user);
        return { message: 'Email verified successfully' };
    }
    async resendVerificationEmail(email) {
        const user = await this.userRepository.findOne({ where: { email } });
        if (!user) {
            throw new CustomError_1.CustomError('User not found', 404);
        }
        if (user.isEmailVerified) {
            throw new CustomError_1.CustomError('Email already verified', 400);
        }
        user.verificationCode = this.generateVerificationCode();
        await this.userRepository.save(user);
        console.log('Verification code:', user.verificationCode);
        await this.mailService.sendVerificationEmail(user.email, user.verificationCode);
        return { message: 'Verification email sent successfully' };
    }
    async sendVerificationEmail(email, verificationCode) {
        await this.transporter.sendMail({
            from: `ELTEZAM <${process.env.EMAIL}>`,
            to: email,
            subject: 'Your ELTEZAM Verification Code',
            html: `
        <p>Thank you for registering with ELTEZAM!</p>
        <p>Your verification code is:</p>
        <h2>${verificationCode}</h2>
        <p>Please enter this code in the app to verify your email address.</p>
        <p>If you didn't create an account, please ignore this email.</p>
        <br>
        <p>Best,<br>The ELTEZAM Team</p> 
      `,
        });
    }
    async getAllUsers(currentUserId, search) {
        try {
            const currentUser = await this.userRepository.findOne({
                where: { id: Number(currentUserId) },
                relations: ['profile'],
            });
            if (!currentUser || !currentUser.profile) {
                throw new common_1.HttpException('Current user or profile not found', common_1.HttpStatus.NOT_FOUND);
            }
            const query = this.userRepository
                .createQueryBuilder('user')
                .leftJoinAndSelect('user.profile', 'profile')
                .where('user.id != :currentUserId', {
                currentUserId: Number(currentUserId),
            })
                .andWhere('user.isOrganization = :isOrganization', {
                isOrganization: false,
            });
            if (search) {
                query.andWhere('user.FullName ILIKE :search', {
                    search: `%${search}%`,
                });
            }
            const users = await query.getMany();
            const connections = await this.profileRepository.manager.find(profile_connection_entity_1.ProfileConnection, {
                where: [
                    { requester: { id: Number(currentUserId) } },
                    { receiver: { id: Number(currentUserId) } },
                ],
                relations: ['requester', 'receiver'],
            });
            return users.map((user) => {
                const { password, ...userInfo } = user;
                const connection = connections.find((conn) => (conn.requester.id === Number(currentUserId) &&
                    conn.receiver.id === user.id) ||
                    (conn.receiver.id === Number(currentUserId) &&
                        conn.requester.id === user.id));
                userInfo.connectionStatus = connection ? connection.status : 'none';
                return userInfo;
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createCompany(companyDto) {
        const existingCompany = await this.companyRepository.findOne({
            where: { email: companyDto.email },
        });
        if (existingCompany) {
            throw new common_1.BadRequestException('Company with this email already exists');
        }
        const newCompany = this.companyRepository.create(companyDto);
        const savedCompany = await this.companyRepository.save(newCompany);
        return {
            message: 'Company created successfully',
            data: savedCompany,
        };
    }
    async emailSubscribe(email) {
        try {
            console.log('Starting email subscription process for:', email);
            const existingSubscription = await this.subscribedEmailRepository.findOne({
                where: { email },
            });
            if (existingSubscription) {
                console.log('Email already subscribed:', email);
                throw new common_1.BadRequestException('Email already subscribed');
            }
            const newSubscription = this.subscribedEmailRepository.create({
                email: email.toLowerCase(),
            });
            await this.subscribedEmailRepository.save(newSubscription);
            console.log('Subscription saved to database:', newSubscription.email);
            console.log('Attempting to send subscription email...');
            await this.mailService.sendSubscriptionEmail(newSubscription.email);
            console.log('Subscription email sent successfully');
            return {
                message: 'Email subscribed successfully',
                data: { email: newSubscription.email },
            };
        }
        catch (error) {
            console.error('Error in emailSubscribe:', error);
            throw error;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_2.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_2.InjectRepository)(profile_entity_1.Profile)),
    __param(3, (0, typeorm_2.InjectRepository)(company_entity_1.Company)),
    __param(5, (0, typeorm_2.InjectRepository)(subscribe_entity_1.SubscribedEmail)),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        typeorm_1.Repository,
        typeorm_1.Repository,
        typeorm_1.Repository,
        sendemail_1.MailService,
        typeorm_1.Repository])
], AuthService);
//# sourceMappingURL=auth.service.js.map