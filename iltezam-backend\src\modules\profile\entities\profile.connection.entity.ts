import {
  En<PERSON>ty,
  ManyToOne,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';
import { Profile } from './profile.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';

@Entity()
export class ProfileConnection extends BaseEntityClass {
  @ManyToOne(() => User, (user) => user.sentConnections)
  requester: User;
  @ManyToOne(() => User, (user) => user.receivedConnections)
  receiver: User;
  @Column({ default: 'pending' })
  status: 'pending' | 'accepted' | 'rejected' | 'disconnected';
  @CreateDateColumn()
  createdAt: Date;
}
