"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationFollow = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../modules/auth/entities/user.entity");
const organiser_entities_1 = require("./organiser.entities");
const base_entity_1 = require("../../modules/auth/entities/base.entity");
let OrganizationFollow = class OrganizationFollow extends base_entity_1.BaseEntityClass {
    follower;
    organization;
    followedAt;
    status;
};
exports.OrganizationFollow = OrganizationFollow;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: true }),
    __metadata("design:type", user_entity_1.User)
], OrganizationFollow.prototype, "follower", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organiser_entities_1.Organization, (org) => org.followers, { eager: true }),
    __metadata("design:type", organiser_entities_1.Organization)
], OrganizationFollow.prototype, "organization", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], OrganizationFollow.prototype, "followedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], OrganizationFollow.prototype, "status", void 0);
exports.OrganizationFollow = OrganizationFollow = __decorate([
    (0, typeorm_1.Entity)()
], OrganizationFollow);
//# sourceMappingURL=organization.follow.entities.js.map