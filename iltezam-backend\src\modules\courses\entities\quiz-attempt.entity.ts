import { <PERSON><PERSON>ty, ManyToOne, Column } from 'typeorm';
import { User } from 'src/modules/auth/entities/user.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { Quiz } from './quiz.entity';
import { IsNotEmpty } from 'class-validator';

@Entity()
export class QuizAttempt extends BaseEntityClass {
  @ManyToOne(() => User)
  user: User;

  @ManyToOne(() => Quiz)
  quiz: Quiz;

  @Column()
  score: number;

  @Column()
  attemptNumber: number;

  @Column('int', { array: true, nullable: true })
  @IsNotEmpty()
  answers: number[];

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  attemptedAt: Date;

  @ManyToOne(() => User, { eager: false })
  users: User;
}
