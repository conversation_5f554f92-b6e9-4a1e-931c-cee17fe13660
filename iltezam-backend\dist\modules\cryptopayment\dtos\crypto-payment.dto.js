"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCryptoPaymentDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCryptoPaymentDto {
    amount;
    currency;
    buyerEmail;
    buyerName;
    itemName;
    itemNumber;
    invoice;
    custom;
    ipnUrl;
    successUrl;
    cancelUrl;
}
exports.CreateCryptoPaymentDto = CreateCryptoPaymentDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Donation amount in USD',
        example: 100,
        minimum: 0.01
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0.01),
    __metadata("design:type", Number)
], CreateCryptoPaymentDto.prototype, "amount", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cryptocurrency to receive payment in',
        example: 'BTC',
        enum: ['BTC', 'ETH', 'LTC', 'BCH', 'XRP', 'ADA', 'DOT', 'USDT', 'USDC']
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "currency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Buyer email address',
        example: '<EMAIL>'
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "buyerEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Buyer name',
        example: 'John Doe',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "buyerName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Item/donation description',
        example: 'Donation for Project ABC',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "itemName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Item number for reference',
        example: 'DON-001',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "itemNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Invoice number',
        example: 'INV-2024-001',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "invoice", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Custom field for additional data',
        example: 'project_id:123',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "custom", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'IPN (Instant Payment Notification) URL',
        example: 'https://yoursite.com/ipn',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "ipnUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success redirect URL',
        example: 'https://yoursite.com/success',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "successUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Cancel redirect URL',
        example: 'https://yoursite.com/cancel',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCryptoPaymentDto.prototype, "cancelUrl", void 0);
//# sourceMappingURL=crypto-payment.dto.js.map