import { BaseEntityClass } from '../../auth/entities/base.entity';
import { User } from '../../auth/entities/user.entity';
import { Project } from './project.entity';
import { Position } from './position.entity';
export declare enum ApplicationStatus {
    PENDING = "pending",
    APPROVED = "approved",
    REJECTED = "rejected",
    WITHDRAWN = "withdrawn"
}
export declare class VolunteerApplication extends BaseEntityClass {
    applicant: User;
    project: Project;
    position: Position;
    CV?: string[];
    status: ApplicationStatus;
}
