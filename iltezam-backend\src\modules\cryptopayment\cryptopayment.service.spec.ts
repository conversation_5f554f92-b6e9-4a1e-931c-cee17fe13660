import { Test, TestingModule } from '@nestjs/testing';
import { CryptopaymentService } from './cryptopayment.service';

describe('CryptopaymentService', () => {
  let service: CryptopaymentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CryptopaymentService],
    }).compile();

    service = module.get<CryptopaymentService>(CryptopaymentService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
