export declare class CryptoPaymentResponseDto {
    success: boolean;
    transactionId: string;
    amount: string;
    currency: string;
    address: string;
    confirmsNeeded: number;
    timeout: number;
    checkoutUrl: string;
    statusUrl: string;
    qrcodeUrl: string;
    message: string;
}
export declare class SupportedCurrencyDto {
    code: string;
    name: string;
    rateBtc: string;
    txFee: string;
    status: string;
    confirms: number;
    capabilities: string[];
}
export declare class SupportedCurrenciesResponseDto {
    success: boolean;
    currencies: SupportedCurrencyDto[];
    message: string;
}
export declare class TransactionStatusResponseDto {
    success: boolean;
    transactionId: string;
    status: number;
    statusText: string;
    type: string;
    coin: string;
    amount: string;
    received: string;
    receivedConfirms: number;
    paymentAddress: string;
    timeCreated: Date;
    timeExpires: Date;
    message: string;
}
