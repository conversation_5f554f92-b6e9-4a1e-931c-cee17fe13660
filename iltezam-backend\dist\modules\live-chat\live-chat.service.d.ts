import { Repository } from 'typeorm';
import { Server } from 'socket.io';
import { ChatRoom } from './entities/chat-room.entity';
import { Message } from './entities/message.entity';
import { User } from '../auth/entities/user.entity';
export declare class LiveChatService {
    private readonly chatRoomRepository;
    private readonly messageRepository;
    private readonly userRepository;
    private io;
    constructor(chatRoomRepository: Repository<ChatRoom>, messageRepository: Repository<Message>, userRepository: Repository<User>);
    setIo(io: Server): void;
    createRoom(senderId: number, receiverId: number): Promise<{
        roomId: string;
        senderId: number;
        receiverId: number;
    }>;
    sendMessage(senderId: number, roomId: string, receiverId: number, messageText: string): Promise<{
        success: boolean;
        message: string;
    }>;
    createGroupRoom(creatorId: number, userIds: number[], groupName: string, groupPicture: string): Promise<{
        roomId: string;
        groupName: string;
        groupImage: string;
    }>;
    sendGroupMessage(senderId: number, roomId: string, messageText: string): Promise<{
        success: boolean;
        message: string;
    }>;
    getMessages(roomId: string, userId: number): Promise<Message[]>;
    getInbox(userId: number): Promise<any[]>;
    getRoom(userId: number, partnerId: number): Promise<{
        roomId: string;
    }>;
    getAllGroups(userId: number): Promise<any[]>;
    private getInboxData;
    joinGroup(roomId: string, userId: number): Promise<{
        success: boolean;
        message: string;
        roomId: string;
    }>;
    private setupSocketEvents;
}
