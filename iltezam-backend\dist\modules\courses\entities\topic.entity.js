"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Topic = void 0;
const typeorm_1 = require("typeorm");
const course_entity_1 = require("./course.entity");
const chapter_entity_1 = require("./chapter.entity");
const base_entity_1 = require("../../auth/entities/base.entity");
const class_validator_1 = require("class-validator");
let Topic = class Topic extends base_entity_1.BaseEntityClass {
    topicTitle;
    numberOfChapters;
    courses;
    chapters;
};
exports.Topic = Topic;
__decorate([
    (0, typeorm_1.Column)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], Topic.prototype, "topicTitle", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Number)
], Topic.prototype, "numberOfChapters", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => course_entity_1.Course, (course) => course.topics, { onDelete: 'CASCADE' }),
    __metadata("design:type", course_entity_1.Course)
], Topic.prototype, "courses", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => chapter_entity_1.Chapter, (chapter) => chapter.topics, { cascade: true }),
    __metadata("design:type", Array)
], Topic.prototype, "chapters", void 0);
exports.Topic = Topic = __decorate([
    (0, typeorm_1.Entity)()
], Topic);
//# sourceMappingURL=topic.entity.js.map