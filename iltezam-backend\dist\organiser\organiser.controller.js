"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganiserController = void 0;
const common_1 = require("@nestjs/common");
const common_2 = require("@nestjs/common");
const organiser_service_1 = require("./organiser.service");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const organiser_dto_1 = require("./dto/organiser.dto");
const publicRoute_decorator_1 = require("../shared/decorators/publicRoute.decorator");
let OrganiserController = class OrganiserController {
    orgService;
    constructor(orgService) {
        this.orgService = orgService;
    }
    async createOrganization(createUserDto, req) {
        try {
            const user = req.user;
            return await this.orgService.createOrganiser(createUserDto, user);
        }
        catch (e) {
            if (e.status === 409) {
                return { message: 'User already has an organization' };
            }
            throw new common_2.HttpException(e.message, e.status || 403);
        }
    }
    async getOrganization(req) {
        try {
            const userId = req.user.id;
            const organisers = await this.orgService.getOrganiser(userId);
            return {
                statusCode: 200,
                message: 'success',
                data: organisers,
            };
        }
        catch (e) {
            throw new common_2.HttpException(e.message, 403);
        }
    }
    async getOrganizationProfile(id, req) {
        try {
            return await this.orgService.getOrganizationProfile(+id, req.user.id);
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || 403);
        }
    }
    async followOrganization(id, req) {
        try {
            return await this.orgService.followOrganization(id, req.user);
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || 403);
        }
    }
    async getProjectCategoryByOrgAndProject(organizationId, projectId) {
        try {
            return await this.orgService.getProjectCategoryByOrgAndProject(+organizationId, +projectId);
        }
        catch (e) {
            throw new common_2.HttpException(e.message, e.status || 403);
        }
    }
};
exports.OrganiserController = OrganiserController;
__decorate([
    (0, common_2.Post)('create'),
    (0, common_2.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_2.Body)()),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [organiser_dto_1.CreateOrganizationDto, Object]),
    __metadata("design:returntype", Promise)
], OrganiserController.prototype, "createOrganization", null);
__decorate([
    (0, common_2.Get)('getorganiser'),
    (0, common_2.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], OrganiserController.prototype, "getOrganization", null);
__decorate([
    (0, common_2.Get)('organization/:id'),
    (0, common_2.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_2.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], OrganiserController.prototype, "getOrganizationProfile", null);
__decorate([
    (0, common_2.Post)(':id/follow'),
    (0, common_2.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_2.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], OrganiserController.prototype, "followOrganization", null);
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_2.Get)(':organizationId/project/:projectId'),
    (0, common_2.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_2.Param)('organizationId')),
    __param(1, (0, common_2.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], OrganiserController.prototype, "getProjectCategoryByOrgAndProject", null);
exports.OrganiserController = OrganiserController = __decorate([
    (0, common_1.Controller)('organiser'),
    __metadata("design:paramtypes", [organiser_service_1.organiserService])
], OrganiserController);
//# sourceMappingURL=organiser.controller.js.map