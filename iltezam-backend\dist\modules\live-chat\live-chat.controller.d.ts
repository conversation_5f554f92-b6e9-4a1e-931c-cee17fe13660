import { LiveChatService } from './live-chat.service';
export declare class LiveChatController {
    private readonly liveChatService;
    constructor(liveChatService: LiveChatService);
    createRoom(req: any, body: any): Promise<{
        roomId: string;
        senderId: number;
        receiverId: number;
    }>;
    sendMessage(req: any, body: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getMessages(roomId: string, req: any): Promise<import("./entities/message.entity").Message[]>;
    getInbox(req: any): Promise<any[]>;
    getRoom(req: any, partnerId: string): Promise<{
        roomId: string;
    }>;
    createGroupRoom(req: any, body: any): Promise<{
        roomId: string;
        groupName: string;
        groupImage: string;
    }>;
    sendGroupMessage(req: any, body: any): Promise<{
        success: boolean;
        message: string;
    }>;
    getAllGroups(req: any): Promise<any[]>;
    joinGroup(roomId: string, req: any): Promise<{
        success: boolean;
        message: string;
        roomId: string;
    }>;
}
