
import { <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON>ToMany } from 'typeorm';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { Chapter } from './chapter.entity';
import { Question } from './question.entity';
import { User } from 'src/modules/auth/entities/user.entity';

@Entity()
export class Quiz extends BaseEntityClass {
 @ManyToOne(() => Chapter, chapter => chapter.quizzes)
chapter: Chapter;

@OneToMany(() => Question, question => question.quiz, { cascade: true, eager: true })
questions: Question[];

  @ManyToOne(() => User, (user) => user.quizzes, { onDelete: 'CASCADE' })
  createdBy: User;
}