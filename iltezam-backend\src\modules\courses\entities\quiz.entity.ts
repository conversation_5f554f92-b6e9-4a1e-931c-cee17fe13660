
import { Column, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, OneToMany } from 'typeorm';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { Chapter } from './chapter.entity';
import { Question } from './question.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { QuizAttempt } from './quiz-attempt.entity';

@Entity()
export class Quiz extends BaseEntityClass {
  @ManyToOne(() => Chapter, chapter => chapter.quizzes, { onDelete: 'CASCADE' })
  chapter: Chapter;

  @OneToMany(() => Question, question => question.quiz, { cascade: true, eager: true })
  questions: Question[];

  @ManyToOne(() => User, (user) => user.quizzes, { onDelete: 'CASCADE' })
  createdBy: User;

  @OneToMany(() => QuizAttempt, (attempt) => attempt.quiz, { cascade: true })
  attempts: QuizAttempt[];
}