"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VolunteerApplication = exports.ApplicationStatus = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../auth/entities/base.entity");
const user_entity_1 = require("../../auth/entities/user.entity");
const project_entity_1 = require("./project.entity");
const position_entity_1 = require("./position.entity");
var ApplicationStatus;
(function (ApplicationStatus) {
    ApplicationStatus["PENDING"] = "pending";
    ApplicationStatus["APPROVED"] = "approved";
    ApplicationStatus["REJECTED"] = "rejected";
    ApplicationStatus["WITHDRAWN"] = "withdrawn";
})(ApplicationStatus || (exports.ApplicationStatus = ApplicationStatus = {}));
let VolunteerApplication = class VolunteerApplication extends base_entity_1.BaseEntityClass {
    applicant;
    project;
    position;
    CV;
    status;
};
exports.VolunteerApplication = VolunteerApplication;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'applicant_id' }),
    __metadata("design:type", user_entity_1.User)
], VolunteerApplication.prototype, "applicant", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'project_id' }),
    __metadata("design:type", project_entity_1.Project)
], VolunteerApplication.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => position_entity_1.Position, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'position_id' }),
    __metadata("design:type", position_entity_1.Position)
], VolunteerApplication.prototype, "position", void 0);
__decorate([
    (0, typeorm_1.Column)('simple-array', { nullable: true }),
    __metadata("design:type", Array)
], VolunteerApplication.prototype, "CV", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ApplicationStatus,
        default: ApplicationStatus.PENDING,
    }),
    __metadata("design:type", String)
], VolunteerApplication.prototype, "status", void 0);
exports.VolunteerApplication = VolunteerApplication = __decorate([
    (0, typeorm_1.Entity)({ name: 'volunteer_applications' })
], VolunteerApplication);
//# sourceMappingURL=volunteer-application.entity.js.map