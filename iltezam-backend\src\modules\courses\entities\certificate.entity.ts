
import { <PERSON><PERSON>ty, ManyToOne, Column } from 'typeorm';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { Course } from './course.entity';
@Entity()
export class Certificate extends BaseEntityClass {

    @ManyToOne(() => User)
    user: User;

    @ManyToOne(() => Course)
    course: Course;

    @Column()
    pdfUrl: string;

    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    issuedAt: Date;
}