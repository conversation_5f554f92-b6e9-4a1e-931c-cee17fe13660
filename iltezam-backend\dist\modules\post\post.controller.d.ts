import { PostService } from './post.service';
import { CreateCommentDto, CreatePostDto, LikePostDto } from './dto/post.dto';
export declare class PostController {
    private readonly postService;
    constructor(postService: PostService);
    createPost(createPostDto: CreatePostDto, req: any): Promise<{
        message: any;
        post: any;
    }>;
    editPost(id: number, updatePostDto: CreatePostDto, req: any): Promise<{
        message: any;
        post: any;
    }>;
    deletePost(id: number, req: any): Promise<{
        message: any;
    }>;
    getAllPosts(): Promise<{
        message: any;
        posts: any;
    }>;
    likesPost(createPostDto: LikePostDto, req: any): Promise<{
        statusCode: number;
        message: any;
        data: {
            post: any;
            user: any;
        };
    }>;
    sharePost(sharePostDto: {
        post_id: number;
    }, req: any): Promise<{
        message: any;
        post: any;
        sharedUsers: any;
    }>;
    createComment(createCommentDto: CreateCommentDto, req: any): Promise<{
        statusCode: number;
        message: any;
        data: {
            comment: any;
        };
    }>;
    getPostsByUserId(userId: number): Promise<{
        message: any;
        posts: any;
    }>;
}
