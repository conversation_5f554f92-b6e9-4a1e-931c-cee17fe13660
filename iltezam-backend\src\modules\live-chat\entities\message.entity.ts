import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn } from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { ChatRoom } from './chat-room.entity';

@Entity()
export class Message {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  roomId: string;

  @ManyToOne(() => User)
  sender: User;

  @ManyToOne(() => User)
  receiver: User;

  @Column()
  message: string;

  @Column({ default: false })
  isRead: boolean;

  
 @Column('int', { array: true, default: () => 'ARRAY[]::INTEGER[]' })
readUsers: number[];


  @CreateDateColumn()
  timestamp: Date;
}