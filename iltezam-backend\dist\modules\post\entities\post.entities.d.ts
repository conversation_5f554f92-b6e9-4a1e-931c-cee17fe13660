import { User } from '../../auth/entities/user.entity';
import { BaseEntityClass } from '../../auth/entities/base.entity';
import { Comment } from './comment.entities';
import { Profile } from 'src/modules/profile/entities/profile.entity';
export declare class Post extends BaseEntityClass {
    content: string;
    imageUrl?: string[];
    videoUrl?: string[];
    visibility: 'public' | 'private';
    likes: number;
    dislikes: number;
    shares: number;
    PostImage: string;
    user: User;
    comments: Comment[];
    likedUsers: User[];
    sharedUsers: User[];
    profile: Profile;
}
