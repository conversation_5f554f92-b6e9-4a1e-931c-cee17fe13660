"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VolunteerApplicationResponseDto = exports.CreateVolunteerApplicationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateVolunteerApplicationDto {
    projectId;
    positionId;
    CV;
}
exports.CreateVolunteerApplicationDto = CreateVolunteerApplicationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project ID' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateVolunteerApplicationDto.prototype, "projectId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Position ID' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateVolunteerApplicationDto.prototype, "positionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'CV or any related documents',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateVolunteerApplicationDto.prototype, "CV", void 0);
class VolunteerApplicationResponseDto {
    id;
    projectId;
    projectName;
    positionId;
    positionName;
    field;
    documents;
    status;
    coverLetter;
    experience;
    skills;
    notes;
    createdAt;
}
exports.VolunteerApplicationResponseDto = VolunteerApplicationResponseDto;
//# sourceMappingURL=volunteer-app.dto.js.map