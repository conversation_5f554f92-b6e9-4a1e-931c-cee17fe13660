import { IsNotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateCodeOfEthicsDto {
  @IsNotEmpty()
  @IsString()
  image: string;

  @IsNotEmpty()
  @IsString()
  organizationName: string;

  @IsNotEmpty()
  @IsString()
  organizationImage: string;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsNotEmpty()
  @IsNumber()
  student: number;

  @IsNotEmpty()
  @IsArray()
  languages: string[];
}

export class UpdateCodeOfEthicsDto extends CreateCodeOfEthicsDto {}
