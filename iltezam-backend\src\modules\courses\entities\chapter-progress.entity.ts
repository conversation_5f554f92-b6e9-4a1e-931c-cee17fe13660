import { <PERSON><PERSON>ty, Column, ManyToOne } from 'typeorm';
import { User } from 'src/modules/auth/entities/user.entity';
import { Chapter } from './chapter.entity';
import { Course } from './course.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';

@Entity()
export class ChapterProgress extends BaseEntityClass {
  @ManyToOne(() => User, (user) => user.chapterProgress, { eager: false })
  user: User;

  @ManyToOne(() => Chapter, { eager: false })
  chapter: Chapter;

  @ManyToOne(() => Course, { eager: false })
  course: Course;

  @Column({ type: 'double precision', default: 0 })
  progressInSeconds: number;

  @Column({ default: false })
  isCompleted: boolean;

  @Column({ default: true })
  isLocked: boolean;
}
