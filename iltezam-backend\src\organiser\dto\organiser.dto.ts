import { IsEmail, IsOptional, IsBoolean, IsString, IsArray } from 'class-validator';

export class CreateOrganizationDto {
  @IsEmail()
  email: string;

  @IsString()
  organizationName: string;

  @IsOptional()
  @IsString()
  organizationNameArabic?: string;

  @IsString()
  country: string;

  @IsOptional()
  @IsString()
  website?: string;

  @IsString()
  contactName: string;

  @IsString()
  contactPhone: string;

  @IsString()
  contactTitle: string;

  @IsEmail()
  contactEmail: string;

  @IsBoolean()
  goodGovernance: boolean;

  @IsBoolean()
  transparencyReporting: boolean;

  @IsBoolean()
  sustainableFunding: boolean;

  @IsOptional()
  @IsString()
  impactMeasurement?: string;

  @IsString()
  shortBio: string;

  @IsString()
  organizationImage: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  organizationTags?: string[];
}

