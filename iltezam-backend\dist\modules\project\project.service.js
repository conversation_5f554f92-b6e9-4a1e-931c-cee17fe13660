"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const project_entity_1 = require("./entities/project.entity");
const position_entity_1 = require("./entities/position.entity");
const donation_entity_1 = require("./entities/donation.entity");
const volunteer_application_entity_1 = require("./entities/volunteer-application.entity");
const user_entity_1 = require("../auth/entities/user.entity");
const project_entity_2 = require("./entities/project.entity");
const organiser_entities_1 = require("../../organiser/entities/organiser.entities");
let ProjectService = class ProjectService {
    projectRepo;
    organizationRepo;
    positionRepo;
    donationRepo;
    applicationRepo;
    userRepo;
    constructor(projectRepo, organizationRepo, positionRepo, donationRepo, applicationRepo, userRepo) {
        this.projectRepo = projectRepo;
        this.organizationRepo = organizationRepo;
        this.positionRepo = positionRepo;
        this.donationRepo = donationRepo;
        this.applicationRepo = applicationRepo;
        this.userRepo = userRepo;
    }
    async create(createProjectDto, userId) {
        try {
            const user = await this.userRepo.findOne({
                where: { id: Number(userId) },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            if (!user.isOrganization) {
                throw new common_1.HttpException('Only organizations can create projects', common_1.HttpStatus.FORBIDDEN);
            }
            if (createProjectDto.projectCategory !== project_entity_1.ProjectCategory.VOLUNTEER &&
                createProjectDto.positions &&
                createProjectDto.positions.length > 0) {
                throw new common_1.HttpException('Positions can only be added to volunteer projects', common_1.HttpStatus.BAD_REQUEST);
            }
            const organization = await this.organizationRepo.findOne({
                where: { user: { id: userId } },
            });
            if (!organization) {
                throw new common_1.NotFoundException('Organization not found');
            }
            const project = this.projectRepo.create({
                ...createProjectDto,
                createdBy: user,
                fundsCollected: createProjectDto.fundsCollected ?? 0,
                organization,
            });
            const savedProject = await this.projectRepo.save(project);
            return savedProject;
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAll(category, location) {
        try {
            const where = {};
            if (category) {
                where.projectCategory = category;
            }
            if (location) {
                where.location = location;
            }
            const projects = await this.projectRepo.find({
                where,
                relations: ['positions', 'createdBy'],
                select: {
                    positions: {
                        id: true,
                        positionName: true,
                        requiredVolunteers: true,
                        positionDescription: true,
                    },
                },
                order: {
                    totalClicks: 'DESC',
                    fundsCollected: 'DESC',
                },
            });
            return projects.map((project) => {
                const { email, organizationName, profilePicture } = project.createdBy || {};
                const { organizationTags, organizationImage } = project.organization || {};
                return {
                    ...project,
                    createdBy: {
                        email,
                        organizationName,
                        organizationTags,
                        organizationImage,
                    },
                };
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrganizationProjects(userId) {
        const projects = await this.projectRepo.find({
            where: {
                createdBy: { id: userId },
            },
            relations: ['positions', 'createdBy', 'organization'],
        });
        return projects;
    }
    async findOne(id) {
        try {
            const project = await this.projectRepo.findOne({
                where: { id: Number(id) },
                relations: ['positions', 'createdBy', 'organization'],
            });
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            project.totalClicks += 1;
            await this.projectRepo.save(project);
            const { id: userId, email, organizationName } = project.createdBy;
            const organizationTags = project.organization?.organizationTags || [];
            return {
                ...project,
                createdBy: {
                    id: userId,
                    email,
                    organizationName,
                    organizationTags,
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async update(id, updateProjectDto, userId) {
        try {
            const project = await this.projectRepo.findOne({
                where: { id: Number(id) },
                relations: ['createdBy'],
            });
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            if (project.createdBy.id !== Number(userId)) {
                throw new common_1.HttpException('You are not authorized to update this project', common_1.HttpStatus.FORBIDDEN);
            }
            Object.assign(project, updateProjectDto);
            await this.projectRepo.save(project);
            if (updateProjectDto.positions && updateProjectDto.positions.length > 0) {
                await this.positionRepo.delete({ project: { id: Number(id) } });
                const positions = updateProjectDto.positions.map((pos) => this.positionRepo.create({
                    ...pos,
                    project,
                }));
                await this.positionRepo.save(positions);
            }
            const { email, organizationName } = project.createdBy;
            return {
                ...project,
                createdBy: {
                    email,
                    organizationName,
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async remove(id, userId) {
        try {
            const project = await this.projectRepo.findOne({
                where: { id: Number(id) },
                relations: ['createdBy'],
            });
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            if (project.createdBy.id !== Number(userId)) {
                throw new common_1.HttpException('You are not authorized to delete this project', common_1.HttpStatus.FORBIDDEN);
            }
            await this.projectRepo.remove(project);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getProjectsByOrganization(organizationId) {
        try {
            const projects = await this.projectRepo.find({
                where: {
                    organization: { id: Number(organizationId) },
                },
                relations: ['positions', 'createdBy', 'organization'],
            });
            return projects.map((project) => {
                const { email, organizationName, profilePicture } = project.createdBy || {};
                return {
                    ...project,
                    createdBy: {
                        email,
                        organizationName,
                        profilePicture,
                    },
                };
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async closeProject(projectId, userId) {
        try {
            const project = await this.projectRepo.findOne({
                where: { id: Number(projectId) },
                relations: ['createdBy'],
            });
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            if (project.createdBy.id !== Number(userId)) {
                throw new common_1.HttpException('You are not authorized to close this project', common_1.HttpStatus.FORBIDDEN);
            }
            if (project.status === project_entity_2.ProjectStatus.CLOSED) {
                throw new common_1.HttpException('Project is already closed', common_1.HttpStatus.BAD_REQUEST);
            }
            project.status = project_entity_2.ProjectStatus.CLOSED;
            return await this.projectRepo.save(project);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createDonation(createDonationDto, userId) {
        try {
            const user = await this.userRepo.findOne({
                where: { id: Number(userId) },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const project = await this.projectRepo.findOne({
                where: { id: Number(createDonationDto.projectId) },
            });
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            if (project.projectCategory !== project_entity_1.ProjectCategory.DONATION) {
                throw new common_1.HttpException('This project does not accept donations', common_1.HttpStatus.BAD_REQUEST);
            }
            const newTotal = (project.fundsCollected || 0) + createDonationDto.amount;
            if (newTotal > project.fundRaisingGoal) {
                throw new common_1.HttpException('Donation exceeds fundraising goal', common_1.HttpStatus.BAD_REQUEST);
            }
            const paymentSuccessful = true;
            const transactionId = `tx_${Math.random().toString(36).substring(2, 15)}`;
            const donation = this.donationRepo.create({
                donor: user,
                project: project,
                paymentMethod: createDonationDto.paymentMethod,
                frequency: createDonationDto.frequency,
                amount: createDonationDto.amount,
                status: paymentSuccessful
                    ? donation_entity_1.PaymentStatus.SUCCESS
                    : donation_entity_1.PaymentStatus.FAILED,
                transactionId: transactionId,
                paymentDate: new Date(),
                notes: createDonationDto.notes,
            });
            const savedDonation = await this.donationRepo.save(donation);
            if (paymentSuccessful) {
                project.fundsCollected = newTotal;
                await this.projectRepo.save(project);
            }
            return {
                id: savedDonation.id,
                amount: savedDonation.amount,
                status: savedDonation.status,
                paymentMethod: savedDonation.paymentMethod,
                frequency: savedDonation.frequency,
                transactionId: savedDonation.transactionId,
                paymentDate: savedDonation.paymentDate,
                notes: savedDonation.notes,
                donor: {
                    email: user.email,
                    FullName: user.FullName,
                    profilePicture: user.profilePicture,
                },
                project: {
                    id: project.id,
                    projectName: project.projectName,
                    projectCategory: project.projectCategory,
                    thumbnailImage: project.thumbnailImage,
                    fundRaisingGoal: project.fundRaisingGoal,
                    fundsCollected: project.fundsCollected,
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDonationsByUser(userId) {
        try {
            const donations = await this.donationRepo.find({
                where: { donor: { id: Number(userId) } },
                relations: ['project', 'donor'],
            });
            return donations.map((donation) => ({
                id: donation.id,
                amount: donation.amount,
                status: donation.status,
                paymentMethod: donation.paymentMethod,
                frequency: donation.frequency,
                transactionId: donation.transactionId,
                paymentDate: donation.paymentDate,
                donor: {
                    FullName: donation.donor.FullName,
                    email: donation.donor.email,
                    profilePicture: donation.donor.profilePicture,
                },
                project: {
                    id: donation.project.id,
                    projectName: donation.project.projectName,
                    projectCategory: donation.project.projectCategory,
                    thumbnailImage: donation.project.thumbnailImage,
                    fundRaisingGoal: donation.project.fundRaisingGoal,
                    fundsCollected: donation.project.fundsCollected,
                },
            }));
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDonationsByProject(projectId) {
        try {
            const donations = await this.donationRepo.find({
                where: { project: { id: Number(projectId) } },
                relations: ['donor', 'donor.profile'],
                order: { created_at: 'DESC' },
            });
            return donations.map((donation) => ({
                id: donation.id,
                amount: donation.amount,
                createdAt: donation.created_at,
                donor: {
                    id: donation.donor.id,
                    name: donation.donor.profile?.fullName ||
                        donation.donor.profile?.organizationName ||
                        donation.donor.email,
                    email: donation.donor.email,
                    profilePicture: donation.donor.profile?.profilePicture || null,
                },
            }));
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Something went wrong', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDonationsById(id) {
        try {
            const donation = await this.donationRepo.findOne({
                where: { id: Number(id) },
                relations: ['project', 'donor', 'donor.profile'],
            });
            if (!donation) {
                throw new common_1.NotFoundException('Donation not found');
            }
            return donation;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Something went wrong', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getVolunteerProjectWithPositions() {
        try {
            return await this.projectRepo.find({
                where: { projectCategory: project_entity_1.ProjectCategory.VOLUNTEER },
                relations: ['positions'],
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getVolunteerProjectPositionsById(projectId, positionId) {
        try {
            const position = await this.positionRepo.findOne({
                where: {
                    id: Number(positionId),
                    project: { id: Number(projectId) },
                },
                relations: ['project'],
            });
            if (!position) {
                throw new common_1.NotFoundException('Position not found in this project');
            }
            if (position.project.projectCategory !== project_entity_1.ProjectCategory.VOLUNTEER) {
                throw new common_1.HttpException('This position is not from a volunteer project', common_1.HttpStatus.BAD_REQUEST);
            }
            const response = {
                position: {
                    id: position.id,
                    positionName: position.positionName,
                    requiredVolunteers: position.requiredVolunteers,
                    positionDescription: position.positionDescription,
                    positionApplications: position.totalApplications,
                },
                project: {
                    id: position.project.id,
                    projectName: position.project.projectName,
                    projectCategory: position.project.projectCategory,
                    status: position.project.status,
                    location: position.project.location,
                    startDate: position.project.startDate,
                    finishDate: position.project.finishDate,
                    projectDescription: position.project.projectDescription,
                    images: position.project.images,
                },
            };
            return response;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Something went wrong', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createApplication(projectId, positionId, createApplicationDto, userId) {
        try {
            const user = await this.userRepo.findOne({
                where: { id: Number(userId) },
            });
            if (!user) {
                throw new common_1.NotFoundException('User not found');
            }
            const project = await this.projectRepo.findOne({
                where: { id: Number(projectId) },
            });
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            const position = await this.positionRepo.findOne({
                where: { id: Number(positionId) },
                relations: ['project'],
            });
            if (!position || position.project.id !== project.id) {
                throw new common_1.NotFoundException('Position not found for this project');
            }
            const duplicateCheck = await this.applicationRepo.findOne({
                where: {
                    applicant: { id: user.id },
                    position: { id: position.id },
                },
            });
            if (duplicateCheck) {
                throw new common_1.HttpException('You have already applied for this position', common_1.HttpStatus.BAD_REQUEST);
            }
            if (!createApplicationDto.CV || createApplicationDto.CV.length === 0) {
                throw new common_1.HttpException('At least one document is required', common_1.HttpStatus.BAD_REQUEST);
            }
            const application = this.applicationRepo.create({
                applicant: user,
                project: project,
                position: position,
                CV: createApplicationDto.CV,
                status: volunteer_application_entity_1.ApplicationStatus.PENDING,
            });
            const savedApplication = await this.applicationRepo.save(application);
            position.totalApplications += 1;
            await this.positionRepo.save(position);
            return savedApplication;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAllApplicationAgainstProject(projectId) {
        try {
            const applications = await this.applicationRepo.find({
                where: { project: { id: Number(projectId) } },
                relations: ['applicant', 'applicant.profile', 'position'],
                order: { created_at: 'DESC' },
            });
            return applications.map((application) => ({
                id: application.id,
                status: application.status,
                createdAt: application.created_at,
                CV: application.CV,
                position: {
                    id: application.position.id,
                    title: application.position.positionName,
                },
                applicant: {
                    id: application.applicant.id,
                    email: application.applicant.email,
                    fullName: application.applicant.profile?.fullName || null,
                    profilePicture: application.applicant.profile?.profilePicture || null,
                },
            }));
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Something went wrong', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getApplicationsByUser(userId) {
        try {
            return await this.applicationRepo.find({
                where: { applicant: { id: Number(userId) } },
                relations: ['project', 'position'],
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getApplicationsByProject(projectId) {
        try {
            return await this.applicationRepo.find({
                where: { project: { id: Number(projectId) } },
                relations: ['applicant', 'position'],
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateApplicationStatus(applicationId, status, organizationId) {
        try {
            const application = await this.applicationRepo.findOne({
                where: { id: Number(applicationId) },
                relations: ['project', 'project.createdBy'],
            });
            if (!application) {
                throw new common_1.NotFoundException('Application not found');
            }
            if (application.project.createdBy.id !== Number(organizationId)) {
                throw new common_1.HttpException('You are not authorized to update this application', common_1.HttpStatus.FORBIDDEN);
            }
            application.status = status;
            return await this.applicationRepo.save(application);
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getOrganizationDonations(userId) {
        try {
            const organization = await this.organizationRepo.findOne({
                where: { user: { id: Number(userId) } },
            });
            console.log('Organization found:', organization);
            if (!organization) {
                throw new common_1.NotFoundException('Organization not found');
            }
            const projects = await this.projectRepo.find({
                where: { organization: { id: organization.id } },
                relations: ['donations', 'donations.donor', 'donations.donor.profile'],
            });
            console.log('Projects found:', projects.length);
            const donations = projects.flatMap((project) => {
                console.log(`Project ${project.id} donations count:`, project.donations.length);
                return project.donations.map((donation) => ({
                    id: donation.id,
                    amount: donation.amount,
                    status: donation.status,
                    paymentMethod: donation.paymentMethod,
                    frequency: donation.frequency,
                    transactionId: donation.transactionId,
                    paymentDate: donation.paymentDate,
                    createdAt: donation.created_at,
                    project: {
                        id: project.id,
                        projectName: project.projectName,
                        projectCategory: project.projectCategory,
                        thumbnailImage: project.thumbnailImage,
                        fundRaisingGoal: project.fundRaisingGoal,
                        fundsCollected: project.fundsCollected,
                    },
                    donor: {
                        id: donation.donor.id,
                        name: donation.donor.profile?.fullName || donation.donor.email,
                        email: donation.donor.email,
                        profilePicture: donation.donor.profile?.profilePicture,
                    },
                }));
            });
            console.log('Total donations found:', donations.length);
            return donations.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.ProjectService = ProjectService;
exports.ProjectService = ProjectService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(project_entity_1.Project)),
    __param(1, (0, typeorm_1.InjectRepository)(organiser_entities_1.Organization)),
    __param(2, (0, typeorm_1.InjectRepository)(position_entity_1.Position)),
    __param(3, (0, typeorm_1.InjectRepository)(donation_entity_1.Donation)),
    __param(4, (0, typeorm_1.InjectRepository)(volunteer_application_entity_1.VolunteerApplication)),
    __param(5, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ProjectService);
//# sourceMappingURL=project.service.js.map