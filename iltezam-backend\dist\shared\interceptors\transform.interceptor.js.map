{"version": 3, "file": "transform.interceptor.js", "sourceRoot": "", "sources": ["../../../src/shared/interceptors/transform.interceptor.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4F;AAE5F,8CAAqC;AAS9B,IAAM,oBAAoB,GAA1B,MAAM,oBAAoB;IAC/B,SAAS,CAAC,OAAyB,EAAE,IAAiB;QACpD,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,IAAI,CACvB,IAAA,eAAG,EAAC,QAAQ,CAAC,EAAE;YACb,MAAM,OAAO,GAAG,QAAQ,EAAE,OAAO,IAAI,SAAS,CAAC;YAG/C,MAAM,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG,kBAAkB,EAAE,GAAG,QAAQ,CAAC;YAEvD,OAAO;gBACL,UAAU,EAAE,GAAG;gBACf,OAAO;gBACP,IAAI,EAAE,kBAAkB,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAkB;aAC3F,CAAC;QACJ,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CACF,CAAA;AAjBY,oDAAoB;+BAApB,oBAAoB;IADhC,IAAA,mBAAU,GAAE;GACA,oBAAoB,CAiBhC"}