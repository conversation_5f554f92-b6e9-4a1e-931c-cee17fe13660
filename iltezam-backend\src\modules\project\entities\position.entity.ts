import {
  Column,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import { Project } from './project.entity';
import { BadRequestException } from '@nestjs/common';
import { BaseEntityClass } from '../../auth/entities/base.entity';

@Entity()
export class Position extends BaseEntityClass {
  @Column()
  positionName: string;

  @Column()
  requiredVolunteers: number;

  @Column({ type: 'text' })
  positionDescription: string;

  @Column({ default: 0 })
  totalApplications: number;

  @ManyToOne(() => Project, (project) => project.positions, {
    onDelete: 'CASCADE',
  })
  project: Project;
}
