"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectController = void 0;
const common_1 = require("@nestjs/common");
const project_service_1 = require("./project.service");
const project_dto_1 = require("./dtos/project.dto");
const donation_dto_1 = require("./dtos/donation.dto");
const volunteer_app_dto_1 = require("./dtos/volunteer-app.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
const volunteer_application_entity_1 = require("./entities/volunteer-application.entity");
const publicRoute_decorator_1 = require("../../shared/decorators/publicRoute.decorator");
let ProjectController = class ProjectController {
    projectService;
    constructor(projectService) {
        this.projectService = projectService;
    }
    async createProject(createProjectDto, req) {
        return this.projectService.create(createProjectDto, req.user.id);
    }
    async findAll(category, location) {
        return this.projectService.findAll(category, location);
    }
    async getMyProjects(req) {
        return this.projectService.getProjectsByOrganization(req.user.id);
    }
    async getOrganizationProjects(req) {
        console.log('Decoded user:', req.user);
        return this.projectService.getOrganizationProjects(req.user.id);
    }
    async getProjectsByOrganizationId(organizationUserId) {
        const projects = await this.projectService.getProjectsByOrganization(organizationUserId);
        return {
            statusCode: common_1.HttpStatus.OK,
            message: 'success',
            data: [...projects],
        };
    }
    async getProjectsByPosition(organizationUserId) {
        const projects = await this.projectService.getProjectsByPosition(organizationUserId);
        return {
            statusCode: common_1.HttpStatus.OK,
            message: 'success',
            data: [...projects],
        };
    }
    async findOne(id) {
        return this.projectService.findOne(id);
    }
    async update(id, updateProjectDto, req) {
        return this.projectService.update(id, updateProjectDto, req.user.id);
    }
    async remove(id, req) {
        return this.projectService.remove(id, req.user.id);
    }
    async closeProject(id, req) {
        return this.projectService.closeProject(id, req.user.id);
    }
    async createDonation(createDonationDto, req) {
        return this.projectService.createDonation(createDonationDto, req.user.id);
    }
    async getMyDonations(req) {
        return this.projectService.getDonationsByUser(req.user.id);
    }
    async getProjectDonations(projectId) {
        return this.projectService.getDonationsByProject(projectId);
    }
    async getVolunteerProjectsWithPositions() {
        return this.projectService.getVolunteerProjectWithPositions();
    }
    async getVolunteerPosition(projectId, positionId) {
        return this.projectService.getVolunteerProjectPositionsById(projectId, positionId);
    }
    async createApplication(projectId, positionId, createApplicationDto, req) {
        return this.projectService.createApplication(projectId, positionId, createApplicationDto, req.user.id);
    }
    async getAllApplicationAgainstProject(projectId) {
        return this.projectService.getAllApplicationAgainstProject(projectId);
    }
    async getMyApplications(req) {
        return this.projectService.getApplicationsByUser(req.user.id);
    }
    async getProjectApplications(projectId) {
        return this.projectService.getApplicationsByProject(projectId);
    }
    async updateApplicationStatus(id, status, req) {
        return this.projectService.updateApplicationStatus(id, status, req.user.id);
    }
    async getOrganizationDonations(req) {
        console.log('JWT decoded user object:', req.user);
        return this.projectService.getOrganizationDonations(req.user.id);
    }
    async getDonationById(donationId) {
        return this.projectService.getDonationsById(donationId);
    }
};
exports.ProjectController = ProjectController;
__decorate([
    (0, swagger_1.ApiTags)('create-project'),
    (0, common_1.Post)('create-project'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new project' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Project created successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [project_dto_1.CreateProjectDto, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "createProject", null);
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all projects' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return all projects',
    }),
    __param(0, (0, common_1.Query)('category')),
    __param(1, (0, common_1.Query)('location')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "findAll", null);
__decorate([
    (0, swagger_1.ApiTags)('Projects'),
    (0, common_1.Get)('organization/my-projects'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all projects created by the authenticated user',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return all projects by user',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getMyProjects", null);
__decorate([
    (0, swagger_1.ApiTags)('organizationProjects'),
    (0, common_1.Get)('organizationProjects'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get projects created by the authenticated user',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Returns projects created by the logged-in user',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getOrganizationProjects", null);
__decorate([
    (0, common_1.Get)('organization/:organizationUserId'),
    __param(0, (0, common_1.Param)('organizationUserId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getProjectsByOrganizationId", null);
__decorate([
    (0, common_1.Get)('organizationPosition/:organizationUserId'),
    __param(0, (0, common_1.Param)('organizationUserId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getProjectsByPosition", null);
__decorate([
    (0, swagger_1.ApiTags)('Projects'),
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a project by ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return the project',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "findOne", null);
__decorate([
    (0, swagger_1.ApiTags)('Projects'),
    (0, common_1.Put)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update a project' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Project updated successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, project_dto_1.UpdateProjectDto, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiTags)('Projects'),
    (0, common_1.Delete)(':id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a project' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Project deleted successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "remove", null);
__decorate([
    (0, common_1.Patch)(':id/status/close'),
    (0, swagger_1.ApiTags)('Projects Close'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Close a project (only for project creator)' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID of the project to close' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Project closed successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Project not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Not authorized to close this project',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Project is already closed',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "closeProject", null);
__decorate([
    (0, swagger_1.ApiTags)('Donations'),
    (0, common_1.Post)('donations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Make a donation to a project' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Donation processed successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [donation_dto_1.CreateDonationDto, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "createDonation", null);
__decorate([
    (0, swagger_1.ApiTags)('Donations'),
    (0, common_1.Get)('donations/my-donations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all donations made by the authenticated user' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return all donations by user',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getMyDonations", null);
__decorate([
    (0, swagger_1.ApiTags)('Donations'),
    (0, common_1.Get)('donations/project/:projectId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all donations for a specific project' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return all donations for the project',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getProjectDonations", null);
__decorate([
    (0, common_1.Get)('volunteer-projects/positions'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all volunteer projects with their positions' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Returns all volunteer projects with positions',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.INTERNAL_SERVER_ERROR,
        description: 'Internal server error',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getVolunteerProjectsWithPositions", null);
__decorate([
    (0, common_1.Get)(':projectId/positions/:positionId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get position details by project ID and position ID',
    }),
    (0, swagger_1.ApiParam)({ name: 'projectId', description: 'ID of the project' }),
    (0, swagger_1.ApiParam)({ name: 'positionId', description: 'ID of the position' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Returns position details',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Position not found in this project',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Position is not from a volunteer project',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('positionId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getVolunteerPosition", null);
__decorate([
    (0, swagger_1.ApiTags)('Volunteer Applications'),
    (0, common_1.Post)(':projectId/positions/:positionId/apply'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Apply as a volunteer for a project position' }),
    (0, swagger_1.ApiParam)({ name: 'projectId', description: 'ID of the project' }),
    (0, swagger_1.ApiParam)({ name: 'positionId', description: 'ID of the position' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Application submitted successfully.',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __param(1, (0, common_1.Param)('positionId')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, volunteer_app_dto_1.CreateVolunteerApplicationDto, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "createApplication", null);
__decorate([
    (0, common_1.Get)('project/:projectId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all applications for a project' }),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getAllApplicationAgainstProject", null);
__decorate([
    (0, swagger_1.ApiTags)('Volunteer Applications'),
    (0, common_1.Get)('volunteer-applications/my-applications'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all applications made by the authenticated user',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return all applications by user',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getMyApplications", null);
__decorate([
    (0, swagger_1.ApiTags)('Volunteer Applications'),
    (0, common_1.Get)('volunteer-applications/project/:projectId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all applications for a specific project',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return all applications for the project',
    }),
    __param(0, (0, common_1.Param)('projectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getProjectApplications", null);
__decorate([
    (0, swagger_1.ApiTags)('Volunteer Applications'),
    (0, common_1.Put)('volunteer-applications/:id/status'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Update application status (for organization)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Application status updated successfully',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)('status')),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "updateApplicationStatus", null);
__decorate([
    (0, common_1.Get)('donations/organization'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getOrganizationDonations", null);
__decorate([
    (0, swagger_1.ApiTags)('Donations'),
    (0, common_1.Get)('donations/:id'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get a single donation by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'ID of the donation' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Return the donation',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Donation not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectController.prototype, "getDonationById", null);
exports.ProjectController = ProjectController = __decorate([
    (0, swagger_1.ApiTags)('Projects'),
    (0, common_1.Controller)('projects'),
    __metadata("design:paramtypes", [project_service_1.ProjectService])
], ProjectController);
//# sourceMappingURL=project.controller.js.map