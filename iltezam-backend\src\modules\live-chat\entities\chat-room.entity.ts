import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from 'typeorm';

@Entity()
export class ChatRoom {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  roomId: string;

  @Column('int', { array: true, nullable: false })
  users: number[]; // Just IDs, no relation

  @Column({ default: false })
  isGroup: boolean;

  @Column({ nullable: true })
  groupName: string;

  @Column({ nullable: true })
  groupPicture: string;

  @CreateDateColumn()
  createdAt: Date;
}
