"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const database_module_1 = require("./core/database/database.module");
const config_1 = require("@nestjs/config");
const database_config_1 = require("./config/database.config");
const core_1 = require("@nestjs/core");
const transform_interceptor_1 = require("./shared/interceptors/transform.interceptor");
const auth_module_1 = require("./modules/auth/auth.module");
const jwt_auth_guard_1 = require("./modules/auth/guards/jwt-auth.guard");
const organiser_controller_1 = require("./organiser/organiser.controller");
const organiser_module_1 = require("./organiser/organiser.module");
const typeorm_1 = require("@nestjs/typeorm");
const user_entity_1 = require("./modules/auth/entities/user.entity");
const organiser_entities_1 = require("./organiser/entities/organiser.entities");
const post_module_1 = require("./modules/post/post.module");
const uploadd_module_1 = require("./modules/uploadd/uploadd.module");
const s3_config_1 = require("./core/database/s3.config");
const project_entity_1 = require("./modules/project/entities/project.entity");
const project_module_1 = require("./modules/project/project.module");
const course_entity_1 = require("./modules/courses/entities/course.entity");
const course_module_1 = require("./modules/courses/course.module");
const profile_entity_1 = require("./modules/profile/entities/profile.entity");
const profile_module_1 = require("./modules/profile/profile.module");
const notification_module_1 = require("./modules/notification/notification.module");
const live_chat_module_1 = require("./modules/live-chat/live-chat.module");
const codeOfEthics_entity_1 = require("./modules/codeOfEthics/Entity/codeOfEthics.entity");
const codeOfEthics_module_1 = require("./modules/codeOfEthics/codeOfEthics.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            typeorm_1.TypeOrmModule.forFeature([
                user_entity_1.User,
                organiser_entities_1.Organization,
                project_entity_1.Project,
                course_entity_1.Course,
                profile_entity_1.Profile,
                codeOfEthics_entity_1.CodeOfEthics,
            ]),
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                load: [database_config_1.default, s3_config_1.default],
            }),
            database_module_1.DatabaseModule,
            auth_module_1.AuthModule,
            organiser_module_1.OrganiserModule,
            post_module_1.PostModule,
            uploadd_module_1.UploaddModule,
            project_module_1.ProjectModule,
            course_module_1.CourseModule,
            profile_module_1.ProfileModule,
            notification_module_1.NotificationModule,
            live_chat_module_1.LiveChatModule,
            codeOfEthics_module_1.CodeOfEthicsModule,
        ],
        controllers: [app_controller_1.AppController, organiser_controller_1.OrganiserController],
        providers: [
            app_service_1.AppService,
            {
                provide: core_1.APP_GUARD,
                useClass: jwt_auth_guard_1.JwtAuthGuard,
            },
            {
                provide: core_1.APP_INTERCEPTOR,
                useClass: transform_interceptor_1.TransformInterceptor,
            },
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map