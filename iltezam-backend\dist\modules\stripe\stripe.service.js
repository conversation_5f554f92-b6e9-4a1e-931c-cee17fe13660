"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const stripe_1 = require("stripe");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const card_details_entity_1 = require("./entities/card-details.entity");
let StripeService = class StripeService {
    configService;
    cardDetailsRepository;
    stripe;
    constructor(configService, cardDetailsRepository) {
        this.configService = configService;
        this.cardDetailsRepository = cardDetailsRepository;
        const stripeSecretKey = this.configService.get('STRIPE_SECRET_KEY') ||
            'sk_test_51QNq2RCYzRt3u4ZllffIfrUv8GE2OJFduTU1OaxKLd9uwG6NTYfe0iKCaxJP4yzkz5ygtbmP3kQBOgTPtq9k5LwA00vWdM8dwC';
        this.stripe = new stripe_1.default(stripeSecretKey);
    }
    async createPaymentIntent(createPaymentIntentDto) {
        try {
            const { amount, currency } = createPaymentIntentDto;
            if (amount && amount <= 0)
                throw new Error("Amount must be greater than 0");
            const convertedAmount = amount ? amount * 100 : 0;
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount: convertedAmount,
                currency: currency || 'usd',
                payment_method_types: ['card'],
            });
            return {
                clientSecret: paymentIntent.client_secret,
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Failed to create payment intent: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async saveCardDetails(saveCardDetailsDto, userId) {
        try {
            const { cardNumber, expiryMonth, expiryYear, cardHolderName, cvv } = saveCardDetailsDto;
            const cardDetails = this.cardDetailsRepository.create({
                userId,
                cardNumber,
                expiryMonth,
                expiryYear,
                cardHolderName,
                cvv,
            });
            await this.cardDetailsRepository.save(cardDetails);
            return {
                success: true,
                message: 'Card details saved successfully',
                cardDetails: {
                    id: cardDetails.id,
                    cardNumber,
                    expiryMonth,
                    expiryYear,
                    cardHolderName,
                    cvv
                },
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Failed to save card details: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getSavedCards(userId) {
        try {
            const cards = await this.cardDetailsRepository.find({
                where: { userId },
            });
            return {
                success: true,
                cards,
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Failed to get saved cards: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async handleWebhook(event) {
        console.log("here is hook event", event);
        switch (event.type) {
            case 'payment_intent.succeeded':
                console.log('✅ PaymentIntent was successful!');
                return { received: true, message: 'Payment succeeded' };
            case 'payment_intent.payment_failed':
                console.log('❌ Payment failed');
                return { received: true, message: 'Payment failed' };
            case 'payment_intent.processing':
            case 'payment_intent.payment_processing':
                console.log('⏳ Payment processing');
                return { received: true, message: 'Payment processing' };
            default:
                console.log(`⚠️ Unhandled event type: ${event.type}`);
                return { received: true, message: 'Event type not handled or unknown' };
        }
    }
    constructEvent(payload, signature) {
        const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
        if (!endpointSecret) {
            throw new Error('STRIPE_WEBHOOK_SECRET is not defined');
        }
        return this.stripe.webhooks.constructEvent(typeof payload === 'string' ? payload : JSON.stringify(payload), signature, endpointSecret);
    }
};
exports.StripeService = StripeService;
exports.StripeService = StripeService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(card_details_entity_1.CardDetails)),
    __metadata("design:paramtypes", [config_1.ConfigService,
        typeorm_2.Repository])
], StripeService);
//# sourceMappingURL=stripe.service.js.map