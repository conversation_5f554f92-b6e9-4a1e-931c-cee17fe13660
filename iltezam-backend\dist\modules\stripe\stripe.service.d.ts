import { ConfigService } from '@nestjs/config';
import Strip<PERSON> from 'stripe';
import { CreatePaymentIntentDto } from './dtos/stripe-payment.dto';
import { SaveCardDetailsDto } from './dtos/save-card.dto';
import { Repository } from 'typeorm';
import { CardDetails } from './entities/card-details.entity';
export declare class StripeService {
    private configService;
    private cardDetailsRepository;
    private stripe;
    constructor(configService: ConfigService, cardDetailsRepository: Repository<CardDetails>);
    createPaymentIntent(createPaymentIntentDto: CreatePaymentIntentDto): Promise<{
        clientSecret: string | null;
    }>;
    saveCardDetails(saveCardDetailsDto: SaveCardDetailsDto, userId: number): Promise<{
        success: boolean;
        message: string;
        cardDetails: {
            id: number;
            cardNumber: string;
            expiryMonth: string;
            expiryYear: string;
            cardHolderName: string;
            cvv: string;
        };
    }>;
    getSavedCards(userId: number): Promise<{
        success: boolean;
        cards: CardDetails[];
    }>;
    handleWebhook(event: any): Promise<any>;
    constructEvent(payload: any, signature: string): Stripe.Event;
}
