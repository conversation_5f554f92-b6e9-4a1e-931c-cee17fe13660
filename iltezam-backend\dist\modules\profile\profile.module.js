"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const profile_entity_1 = require("./entities/profile.entity");
const profile_service_1 = require("./profile.service");
const profile_controller_1 = require("./profile.controller");
const profile_connection_entity_1 = require("./entities/profile.connection.entity");
const user_entity_1 = require("../auth/entities/user.entity");
const chat_room_entity_1 = require("../live-chat/entities/chat-room.entity");
const organization_follow_entities_1 = require("../../organiser/entities/organization.follow.entities");
let ProfileModule = class ProfileModule {
};
exports.ProfileModule = ProfileModule;
exports.ProfileModule = ProfileModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([
                profile_entity_1.Profile,
                profile_connection_entity_1.ProfileConnection,
                user_entity_1.User,
                chat_room_entity_1.ChatRoom,
                organization_follow_entities_1.OrganizationFollow
            ])],
        controllers: [profile_controller_1.ProfileController],
        providers: [profile_service_1.ProfileService],
        exports: [profile_service_1.ProfileService],
    })
], ProfileModule);
//# sourceMappingURL=profile.module.js.map