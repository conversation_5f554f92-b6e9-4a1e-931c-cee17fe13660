import { ProfileService } from './profile.service';
import { UpdateProfileDto } from './dtos/create-profile.dto';
import { Request } from 'express';
interface AuthenticatedRequest extends Request {
    user: {
        id: number;
    };
}
export declare class ProfileController {
    private readonly profileService;
    constructor(profileService: ProfileService);
    getMyConnections(req: AuthenticatedRequest): Promise<{
        users: {
            user: {
                id: number;
                email: string;
                FullName: string;
                designation: string;
                profilePicture: string;
                isOrganization: boolean;
                organizationName: string | undefined;
                created_at: Date;
                updated_at: Date;
            };
            connectionId: number;
            status: "pending" | "accepted" | "rejected" | "disconnected";
            createdAt: Date;
            updatedAt: Date;
        }[];
    }>;
    getMyProfile(req: AuthenticatedRequest): Promise<any>;
    getProfileByUserId(userId: number): Promise<any>;
    updateMyProfile(req: AuthenticatedRequest, updateProfileDto: UpdateProfileDto): Promise<import("./entities/profile.entity").Profile>;
    sendConnectionRequest(receiverUserId: number, req: AuthenticatedRequest): Promise<import("./entities/profile.connection.entity").ProfileConnection>;
    acceptConnection(connectionId: number): Promise<import("./entities/profile.connection.entity").ProfileConnection>;
    rejectConnection(connectionId: number): Promise<import("./entities/profile.connection.entity").ProfileConnection>;
}
export {};
