"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganiserModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const organiser_entities_1 = require("./entities/organiser.entities");
const organiser_controller_1 = require("./organiser.controller");
const organiser_service_1 = require("./organiser.service");
const user_entity_1 = require("../modules/auth/entities/user.entity");
const organization_follow_entities_1 = require("./entities/organization.follow.entities");
const project_entity_1 = require("../modules/project/entities/project.entity");
let OrganiserModule = class OrganiserModule {
};
exports.OrganiserModule = OrganiserModule;
exports.OrganiserModule = OrganiserModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([organiser_entities_1.Organization, user_entity_1.User, organization_follow_entities_1.OrganizationFollow, project_entity_1.Project])],
        controllers: [organiser_controller_1.OrganiserController],
        providers: [organiser_service_1.organiserService],
        exports: [organiser_service_1.organiserService],
    })
], OrganiserModule);
//# sourceMappingURL=organiser.module.js.map