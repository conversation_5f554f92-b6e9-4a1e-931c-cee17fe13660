"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const auth_service_1 = require("./auth.service");
const publicRoute_decorator_1 = require("../../shared/decorators/publicRoute.decorator");
const auth_dto_1 = require("./dtos/auth.dto");
const auth_dto_2 = require("./dtos/auth.dto");
const swagger_1 = require("@nestjs/swagger");
const passport_1 = require("@nestjs/passport");
const auth_dto_3 = require("./dtos/auth.dto");
const auth_dto_4 = require("./dtos/auth.dto");
const auth_dto_5 = require("./dtos/auth.dto");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
let AuthController = class AuthController {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async login(authenticateRequest) {
        try {
            return await this.authService.loginUser(authenticateRequest);
        }
        catch (e) {
            throw new common_1.HttpException(e.message, 401);
        }
    }
    async register(signUpUserDto) {
        try {
            let result = await this.authService.registerUser(signUpUserDto);
            return {
                message: result.message,
                data: result.data,
            };
        }
        catch (e) {
            if (e instanceof common_1.HttpException) {
                throw e;
            }
            throw new common_1.HttpException(e.message, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async verifyEmail(params) {
        try {
            const result = await this.authService.verifyEmail(params);
            return {
                message: result.message,
            };
        }
        catch (e) {
            throw new common_1.UnauthorizedException(e.message);
        }
    }
    async forgotPassword(dto) {
        try {
            const result = await this.authService.forgotPassword(dto.email);
            return {
                message: result.message,
            };
        }
        catch (e) {
            throw new common_1.UnauthorizedException(e.message);
        }
    }
    async resendVerification(dto) {
        try {
            const result = await this.authService.resendVerificationEmail(dto.email);
            return {
                message: result.message,
            };
        }
        catch (e) {
            throw new common_1.UnauthorizedException(e.message);
        }
    }
    async getAllUsers(req, search) {
        const currentUserId = req.user?.id;
        if (!currentUserId) {
            throw new common_1.UnauthorizedException('User not authenticated');
        }
        return await this.authService.getAllUsers(currentUserId, search);
    }
    async createCompany(companyDto, req) {
        return this.authService.createCompany(companyDto);
    }
    async subscribeEmail(body) {
        const { email } = body;
        try {
            const result = await this.authService.emailSubscribe(email);
            return {
                message: result.message,
            };
        }
        catch (e) {
            throw new common_1.UnauthorizedException(e.message);
        }
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Post)('login'),
    (0, swagger_1.ApiOperation)({ summary: 'Sign in to account' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.AuthLoginDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Post)('register'),
    (0, swagger_1.ApiOperation)({ summary: 'Register User' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_2.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Post)('verify-email'),
    (0, swagger_1.ApiOperation)({ summary: 'Verify Email' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_1.verifyEmaildDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyEmail", null);
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Post)('forgot-password'),
    (0, swagger_1.ApiOperation)({ summary: 'Forgot Password' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_4.ForgotPasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Post)('resend-verification'),
    (0, swagger_1.ApiOperation)({ summary: 'Resend Email Verification' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_3.ResendVerificationDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resendVerification", null);
__decorate([
    (0, common_1.Get)('users'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getAllUsers", null);
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new company' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Company created successfully',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [auth_dto_5.CompanyDto, Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "createCompany", null);
__decorate([
    (0, common_1.Post)('subscribe-email'),
    (0, publicRoute_decorator_1.Public)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "subscribeEmail", null);
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map