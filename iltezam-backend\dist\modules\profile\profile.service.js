"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const profile_entity_1 = require("./entities/profile.entity");
const profile_connection_entity_1 = require("./entities/profile.connection.entity");
const user_entity_1 = require("../auth/entities/user.entity");
const chat_room_entity_1 = require("../live-chat/entities/chat-room.entity");
const organization_follow_entities_1 = require("../../organiser/entities/organization.follow.entities");
let ProfileService = class ProfileService {
    profileRepository;
    connectionRepository;
    userRepository;
    chatRoomRepository;
    orgFollowRepository;
    constructor(profileRepository, connectionRepository, userRepository, chatRoomRepository, orgFollowRepository) {
        this.profileRepository = profileRepository;
        this.connectionRepository = connectionRepository;
        this.userRepository = userRepository;
        this.chatRoomRepository = chatRoomRepository;
        this.orgFollowRepository = orgFollowRepository;
    }
    async getByUserId(userId, countsOnly = false) {
        const profile = await this.profileRepository.findOne({
            where: { user: { id: userId } },
            relations: ['user'],
        });
        if (!profile) {
            throw new common_1.NotFoundException(`Profile for user ID ${userId} not found`);
        }
        const groupRoomsCount = await this.chatRoomRepository
            .createQueryBuilder('room')
            .where('room.isGroup = :isGroup', { isGroup: true })
            .andWhere(':userId = ANY(room.users)', { userId })
            .getCount();
        const orgFollowCount = await this.orgFollowRepository.count({
            where: {
                follower: { id: userId },
                status: true,
            },
        });
        console.log('Group Rooms Count:', groupRoomsCount);
        console.log('Organization Follow Count:', orgFollowCount);
        if (countsOnly) {
            const connections = await this.getConnections(userId);
            const acceptedCount = connections.users.filter((c) => c.status === 'accepted').length;
            const disconnectedCount = connections.users.filter((c) => c.status === 'disconnected').length;
            return {
                profile,
                groupRoomsCount,
                orgFollowCount,
                connectionsCount: {
                    accepted: acceptedCount,
                    disconnected: disconnectedCount,
                },
            };
        }
        else {
            let connections = await this.getConnections(userId);
            let profileWithStatus = {
                status: connections.users.length > 0 ? connections.users[0].status : 'Not Connected',
                ...profile,
            };
            return { profile: profileWithStatus, connections, groupRoomsCount,
                orgFollowCount, };
        }
    }
    async getUserByUserId(userId) {
        console.log('Looking for profile of userId:', userId);
        const profile = await this.profileRepository.findOne({
            where: { user: { id: userId } },
            relations: ['user'],
        });
        if (!profile) {
            throw new common_1.NotFoundException(`Profile for user ID ${userId} not found`);
        }
        let connections = await this.getConnections(userId);
        let profileWithStatus = {
            status: connections.users.length > 0 ? connections.users[0].status : 'none',
            ...profile,
        };
        return { profile: profileWithStatus, connections };
    }
    async updateByUserId(userId, updateProfileDto) {
        const existingProfile = await this.profileRepository.findOne({
            where: { user: { id: userId } },
            relations: ['user'],
        });
        if (!existingProfile) {
            throw new common_1.NotFoundException(`Profile not found for user ID: ${userId}`);
        }
        for (const key in updateProfileDto) {
            if (key !== 'id' && key !== 'user') {
                existingProfile[key] = updateProfileDto[key];
            }
        }
        if (updateProfileDto.profilePicture !== undefined && existingProfile.user) {
            existingProfile.user.profilePicture = updateProfileDto.profilePicture;
            await this.userRepository.save(existingProfile.user);
        }
        return await this.profileRepository.save(existingProfile);
    }
    async findOne(id) {
        const profile = await this.profileRepository.findOne({
            where: { id },
            relations: ['user'],
        });
        if (!profile) {
            throw new common_1.NotFoundException(`Profile with ID ${id} not found`);
        }
        return profile;
    }
    async connectProfiles(requesterUserId, receiverUserId) {
        if (typeof requesterUserId !== 'number' || typeof receiverUserId !== 'number') {
            throw new common_1.HttpException('Invalid input types', common_1.HttpStatus.BAD_REQUEST);
        }
        if (requesterUserId === receiverUserId) {
            throw new common_1.HttpException('Cannot connect to yourself', common_1.HttpStatus.BAD_REQUEST);
        }
        const [requesterUser, receiverUser] = await Promise.all([
            this.userRepository.findOne({ where: { id: requesterUserId } }),
            this.userRepository.findOne({ where: { id: receiverUserId } }),
        ]);
        if (!requesterUser || !receiverUser) {
            throw new common_1.NotFoundException('User not found');
        }
        const existing = await this.connectionRepository.findOne({
            where: [
                { requester: { id: requesterUserId }, receiver: { id: receiverUserId } },
                { requester: { id: receiverUserId }, receiver: { id: requesterUserId } }
            ],
        });
        if (existing) {
            if (existing.status === 'disconnected' || existing.status === 'rejected') {
                existing.status = 'pending';
            }
            else {
                existing.status = (existing.status === 'pending' ? 'disconnected' : 'disconnected');
            }
            return this.connectionRepository.save(existing);
        }
        const connection = this.connectionRepository.create({
            requester: requesterUser,
            receiver: receiverUser,
            status: 'pending',
        });
        return this.connectionRepository.save(connection);
    }
    async acceptConnection(connectionId) {
        const connection = await this.connectionRepository.findOne({
            where: { id: connectionId },
        });
        if (!connection) {
            throw new common_1.NotFoundException('Connection not found');
        }
        connection.status = 'accepted';
        return await this.connectionRepository.save(connection);
    }
    async rejectConnection(connectionId) {
        const connection = await this.connectionRepository.findOne({
            where: { id: connectionId },
        });
        if (!connection) {
            throw new common_1.NotFoundException('Connection not found');
        }
        return await this.connectionRepository.remove(connection);
    }
    async getConnections(userId) {
        const connections = await this.connectionRepository.find({
            where: [{ requester: { id: userId } }, { receiver: { id: userId } }],
            relations: ['requester', 'receiver'],
        });
        const result = connections.map((conn) => {
            const otherUser = conn.requester.id === userId ? conn.receiver : conn.requester;
            return {
                user: {
                    id: otherUser.id,
                    email: otherUser.email,
                    FullName: otherUser.FullName,
                    designation: otherUser.designation,
                    profilePicture: otherUser.profilePicture,
                    isOrganization: otherUser.isOrganization,
                    organizationName: otherUser.organizationName,
                    created_at: otherUser.created_at,
                    updated_at: otherUser.updated_at,
                },
                connectionId: conn.id,
                status: conn.status,
                createdAt: conn.createdAt,
                updatedAt: conn.updated_at,
            };
        });
        return { users: result };
    }
    async getReceivedConnectionRequests(userId) {
        const connectionRequests = await this.connectionRepository
            .createQueryBuilder('connection')
            .leftJoinAndSelect('connection.requester', 'requester')
            .where('connection.status = :status', { status: 'pending' })
            .andWhere('connection.receiver = :userId', { userId })
            .getMany();
        const result = connectionRequests.map((conn) => {
            const requester = conn.requester;
            return {
                connectionId: conn.id,
                status: conn.status,
                createdAt: conn.createdAt,
                updatedAt: conn.updated_at,
                requester: {
                    id: requester.id,
                    email: requester.email,
                    FullName: requester.FullName,
                    designation: requester.designation,
                    profilePicture: requester.profilePicture,
                    isOrganization: requester.isOrganization,
                    organizationName: requester.organizationName,
                    created_at: requester.created_at,
                    updated_at: requester.updated_at,
                },
            };
        });
        return { users: result };
    }
};
exports.ProfileService = ProfileService;
exports.ProfileService = ProfileService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(profile_entity_1.Profile)),
    __param(1, (0, typeorm_1.InjectRepository)(profile_connection_entity_1.ProfileConnection)),
    __param(2, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(3, (0, typeorm_1.InjectRepository)(chat_room_entity_1.ChatRoom)),
    __param(4, (0, typeorm_1.InjectRepository)(organization_follow_entities_1.OrganizationFollow)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], ProfileService);
//# sourceMappingURL=profile.service.js.map