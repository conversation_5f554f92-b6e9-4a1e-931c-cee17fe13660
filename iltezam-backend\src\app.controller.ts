import { Controller, Get, HttpException, HttpStatus } from '@nestjs/common';
import { AppService } from './app.service';
import { CustomError } from './core/utils/CustomError';
import { Public } from './shared/decorators/publicRoute.decorator';

@Controller()
export class AppController {
    constructor(private readonly appService: AppService) { }

    @Public()
    @Get('health')
    getHello() {
        try {
            let result = this.appService.getHello();
            return {
                message: result
            }
        } catch (error) {
            throw new HttpException(error.message, error.status || HttpStatus.INTERNAL_SERVER_ERROR);

        }
    }
}
