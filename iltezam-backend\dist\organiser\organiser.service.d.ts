import { Repository } from 'typeorm';
import { User } from 'src/modules/auth/entities/user.entity';
import { Organization } from './../organiser/entities/organiser.entities';
import { OrganizationFollow } from './../organiser/entities/organization.follow.entities';
import { CreateOrganizationDto } from './dto/organiser.dto';
export declare class organiserService {
    private readonly userRepo;
    private readonly orgRepo;
    private readonly followRepo;
    projectRepo: any;
    constructor(userRepo: Repository<User>, orgRepo: Repository<Organization>, followRepo: Repository<OrganizationFollow>);
    createOrganiser(createUserDto: CreateOrganizationDto, user: any): Promise<Organization>;
    getOrganiser(userId: number): Promise<{
        id: number;
        created_at: Date;
        updated_at: Date;
        email: string;
        organizationName: string;
        organizationNameArabic: string;
        country: string;
        website: string;
        licenseFilePath: string;
        contactName: string;
        contactPhone: string;
        contactTitle: string;
        contactEmail: string;
        goodGovernance: boolean;
        transparencyReporting: boolean;
        sustainableFunding: boolean;
        impactMeasurement: string;
        shortBio: string;
        organizationImage: string;
        organizationTags: string[];
        status: boolean;
        user: {
            id: number;
            created_at: Date;
            updated_at: Date;
            email: string;
            FullName: string;
            designation: string;
            profilePicture: string;
            isOrganization: boolean;
            organizationName: string | undefined;
            isEmailVerified: boolean;
        };
        followers: {
            id: number;
            created_at: Date;
            updated_at: Date;
            followedAt: Date;
            status: boolean;
            follower: {
                email: string;
                profilePicture: string;
                isOrganization: boolean;
                organizationName: string | undefined;
            };
        }[];
    }[]>;
    getOrganiserById(id: number): Promise<Organization>;
    followOrganization(orgId: number, user: any): Promise<{
        statusCode: number;
        message: string;
        data: {
            status: boolean;
            follow: OrganizationFollow;
        };
    }>;
    getOrganizationProfile(orgId: number, userId: number): Promise<{
        id: number;
        email: string;
        organizationName: string;
        organizationNameArabic: string;
        country: string;
        website: string;
        licenseFilePath: string;
        contactName: string;
        contactPhone: string;
        contactTitle: string;
        contactEmail: string;
        goodGovernance: boolean;
        transparencyReporting: boolean;
        sustainableFunding: boolean;
        impactMeasurement: string;
        shortBio: string;
        organizationImage: string;
        organizationTags: string[];
        user: {
            id: number;
            name: string | undefined;
            email: string;
        };
        followers: {
            id: number;
            email: string;
            fullName: string | null;
            status: boolean;
        }[];
        status: boolean;
    }>;
    getProjectCategoryByOrgAndProject(orgId: number, projectId: number): Promise<{
        organizationId: number;
        projectId: any;
        projectName: any;
        projectCategory: any;
        status: any;
        images: any;
        fundRaisingGoal: any;
        fundsCollected: any;
        location: any;
        startDate: any;
        finishDate: any;
        projectDescription: any;
        thumbnailImage: any;
        totalClicks: any;
        totalFilledPositions: any;
        positions: any;
        organization: {
            id: any;
            organizationName: any;
        };
    }>;
}
