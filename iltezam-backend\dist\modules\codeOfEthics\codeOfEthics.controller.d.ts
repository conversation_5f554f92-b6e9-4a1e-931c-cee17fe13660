import { CodeOfEthicsService } from './codeOfEthics.services';
import { CreateCodeOfEthicsDto } from './DTO/codeOfEthics.dto';
export declare class CodeOfEthicsController {
    private readonly codeOfEthicsService;
    constructor(codeOfEthicsService: CodeOfEthicsService);
    create(createCodeOfEthicsDto: CreateCodeOfEthicsDto): Promise<import("./Entity/codeOfEthics.entity").CodeOfEthics>;
    findAll(): Promise<import("./Entity/codeOfEthics.entity").CodeOfEthics[]>;
}
