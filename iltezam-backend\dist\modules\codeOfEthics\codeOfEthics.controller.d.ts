import { CodeOfEthicsService } from './codeOfEthics.services';
import { CreateCodeOfEthicsDto } from './DTO/codeOfEthics.dto';
export declare class CodeOfEthicsController {
    private readonly codeOfEthicsService;
    constructor(codeOfEthicsService: CodeOfEthicsService);
    findAll(): Promise<{
        statusCode: number;
        message: string;
        data: import("./Entity/codeOfEthics.entity").CodeOfEthics[];
    }>;
    create(createCodeOfEthicsDto: CreateCodeOfEthicsDto): Promise<import("./Entity/codeOfEthics.entity").CodeOfEthics>;
}
