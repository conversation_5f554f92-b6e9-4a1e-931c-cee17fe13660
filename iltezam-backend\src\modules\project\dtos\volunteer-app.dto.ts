import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsUUID,
  IsArray,
  IsOptional,
  IsNumber,
} from 'class-validator';

export class CreateVolunteerApplicationDto {
  @ApiProperty({ description: 'Project ID' })
  @IsNotEmpty()
  @IsNumber()
  projectId: number;

  @ApiProperty({ description: 'Position ID' })
  @IsNotEmpty()
  @IsNumber()
  positionId: number;

  @ApiProperty({
    description: 'CV or any related documents',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  CV?: string[];
}

export class VolunteerApplicationResponseDto {
  id: string;
  projectId: string;
  projectName: string;
  positionId: string;
  positionName: string;
  field: string;
  documents: string[];
  status: string;
  coverLetter?: string;
  experience?: string;
  skills?: string;
  notes?: string;
  createdAt: Date;
}
