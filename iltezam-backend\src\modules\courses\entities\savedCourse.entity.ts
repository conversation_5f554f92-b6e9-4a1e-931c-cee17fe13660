import { <PERSON><PERSON>ty, ManyToOne, Unique } from 'typeorm';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { Course } from './course.entity';

@Entity()
@Unique(['user', 'course'])
export class SavedCourse extends BaseEntityClass {
  @ManyToOne(() => User, { eager: true })
  user: User;

  @ManyToOne(() => Course, { eager: true })
  course: Course;
}
