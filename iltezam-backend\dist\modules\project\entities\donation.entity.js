"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Donation = exports.PaymentStatus = exports.PaymentFrequency = exports.PaymentMethod = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../auth/entities/base.entity");
const user_entity_1 = require("../../auth/entities/user.entity");
const project_entity_1 = require("./project.entity");
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["STRIPE"] = "stripe";
    PaymentMethod["CRYPTO"] = "crypto";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var PaymentFrequency;
(function (PaymentFrequency) {
    PaymentFrequency["ONE_TIME"] = "one_time";
    PaymentFrequency["MONTHLY"] = "monthly";
})(PaymentFrequency || (exports.PaymentFrequency = PaymentFrequency = {}));
var PaymentStatus;
(function (PaymentStatus) {
    PaymentStatus["SUCCESS"] = "success";
    PaymentStatus["FAILED"] = "failed";
    PaymentStatus["PENDING"] = "pending";
})(PaymentStatus || (exports.PaymentStatus = PaymentStatus = {}));
let Donation = class Donation extends base_entity_1.BaseEntityClass {
    donor;
    project;
    paymentMethod;
    frequency;
    amount;
    status;
    transactionId;
    paymentDate;
    notes;
};
exports.Donation = Donation;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'donor_id' }),
    __metadata("design:type", user_entity_1.User)
], Donation.prototype, "donor", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => project_entity_1.Project, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'project_id' }),
    __metadata("design:type", project_entity_1.Project)
], Donation.prototype, "project", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PaymentMethod,
    }),
    __metadata("design:type", String)
], Donation.prototype, "paymentMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PaymentFrequency,
    }),
    __metadata("design:type", String)
], Donation.prototype, "frequency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2 }),
    __metadata("design:type", Number)
], Donation.prototype, "amount", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: PaymentStatus,
        default: PaymentStatus.PENDING,
    }),
    __metadata("design:type", String)
], Donation.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Donation.prototype, "transactionId", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Date)
], Donation.prototype, "paymentDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Donation.prototype, "notes", void 0);
exports.Donation = Donation = __decorate([
    (0, typeorm_1.Entity)({ name: 'donations' })
], Donation);
//# sourceMappingURL=donation.entity.js.map