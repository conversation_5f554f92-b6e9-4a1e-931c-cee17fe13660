import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { CreatePaymentIntentDto } from './dtos/stripe-payment.dto';
import { SaveCardDetailsDto } from './dtos/save-card.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CardDetails } from './entities/card-details.entity';

@Injectable()
export class StripeService {
  private stripe: Stripe;

  constructor(
    private configService: ConfigService,
    @InjectRepository(CardDetails)
    private cardDetailsRepository: Repository<CardDetails>,
  ) {
    const stripeSecretKey = this.configService.get<string>('STRIPE_SECRET_KEY') || 
      'sk_test_51QNq2RCYzRt3u4ZllffIfrUv8GE2OJFduTU1OaxKLd9uwG6NTYfe0iKCaxJP4yzkz5ygtbmP3kQBOgTPtq9k5LwA00vWdM8dwC';
    
    this.stripe = new Stripe(stripeSecretKey);
  }

  /**
   * Create a payment intent
   */
  async createPaymentIntent(createPaymentIntentDto: CreatePaymentIntentDto) {
    try {
      const { amount, currency } = createPaymentIntentDto;
      
     if (amount && amount <= 0) throw new Error("Amount must be greater than 0");
      const convertedAmount = amount ? amount * 100 : 0; 

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: convertedAmount,
        currency: currency || 'usd',
        payment_method_types: ['card'],
      });

      return {
        clientSecret: paymentIntent.client_secret,
      };
    } catch (error) {
      throw new HttpException(
        `Failed to create payment intent: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Save card details
   */
  async saveCardDetails(saveCardDetailsDto: SaveCardDetailsDto, userId: number) {
    try {
      const { cardNumber, expiryMonth, expiryYear, cardHolderName,cvv } = saveCardDetailsDto;
      
      
      
      
      const cardDetails = this.cardDetailsRepository.create({
        userId,
        cardNumber,
        expiryMonth,
        expiryYear,
        cardHolderName,
        cvv,
      });
      
      await this.cardDetailsRepository.save(cardDetails);
      
      return {
        success: true,
        message: 'Card details saved successfully',
        cardDetails: {
          id: cardDetails.id,
          cardNumber,
          expiryMonth,
          expiryYear,
          cardHolderName,
          cvv
        },
      };
    } catch (error) {
      throw new HttpException(
        `Failed to save card details: ${error.message}`,
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Get saved cards for a user
   */
  async getSavedCards(userId: number) {
    try {
      const cards = await this.cardDetailsRepository.find({
        where: { userId },
      });
      
      return {
        success: true,
        cards,
      };
    } catch (error) {
      throw new HttpException(
        `Failed to get saved cards: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

    async handleWebhook(event: any): Promise<any> {
  console.log("here is hook event", event);

  switch (event.type) {
    case 'payment_intent.succeeded':
      console.log('✅ PaymentIntent was successful!');
      return { received: true, message: 'Payment succeeded' };

    case 'payment_intent.payment_failed':
      console.log('❌ Payment failed');
      return { received: true, message: 'Payment failed' };

    case 'payment_intent.processing':
    case 'payment_intent.payment_processing': 
      console.log('⏳ Payment processing');
      return { received: true, message: 'Payment processing' };

    default:
      console.log(`⚠️ Unhandled event type: ${event.type}`);
      return { received: true, message: 'Event type not handled or unknown' };
  }
}


   constructEvent(payload: any, signature: string): Stripe.Event {
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!endpointSecret) {
      throw new Error('STRIPE_WEBHOOK_SECRET is not defined');
    }
    return this.stripe.webhooks.constructEvent(
      typeof payload === 'string' ? payload : JSON.stringify(payload),
      signature,
      endpointSecret
    );
  }
}
