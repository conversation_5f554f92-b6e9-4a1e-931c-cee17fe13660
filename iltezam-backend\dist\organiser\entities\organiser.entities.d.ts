import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { OrganizationFollow } from './organization.follow.entities';
import { Project } from 'src/modules/project/entities/project.entity';
export declare class Organization extends BaseEntityClass {
    email: string;
    organizationName: string;
    profilePicture: string;
    organizationNameArabic: string;
    country: string;
    website: string;
    licenseFilePath: string;
    contactName: string;
    contactPhone: string;
    contactTitle: string;
    contactEmail: string;
    goodGovernance: boolean;
    transparencyReporting: boolean;
    sustainableFunding: boolean;
    impactMeasurement: string;
    shortBio: string;
    organizationImage: string;
    organizationTags: string[];
    user: User;
    followers: OrganizationFollow[];
    projects: Project[];
}
