import { ConfigService } from '@nestjs/config';
import { CreateCryptoPaymentDto } from './dtos/crypto-payment.dto';
import { CryptoPaymentResponseDto, SupportedCurrenciesResponseDto, TransactionStatusResponseDto } from './dtos/crypto-payment-response.dto';
export declare class CryptopaymentService {
    private configService;
    private coinpaymentsClient;
    constructor(configService: ConfigService);
    createPayment(createPaymentDto: CreateCryptoPaymentDto): Promise<CryptoPaymentResponseDto>;
    getSupportedCurrencies(): Promise<SupportedCurrenciesResponseDto>;
    getTransactionStatus(txnId: string): Promise<TransactionStatusResponseDto>;
}
