import { Repository } from 'typeorm';
import { UpdateProfileDto } from './dtos/create-profile.dto';
import { Profile } from './entities/profile.entity';
import { ProfileConnection } from './entities/profile.connection.entity';
import { User } from '../auth/entities/user.entity';
import { ChatRoom } from '../live-chat/entities/chat-room.entity';
import { OrganizationFollow } from 'src/organiser/entities/organization.follow.entities';
export declare class ProfileService {
    private profileRepository;
    private readonly connectionRepository;
    private userRepository;
    private chatRoomRepository;
    private orgFollowRepository;
    constructor(profileRepository: Repository<Profile>, connectionRepository: Repository<ProfileConnection>, userRepository: Repository<User>, chatRoomRepository: Repository<ChatRoom>, orgFollowRepository: Repository<OrganizationFollow>);
    getByUserId(userId: number, countsOnly?: boolean): Promise<any>;
    getUserByUserId(userId: number): Promise<Profile>;
    updateByUserId(userId: number, updateProfileDto: UpdateProfileDto): Promise<Profile>;
    findOne(id: number): Promise<Profile>;
    connectProfiles(requesterUserId: number, receiverUserId: number): Promise<ProfileConnection>;
    acceptConnection(connectionId: number): Promise<ProfileConnection>;
    rejectConnection(connectionId: number): Promise<ProfileConnection>;
    getConnections(userId: number): Promise<{
        users: {
            user: {
                id: number;
                email: string;
                FullName: string;
                designation: string;
                profilePicture: string;
                isOrganization: boolean;
                organizationName: string | undefined;
                created_at: Date;
                updated_at: Date;
            };
            connectionId: number;
            status: "pending" | "accepted" | "rejected" | "disconnected";
            createdAt: Date;
            updatedAt: Date;
        }[];
    }>;
}
