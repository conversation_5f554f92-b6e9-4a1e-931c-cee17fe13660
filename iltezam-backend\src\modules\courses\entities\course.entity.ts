import { IsNotEmpty } from 'class-validator';
import { Enti<PERSON>, Column, OneToMany, JoinColumn, ManyToOne } from 'typeorm';
import { Topic } from './topic.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { Enrollment } from './enrollment.entity';

@Entity()
export class Course extends BaseEntityClass {
  @Column()
  @IsNotEmpty({ message: 'Course name is required' })
  courseName: string;

  @Column({ default: 'english' })
  @IsNotEmpty({ message: 'language is required' })
  language: string;

  @Column('text')
  @IsNotEmpty()
  whatTheyLearn: string;

  @Column('text', { array: true, nullable: true })
  topicsTag: string[];

  @Column('text', { array: true, nullable: true })
  tags: string[];

  @Column('text', { nullable: true })
  thumbnailUrl: string;

  @Column({ default: 'active' })
  status: 'active' | 'closed';

  @Column({ nullable: true })
  estimatedDuration: string; // e.g., '4 hours'

  @Column({ nullable: true })
  previewVideoUrl: string;

  @ManyToOne(() => User, (user) => user.courses, { onDelete: 'CASCADE' })
  createdBy: User;

  @OneToMany(() => Topic, (topic) => topic.courses, { cascade: true })
  topics: Topic[];

  @OneToMany(() => Enrollment, (enrollment) => enrollment.course)
  enrollments: Enrollment[];
}
