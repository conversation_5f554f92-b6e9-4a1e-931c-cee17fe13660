"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadMiddleware = void 0;
const common_1 = require("@nestjs/common");
const multer = require("multer");
const path_1 = require("path");
let UploadMiddleware = class UploadMiddleware {
    upload = multer({
        storage: multer.diskStorage({
            destination: './uploads/universities',
            filename: (req, file, callback) => {
                const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
                const ext = (0, path_1.extname)(file.originalname);
                callback(null, `${uniqueSuffix}${ext}`);
            }
        })
    }).single('logo_url');
    use(req, res, next) {
        this.upload(req, res, (err) => {
            if (err) {
                return res.status(400).json({ message: err.message });
            }
            next();
        });
    }
};
exports.UploadMiddleware = UploadMiddleware;
exports.UploadMiddleware = UploadMiddleware = __decorate([
    (0, common_1.Injectable)()
], UploadMiddleware);
//# sourceMappingURL=upload.middleware.js.map