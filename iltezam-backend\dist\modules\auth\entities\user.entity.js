"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("./base.entity");
const post_entities_1 = require("../../post/entities/post.entities");
const organiser_entities_1 = require("../../../organiser/entities/organiser.entities");
const enrollment_entity_1 = require("../../courses/entities/enrollment.entity");
const profile_entity_1 = require("../../profile/entities/profile.entity");
const notification_entity_1 = require("../../notification/entities/notification.entity");
const course_progress_entity_1 = require("../../courses/entities/course-progress.entity");
const course_entity_1 = require("../../courses/entities/course.entity");
const quiz_entity_1 = require("../../courses/entities/quiz.entity");
const profile_connection_entity_1 = require("../../profile/entities/profile.connection.entity");
const chapter_progress_entity_1 = require("../../courses/entities/chapter-progress.entity");
let User = class User extends base_entity_1.BaseEntityClass {
    email;
    FullName;
    password;
    designation;
    profilePicture;
    isOrganization;
    isFormFillUp;
    organizationName;
    isEmailVerified;
    verificationCode;
    resetPasswordToken;
    forgotPasswordTokenMatch;
    resetPasswordTokenExpiry;
    courseProgress;
    savedCourses;
    courses;
    chapterProgress;
    quizzes;
    enrollments;
    posts;
    likedPosts;
    sharedPosts;
    organizationDetails;
    profile;
    notifications;
    sentConnections;
    receivedConnections;
};
exports.User = User;
__decorate([
    (0, typeorm_1.Column)({ unique: true }),
    __metadata("design:type", String)
], User.prototype, "email", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false, nullable: true }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "FullName", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], User.prototype, "password", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", String)
], User.prototype, "designation", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "profilePicture", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isOrganization", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isFormFillUp", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "organizationName", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "isEmailVerified", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "verificationCode", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], User.prototype, "resetPasswordToken", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: false }),
    __metadata("design:type", Boolean)
], User.prototype, "forgotPasswordTokenMatch", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamptz', nullable: true }),
    __metadata("design:type", Date)
], User.prototype, "resetPasswordTokenExpiry", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => course_progress_entity_1.CourseProgress, (progress) => progress.user),
    __metadata("design:type", Array)
], User.prototype, "courseProgress", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => course_entity_1.Course),
    (0, typeorm_1.JoinTable)(),
    __metadata("design:type", Array)
], User.prototype, "savedCourses", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => course_entity_1.Course, (course) => course.createdBy),
    __metadata("design:type", Array)
], User.prototype, "courses", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => chapter_progress_entity_1.ChapterProgress, (chapterProgress) => chapterProgress.user),
    __metadata("design:type", Array)
], User.prototype, "chapterProgress", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => quiz_entity_1.Quiz, (quiz) => quiz.createdBy),
    __metadata("design:type", Array)
], User.prototype, "quizzes", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => enrollment_entity_1.Enrollment, (enrollment) => enrollment.user),
    __metadata("design:type", Array)
], User.prototype, "enrollments", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => post_entities_1.Post, (post) => post.user),
    __metadata("design:type", Array)
], User.prototype, "posts", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => post_entities_1.Post, (post) => post.likedUsers),
    __metadata("design:type", Array)
], User.prototype, "likedPosts", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => post_entities_1.Post, (post) => post.sharedUsers),
    __metadata("design:type", Array)
], User.prototype, "sharedPosts", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => organiser_entities_1.Organization, (org) => org.user),
    __metadata("design:type", organiser_entities_1.Organization)
], User.prototype, "organizationDetails", void 0);
__decorate([
    (0, typeorm_1.OneToOne)(() => profile_entity_1.Profile, (profile) => profile.user),
    (0, typeorm_1.JoinColumn)(),
    __metadata("design:type", profile_entity_1.Profile)
], User.prototype, "profile", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => notification_entity_1.Notification, (notification) => notification.user),
    __metadata("design:type", Array)
], User.prototype, "notifications", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => profile_connection_entity_1.ProfileConnection, (connection) => connection.requester),
    __metadata("design:type", Array)
], User.prototype, "sentConnections", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => profile_connection_entity_1.ProfileConnection, (connection) => connection.receiver),
    __metadata("design:type", Array)
], User.prototype, "receivedConnections", void 0);
exports.User = User = __decorate([
    (0, typeorm_1.Entity)({ name: 'users' })
], User);
//# sourceMappingURL=user.entity.js.map