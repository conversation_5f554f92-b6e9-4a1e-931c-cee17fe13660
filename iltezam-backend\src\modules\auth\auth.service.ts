import {
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { User } from './entities/user.entity';
import { Repository, <PERSON><PERSON>han } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcryptjs';
import { AuthLoginDto, CompanyDto } from './dtos/auth.dto';
import { RegisterDto } from './dtos/auth.dto';
import { v4 as uuidv4 } from 'uuid';

import { CustomError } from '../../core/utils/CustomError';
import { Profile } from '../profile/entities/profile.entity';
import { CreateProfileDto } from '../profile/dtos/create-profile.dto';
import { ProfileConnection } from '../profile/entities/profile.connection.entity';
import { MailService } from 'src/core/utils/sendemail';
import { Company } from './entities/company.entity';
import { SubscribedEmail } from './entities/subscribe.entity';
import { Organization } from '../../organiser/entities/organiser.entities';
@Injectable()
export class AuthService {
  transporter: any;

  constructor(
    private jwtService: JwtService,
    private emailService: MailService,
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(Profile) private profileRepository: Repository<Profile>,
    @InjectRepository(Company)
    private companyRepository: Repository<Company>,
    private readonly mailService: MailService,
    @InjectRepository(SubscribedEmail)
    private subscribedEmailRepository: Repository<SubscribedEmail>,
    @InjectRepository(ProfileConnection)
    private connectionRepository: Repository<ProfileConnection>,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
  ) {}

  private generateVerificationCode(): string {
    return Math.random().toString(36).substring(2, 6).toUpperCase();
  }

  async loginUser(payload: AuthLoginDto) {
    try {
      const user = await this.userRepository.findOne({
        where: { email: payload.email },
      });

      if (!user) {
        throw new HttpException('Account does not exist', 404);
      }

      const isPasswordValid = await bcrypt.compare(
        payload.password,
        user.password,
      );
      if (!isPasswordValid) {
        throw new HttpException('Incorrect password', 401);
      }

      if (!user.isEmailVerified) {
        const newCode = this.generateVerificationCode();
        user.verificationCode = newCode;
        await this.userRepository.save(user);

        await this.emailService.sendVerificationEmail(user.email, newCode);

        throw new HttpException(
          'Email is not verified. Please verify your email.',
          400,
        );
      }

      const token = await this.signToken(user);
      const { password, ...userWithoutPassword } = user;

      let organization: Organization | null = null;
      if (user.isOrganization) {
        organization = await this.organizationRepository.findOne({
          where: { user: { id: user.id } },
          relations: ['user'],
        });
        if (!organization) {
          console.warn(`Organization not found for user ID: ${user.id}`);
        } else {
          console.log('Fetched organization:', organization);
        }
      }

      return {
        user: userWithoutPassword,
        organization,
        token,
      };
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Internal server error', 500);
    }
  }

  private signToken(checkUserExist: User) {
    const jwtPayload = {
      id: checkUserExist.id,
      email: checkUserExist.email,
    };

    return this.jwtService.sign(jwtPayload);
  }

  async registerUser(payload: RegisterDto) {
    try {
      const {
        email,
        password,
        confirmPassword,
        isOrganization,
        organizationName,
        FullName,
        designation,
      } = payload;

      if (!email || !password || !confirmPassword) {
        throw new HttpException(
          'Email, password and confirm password are required',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (password !== confirmPassword) {
        throw new HttpException(
          'Passwords do not match',
          HttpStatus.BAD_REQUEST,
        );
      }

      const existingUser = await this.userRepository.findOne({
        where: { email },
      });
      if (existingUser) {
        throw new HttpException(
          'User email already exists',
          HttpStatus.BAD_REQUEST,
        );
      }

      // 4. Validate based on user type
      if (isOrganization) {
        if (!organizationName || organizationName.trim() === '') {
          throw new HttpException(
            'Organization name is required for organizers',
            HttpStatus.BAD_REQUEST,
          );
        }
      } else {
        if (!FullName || FullName.trim() === '') {
          throw new HttpException(
            'Full name is required for individuals',
            HttpStatus.BAD_REQUEST,
          );
        }
        if (!designation || designation.trim() === '') {
          throw new HttpException(
            'Designation is required for individuals',
            HttpStatus.BAD_REQUEST,
          );
        }
      }

      const verificationCode = this.generateVerificationCode();

      console.log('Verification Code:', verificationCode);

      const userData: Partial<User> = {
        email: email.toLowerCase(),
        password: await bcrypt.hash(password, 8),
        isOrganization,
        verificationCode,
        FullName: FullName,
      };

      console.log('userData:', userData);

      if (isOrganization) {
        userData.organizationName = organizationName;
      } else {
        userData.designation = designation;
      }

      const newUser = this.userRepository.create(userData);
      await this.userRepository.save(newUser);

      // creating a profile
      const newProfile = this.profileRepository.create({
        fullName: FullName,
        organizationName,
        email: email.toLowerCase(),
        user: newUser,
        bio: '',
        profilePicture: '',
        location: '',
        phone: '',
      });
      console.log('newProfile:', newProfile);
      await this.profileRepository.save(newProfile);
      newUser.profile = newProfile;
      await this.userRepository.save(newUser);

      await this.emailService.sendVerificationEmail(email, verificationCode);
      console.log('Verification email sent to:', email);
      // const token = await this.signToken(newUser);
      // console.log('Generated Token:', token);

      const responseData = isOrganization
        ? {
            isOrganization: true,
            organizationName,
            email,
            password,
            confirmPassword,
          }
        : {
            isOrganization: false,
            FullName,
            email,
            password,
            confirmPassword,
            designation,
          };
      const dataaa = {
        user: responseData,
        // token: token,
      };

      console.log('Response Data:', responseData);
      // console.log('token:', token);
      return {
        data: dataaa,
        message: 'Registered successfully.',
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async forgotPassword(email: string) {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    const resetToken = this.generateVerificationCode();
    const expiry = new Date(Date.now() + 1000 * 60 * 30);
    console.log('Reset Token:', resetToken);

    user.resetPasswordToken = resetToken;
    user.resetPasswordTokenExpiry = expiry;

    await this.userRepository.save(user);
    await this.emailService.sendResetPasswordEmail(email, resetToken);

    return { message: 'Reset password email sent', data: resetToken };
  }

  async verifyResetCode(email: string, code: string): Promise<void> {
    const user = await this.userRepository.findOne({
      where: {
        email,
        resetPasswordToken: code,
      },
    });

    if (!user) {
      throw new BadRequestException('Invalid or expired reset code');
    }

    user.forgotPasswordTokenMatch = true;
    await this.userRepository.save(user);
  }

  async resetPassword(email: string, newPassword: string): Promise<void> {
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user || !user.forgotPasswordTokenMatch) {
      throw new BadRequestException('Reset code not verified');
    }

    user.password = await bcrypt.hash(newPassword, 10);
    user.resetPasswordToken = '';
    user.forgotPasswordTokenMatch = false;

    await this.userRepository.save(user);
  }

  async verifyEmail(payload: {
    email: string;
    code: string;
  }): Promise<{ message: string }> {
    const { email, code } = payload;
    console.log('Verifying email:', email, 'with code:', code);

    const user = await this.userRepository.findOne({
      where: {
        email,
        verificationCode: code,
      },
    });
    console.log('User found:', user);

    if (!user) {
      throw new CustomError('Invalid verification code or email', 400);
    }

    if (user.isEmailVerified) {
      throw new CustomError('Email is already verified', 400);
    }

    user.isEmailVerified = true;
    user.verificationCode = '';
    await this.userRepository.save(user);

    return { message: 'Email verified successfully' };
  }

  async resendVerificationEmail(email: string): Promise<any> {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new CustomError('User not found', 404);
    }

    if (user.isEmailVerified) {
      throw new CustomError('Email already verified', 400);
    }

    user.verificationCode = this.generateVerificationCode();
    await this.userRepository.save(user);
    console.log('Verification code:', user.verificationCode);

    //  await this.mailService.sendVerificationEmail(
    //   user.email,
    //  user.verificationCode,
    // );

    return { message: 'Verification email sent successfully' };
  }

  // async sendVerificationEmail(
  //   email: string,
  //   verificationCode: string,
  // ): Promise<void> {
  //   await this.transporter.sendMail({
  //     from: `ELTEZAM <${process.env.EMAIL}>`,
  //     to: email,
  //     subject: 'Your ELTEZAM Verification Code',
  //     html: `
  //       <p>Thank you for registering with ELTEZAM!</p>
  //       <p>Your verification code is:</p>
  //       <h2>${verificationCode}</h2>
  //       <p>Please enter this code in the app to verify your email address.</p>
  //       <p>If you didn't create an account, please ignore this email.</p>
  //       <br>
  //       <p>Best,<br>The ELTEZAM Team</p>
  //     `,
  //   });
  // }

  async getAllUsers(userId: number | null, search?: string) {
    try {
      const query = this.userRepository
        .createQueryBuilder('user')
        .leftJoinAndSelect('user.profile', 'profile')
        .orderBy('user.id', 'ASC');

      if (search) {
        query.andWhere('user.FullName ILIKE :search', {
          search: `%${search}%`,
        });
      }

      const users = await query.getMany();
      console.log('Fetched users count:', users.length);
      console.log('Fetched user IDs:', users.map(u => u.id));

      let connections: ProfileConnection[] = [];

      if (userId) {
        connections = await this.connectionRepository.find({
          where: [
            { requester: { id: userId } },
            { receiver: { id: userId } },
          ],
          relations: ['requester', 'receiver'],
        });
      }

      return users.map((user: any) => {
        const { password, ...userInfo } = user;

        let connectionStatus = 'Not Connected';

        if (userId && userId !== user.id) {
          const connection = (connections as ProfileConnection[]).find(
            (conn: ProfileConnection) =>
              (conn.requester.id === userId && conn.receiver.id === user.id) ||
              (conn.receiver.id === userId && conn.requester.id === user.id),
          );

          if (connection) {
            connectionStatus =
              connection.status.charAt(0).toUpperCase() +
              connection.status.slice(1).toLowerCase();
          }
        }

        userInfo.connectionStatus = connectionStatus;
        return userInfo;
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createCompany(companyDto: CompanyDto) {
    const existingCompany = await this.companyRepository.findOne({
      where: { email: companyDto.email },
    });

    if (existingCompany) {
      throw new BadRequestException('Company with this email already exists');
    }

    const newCompany = this.companyRepository.create(companyDto);
    const savedCompany = await this.companyRepository.save(newCompany);

    return {
      message: 'Company created successfully',
      data: savedCompany,
    };
  }

  async emailSubscribe(email: string): Promise<any> {
    try {
      console.log('Starting email subscription process for:', email);

      const existingSubscription = await this.subscribedEmailRepository.findOne(
        {
          where: { email },
        },
      );

      if (existingSubscription) {
        console.log('Email already subscribed:', email);
        throw new BadRequestException('Email already subscribed');
      }

      const newSubscription = this.subscribedEmailRepository.create({
        email: email.toLowerCase(),
      });

      await this.subscribedEmailRepository.save(newSubscription);
      console.log('Subscription saved to database:', newSubscription.email);

      console.log('Attempting to send subscription email...');
      await this.mailService.sendSubscriptionEmail(newSubscription.email);
      console.log('Subscription email sent successfully');

      return {
        message: 'Email subscribed successfully',
        data: { email: newSubscription.email },
      };
    } catch (error) {
      console.error('Error in emailSubscribe:', error);
      throw error;
    }
  }
}
