"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CryptopaymentController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const cryptopayment_service_1 = require("./cryptopayment.service");
const crypto_payment_dto_1 = require("./dtos/crypto-payment.dto");
const crypto_payment_response_dto_1 = require("./dtos/crypto-payment-response.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let CryptopaymentController = class CryptopaymentController {
    cryptopaymentService;
    constructor(cryptopaymentService) {
        this.cryptopaymentService = cryptopaymentService;
    }
    async createPayment(createPaymentDto) {
        return this.cryptopaymentService.createPayment(createPaymentDto);
    }
    async getSupportedCurrencies() {
        return this.cryptopaymentService.getSupportedCurrencies();
    }
    async getTransactionStatus(txnId) {
        return this.cryptopaymentService.getTransactionStatus(txnId);
    }
};
exports.CryptopaymentController = CryptopaymentController;
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create crypto payment transaction',
        description: 'Creates a new crypto payment transaction with QR code and payment link'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Payment transaction created successfully',
        type: crypto_payment_response_dto_1.CryptoPaymentResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid payment data or API error'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [crypto_payment_dto_1.CreateCryptoPaymentDto]),
    __metadata("design:returntype", Promise)
], CryptopaymentController.prototype, "createPayment", null);
__decorate([
    (0, common_1.Get)('currencies'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get supported cryptocurrencies',
        description: 'Returns list of all supported cryptocurrencies for payments'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Supported currencies retrieved successfully',
        type: crypto_payment_response_dto_1.SupportedCurrenciesResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Failed to retrieve currencies'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CryptopaymentController.prototype, "getSupportedCurrencies", null);
__decorate([
    (0, common_1.Get)('transaction/:txnId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get transaction status',
        description: 'Get the current status of a crypto payment transaction'
    }),
    (0, swagger_1.ApiParam)({
        name: 'txnId',
        description: 'Transaction ID from CoinPayments',
        example: 'CPBF23CBUSHKKOMV1OPMRBNEFV'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Transaction status retrieved successfully',
        type: crypto_payment_response_dto_1.TransactionStatusResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid transaction ID or API error'
    }),
    __param(0, (0, common_1.Param)('txnId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CryptopaymentController.prototype, "getTransactionStatus", null);
exports.CryptopaymentController = CryptopaymentController = __decorate([
    (0, swagger_1.ApiTags)('Crypto Payment'),
    (0, common_1.Controller)('cryptopayment'),
    __metadata("design:paramtypes", [cryptopayment_service_1.CryptopaymentService])
], CryptopaymentController);
//# sourceMappingURL=cryptopayment.controller.js.map