export declare class CreatePostDto {
    content: string;
    imageUrl?: string[];
    videoUrl?: string[];
    PostImage?: string;
    visibility?: 'public' | 'private';
    likes?: number;
    shares?: number;
    professional?: string;
}
export declare class LikePostDto {
    post_id: number;
    likes?: number;
    dislikes?: number;
}
export declare class sharePostDto {
    post_id: number;
    shares?: number;
}
export declare class UpdatePostDto {
    content?: string;
    imageUrl?: string[];
    videoUrl?: string[];
    visibility?: 'public' | 'private';
    likes?: number;
    shares?: number;
    professional?: string;
}
export declare class CreateCommentDto {
    content: string;
    post_id: number;
    user_id: number;
}
