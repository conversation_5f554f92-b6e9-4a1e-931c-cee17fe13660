import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';

@Entity()
export class CodeOfEthics extends BaseEntityClass {
  @Column()
  image: string;

  @Column()
  organizationName: string;

  @Column()
  organizationImage: string;

  @Column()
  title: string;

  @Column()
  description: string;

  @Column()
  student: number;

  @Column('simple-array')
  languages: string[];
}
