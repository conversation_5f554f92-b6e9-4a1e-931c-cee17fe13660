import { <PERSON>umn, CreateDateColumn, Entity, ManyToOne, Unique } from 'typeorm';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { Course } from './course.entity';

@Entity()
@Unique(['user', 'course'])
export class Enrollment extends BaseEntityClass {
  @ManyToOne(() => User, (user) => user.enrollments, { eager: true })
  user: User;

  @ManyToOne(() => Course, (course) => course.enrollments, { eager: true })
  course: Course;

  @CreateDateColumn()
  enrolledAt: Date;

  @Column({ default: true })
  isEnroll: boolean;
}
