import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// Entities
import { Project } from './entities/project.entity';
import { Position } from './entities/position.entity';
import { Donation } from './entities/donation.entity';
import { VolunteerApplication } from './entities/volunteer-application.entity';
import { User } from '../auth/entities/user.entity';

// Controllers and Services
import { ProjectController } from './project.controller';
import { ProjectService } from './project.service';
import { Organization } from 'src/organiser/entities/organiser.entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Project,
      Position,
      Donation,
      VolunteerApplication,
      User,
      Organization
    ]),
  ],
  controllers: [ProjectController],
  providers: [ProjectService],
  exports: [ProjectService],
})
export class ProjectModule {}
