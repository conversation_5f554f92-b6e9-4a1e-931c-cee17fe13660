import { JwtService } from '@nestjs/jwt';
import { User } from './entities/user.entity';
import { Repository } from 'typeorm';
import { AuthLoginDto, CompanyDto } from './dtos/auth.dto';
import { RegisterDto } from './dtos/auth.dto';
import { MailService } from '../../core/utils/sendemail';
import { Profile } from '../profile/entities/profile.entity';
import { ProfileConnection } from '../profile/entities/profile.connection.entity';
import { Company } from './entities/company.entity';
import { SubscribedEmail } from './entities/subscribe.entity';
export declare class AuthService {
    private jwtService;
    private userRepository;
    private profileRepository;
    private companyRepository;
    private readonly mailService;
    private subscribedEmailRepository;
    transporter: any;
    constructor(jwtService: JwtService, userRepository: Repository<User>, profileRepository: Repository<Profile>, companyRepository: Repository<Company>, mailService: MailService, subscribedEmailRepository: Repository<SubscribedEmail>);
    private generateVerificationCode;
    loginUser(payload: AuthLoginDto): Promise<{
        user: {
            email: string;
            FullName: string;
            designation: string;
            profilePicture: string;
            isOrganization: boolean;
            isFormFillUp: boolean;
            organizationName?: string;
            isEmailVerified: boolean;
            verificationCode: string;
            resetPasswordToken: string;
            forgotPasswordTokenMatch: boolean;
            resetPasswordTokenExpiry: Date;
            courseProgress: import("../courses/entities/course-progress.entity").CourseProgress[];
            savedCourses: import("../courses/entities/course.entity").Course[];
            courses: import("../courses/entities/course.entity").Course[];
            chapterProgress: import("../courses/entities/chapter-progress.entity").ChapterProgress[];
            quizzes: import("../courses/entities/quiz.entity").Quiz[];
            enrollments: import("../courses/entities/enrollment.entity").Enrollment[];
            posts: import("../post/entities/post.entities").Post[];
            likedPosts: import("../post/entities/post.entities").Post[];
            sharedPosts: import("../post/entities/post.entities").Post[];
            organizationDetails: import("../../organiser/entities/organiser.entities").Organization;
            profile: Profile;
            notifications: import("../notification/entities/notification.entity").Notification[];
            sentConnections: ProfileConnection[];
            receivedConnections: ProfileConnection[];
            id: number;
            created_at: Date;
            updated_at: Date;
        };
        token: string;
    }>;
    private signToken;
    registerUser(payload: RegisterDto): Promise<{
        data: {
            user: {
                isOrganization: boolean;
                organizationName: string | undefined;
                email: string;
                password: string;
                confirmPassword: string;
                FullName?: undefined;
                designation?: undefined;
            } | {
                isOrganization: boolean;
                FullName: string;
                email: string;
                password: string;
                confirmPassword: string;
                designation: string | undefined;
                organizationName?: undefined;
            };
        };
        message: string;
    }>;
    forgotPassword(email: string): Promise<{
        message: string;
        data: string;
    }>;
    verifyResetCode(email: string, code: string): Promise<void>;
    resetPassword(email: string, newPassword: string): Promise<void>;
    verifyEmail(payload: {
        email: string;
        code: string;
    }): Promise<{
        message: string;
    }>;
    resendVerificationEmail(email: string): Promise<any>;
    sendVerificationEmail(email: string, verificationCode: string): Promise<void>;
    getAllUsers(currentUserId: string, search?: string): Promise<any[]>;
    createCompany(companyDto: CompanyDto): Promise<{
        message: string;
        data: Company;
    }>;
    emailSubscribe(email: string): Promise<any>;
}
