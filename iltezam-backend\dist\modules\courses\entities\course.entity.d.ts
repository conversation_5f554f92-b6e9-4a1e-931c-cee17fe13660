import { Topic } from './topic.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { Enrollment } from './enrollment.entity';
export declare class Course extends BaseEntityClass {
    courseName: string;
    language: string;
    whatTheyLearn: string;
    topicsTag: string[];
    tags: string[];
    thumbnailUrl: string;
    status: 'active' | 'closed';
    estimatedDuration: string;
    previewVideoUrl: string;
    createdBy: User;
    topics: Topic[];
    enrollments: Enrollment[];
}
