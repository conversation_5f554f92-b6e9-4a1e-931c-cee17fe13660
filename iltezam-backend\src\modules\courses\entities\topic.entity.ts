import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, OneToMany, Unique } from 'typeorm';
import { Course } from './course.entity';
import { Chapter } from './chapter.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { IsNotEmpty } from 'class-validator';

@Entity()
// @Unique(['user', 'quiz', 'attemptNumber']) // prevent duplicate attempt entries
export class Topic extends BaseEntityClass {
  @Column()
  @IsNotEmpty()
  topicTitle: string;

  @Column()
  @IsNotEmpty()
  numberOfChapters: number;

  @ManyToOne(() => Course, (course) => course.topics, { onDelete: 'CASCADE' })
  courses: Course;

  @OneToMany(() => Chapter, (chapter) => chapter.topics, { cascade: true })
  chapters: Chapter[];
}
