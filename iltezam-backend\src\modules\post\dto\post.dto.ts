import { IsString, <PERSON>Optional, IsEnum, IsNumber, IsInt, IsPositive, IsNotEmpty, IsArray } from 'class-validator';

export class CreatePostDto {
  @IsString()
  content: string;

   @IsOptional()
  @IsArray()
  @IsString({ each: true })
  imageUrl?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  videoUrl?: string[];


   @IsOptional()
  @IsString()
    PostImage?: string;


  @IsOptional()
  @IsEnum(['public', 'private'], {
    message: 'Visibility must be either "public" or "private"',
  })
  visibility?: 'public' | 'private';

  @IsOptional()
  @IsInt()
  @IsPositive()
  likes?: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  shares?: number;

  @IsOptional()
  @IsString()
  professional?: string;


  

}


export class LikePostDto {
  @IsNumber()
  post_id: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  likes?: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  dislikes?: number;
}

export class sharePostDto {
  @IsNumber()
  post_id: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  shares?: number;
}

export class UpdatePostDto {
  @IsOptional()
  @IsString()
  content?: string;

 @IsOptional()
@IsArray()
@IsString({ each: true })
imageUrl?: string[];

@IsOptional()
@IsArray()
@IsString({ each: true })
videoUrl?: string[];

  @IsOptional()
  @IsEnum(['public', 'private'], {
    message: 'Visibility must be either "public" or "private"',
  })
  visibility?: 'public' | 'private';


  @IsOptional()
  @IsInt()
  @IsPositive()
  likes?: number;

  @IsOptional()
  @IsInt()
  @IsPositive()
  shares?: number;

  @IsOptional()
  @IsString()
  professional?: string;
}

export class CreateCommentDto {
  @IsNotEmpty()
  @IsString()
  content: string;

  @IsNotEmpty()
  @IsNumber()
  post_id: number;

  @IsNotEmpty()
  @IsNumber()
  user_id: number;
}
