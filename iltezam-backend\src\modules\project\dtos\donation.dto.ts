import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsUUID,
} from 'class-validator';
import { PaymentMethod, PaymentFrequency } from '../entities/donation.entity';

export class CreateDonationDto {
  @ApiProperty({ description: 'Project ID' })
  @IsNotEmpty()
  @IsUUID()
  projectId: string;

  @ApiProperty({
    description: 'Payment method',
    enum: PaymentMethod,
  })
  @IsNotEmpty()
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({
    description: 'Payment frequency',
    enum: PaymentFrequency,
  })
  @IsNotEmpty()
  @IsEnum(PaymentFrequency)
  frequency: PaymentFrequency;

  @ApiProperty({ description: 'Donation amount' })
  @IsNotEmpty()
  @IsNumber()
  amount: number;

  @ApiProperty({ description: 'Additional notes', required: false })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class DonationResponseDto {
  id: string;
  projectId: string;
  projectName: string;
  paymentMethod: PaymentMethod;
  frequency: PaymentFrequency;
  amount: number;
  status: string;
  transactionId?: string;
  paymentDate?: Date;
  notes?: string;
}
