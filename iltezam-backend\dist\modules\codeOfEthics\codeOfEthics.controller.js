"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeOfEthicsController = void 0;
const common_1 = require("@nestjs/common");
const codeOfEthics_services_1 = require("./codeOfEthics.services");
const codeOfEthics_dto_1 = require("./DTO/codeOfEthics.dto");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let CodeOfEthicsController = class CodeOfEthicsController {
    codeOfEthicsService;
    constructor(codeOfEthicsService) {
        this.codeOfEthicsService = codeOfEthicsService;
    }
    async create(createCodeOfEthicsDto) {
        return await this.codeOfEthicsService.create(createCodeOfEthicsDto);
    }
    async findAll() {
        return await this.codeOfEthicsService.findAll();
    }
};
exports.CodeOfEthicsController = CodeOfEthicsController;
__decorate([
    (0, common_1.Post)('create'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [codeOfEthics_dto_1.CreateCodeOfEthicsDto]),
    __metadata("design:returntype", Promise)
], CodeOfEthicsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)('all'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CodeOfEthicsController.prototype, "findAll", null);
exports.CodeOfEthicsController = CodeOfEthicsController = __decorate([
    (0, swagger_1.ApiTags)('Code of Ethics'),
    (0, common_1.Controller)('code-of-ethics'),
    __metadata("design:paramtypes", [codeOfEthics_services_1.CodeOfEthicsService])
], CodeOfEthicsController);
//# sourceMappingURL=codeOfEthics.controller.js.map