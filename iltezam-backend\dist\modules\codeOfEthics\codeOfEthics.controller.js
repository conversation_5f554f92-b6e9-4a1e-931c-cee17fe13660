"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeOfEthicsController = void 0;
const common_1 = require("@nestjs/common");
const codeOfEthics_services_1 = require("./codeOfEthics.services");
const codeOfEthics_dto_1 = require("./DTO/codeOfEthics.dto");
const swagger_1 = require("@nestjs/swagger");
const publicRoute_decorator_1 = require("../../shared/decorators/publicRoute.decorator");
let CodeOfEthicsController = class CodeOfEthicsController {
    codeOfEthicsService;
    constructor(codeOfEthicsService) {
        this.codeOfEthicsService = codeOfEthicsService;
    }
    async findAll() {
        try {
            const result = await this.codeOfEthicsService.findAllCodeOfEthics();
            return {
                statusCode: 200,
                message: 'success',
                data: result,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async create(createCodeOfEthicsDto) {
        return await this.codeOfEthicsService.create(createCodeOfEthicsDto);
    }
};
exports.CodeOfEthicsController = CodeOfEthicsController;
__decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Get)('all'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all codes of ethics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Return all codes of ethics' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CodeOfEthicsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)('create'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [codeOfEthics_dto_1.CreateCodeOfEthicsDto]),
    __metadata("design:returntype", Promise)
], CodeOfEthicsController.prototype, "create", null);
exports.CodeOfEthicsController = CodeOfEthicsController = __decorate([
    (0, swagger_1.ApiTags)('Code of Ethics'),
    (0, common_1.Controller)('code-of-ethics'),
    __metadata("design:paramtypes", [codeOfEthics_services_1.CodeOfEthicsService])
], CodeOfEthicsController);
//# sourceMappingURL=codeOfEthics.controller.js.map