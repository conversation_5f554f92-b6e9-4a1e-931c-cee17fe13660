import { <PERSON><PERSON>ty, <PERSON>umn, ManyTo<PERSON>ne, CreateDateColumn } from 'typeorm';
import { User } from 'src/modules/auth/entities/user.entity';
import { Organization } from './organiser.entities';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
@Entity()
export class OrganizationFollow extends BaseEntityClass {
  @ManyToOne(() => User, { eager: true })
  follower: User;
  @ManyToOne(() => Organization, (org) => org.followers, { eager: true })
  organization: Organization;
  @CreateDateColumn()
  followedAt: Date;

  @Column({ default: false })
  status: boolean

  
}
