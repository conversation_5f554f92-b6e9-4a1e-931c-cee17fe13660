import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { UploaddService } from './uploadd.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiConsumes, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
import { Public } from 'src/shared/decorators/publicRoute.decorator';
@Public()
@Controller('uploadd')
@UseGuards(JwtAuthGuard)
export class UploaddController {
  constructor(private readonly uploadService: UploaddService) {}
  @Post('photo')
  @UseInterceptors(
    FileInterceptor('photo', {
      limits: { fileSize: 500 * 1024 * 1024 },
      fileFilter: (req, file, callback) => {
        const allowedMimeTypes = [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/webp',
          'application/pdf',
          'video/mp4',
          'video/webm',
          'video/ogg',
          'video/x-matroska',
          'video/quicktime',
          'video/3gpp',
        ];
        if (allowedMimeTypes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(
            new BadRequestException(
              'Only image and video single files are allowed',
            ),
            false,
          );
        }
      },
    }),
  )
  @ApiBearerAuth()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        photo: { type: 'string', format: 'binary' },
      },
    },
  })
  async uploadPhoto(@UploadedFile() file: Express.Multer.File) {
    if (!file) throw new BadRequestException('No file uploaded');
    const fileUrl = await this.uploadService.uploadFile(file, 'photos');
    return {
      message: 'Photo uploaded successfully',
      url: fileUrl,
      fileName: file.originalname,
      size: file.size,
    };
  }
  @Post('photos')
  @UseInterceptors(
    FilesInterceptor('photos', 10, {
      limits: { fileSize: 500 * 1024 * 1024 },
      fileFilter: (req, file, callback) => {
        const allowedMimeTypes = [
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/webp',
          'application/pdf',
          'video/mp4',
          'video/webm',
          'video/ogg',
          'video/x-matroska',
          'video/quicktime',
          'video/3gpp',
        ];
        if (allowedMimeTypes.includes(file.mimetype)) {
          callback(null, true);
        } else {
          callback(
            new BadRequestException('Only image and videos  files are allowed'),
            false,
          );
        }
      },
    }),
  )
  @ApiBearerAuth()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        photos: {
          type: 'array',
          items: { type: 'string', format: 'binary' },
        },
      },
    },
  })
  async uploadMultiplePhotos(@UploadedFiles() files: Express.Multer.File[]) {
    if (!files || files.length === 0)
      throw new BadRequestException('No files uploaded');
    // upload all files concurrently
    const uploadResults = await Promise.all(
      files.map((file) => this.uploadService.uploadFile(file, 'photos')),
    );
    return {
      message: 'Photos uploaded successfully',
      files: files.map((file, index) => ({
        url: uploadResults[index],
        fileName: file.originalname,
        size: file.size,
      })),
    };
  }
}
