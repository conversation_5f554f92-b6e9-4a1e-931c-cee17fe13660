"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PostService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const post_entities_1 = require("./entities/post.entities");
const user_entity_1 = require("../auth/entities/user.entity");
const comment_entities_1 = require("./entities/comment.entities");
const profile_entity_1 = require("../profile/entities/profile.entity");
let PostService = class PostService {
    postRepo;
    userRepo;
    commentRepo;
    profileRepo;
    constructor(postRepo, userRepo, commentRepo, profileRepo) {
        this.postRepo = postRepo;
        this.userRepo = userRepo;
        this.commentRepo = commentRepo;
        this.profileRepo = profileRepo;
    }
    async createPost(createPostDto, userId) {
        try {
            const user = await this.userRepo.findOne({ where: { id: userId } });
            if (!user) {
                throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
            }
            const newPost = this.postRepo.create({
                content: createPostDto.content,
                imageUrl: createPostDto.imageUrl || [],
                videoUrl: createPostDto.videoUrl || [],
                likes: createPostDto.likes ?? 0,
                shares: createPostDto.shares ?? 0,
                user,
            });
            const savedPost = await this.postRepo.save(newPost);
            return {
                message: 'Post created successfully',
                post: {
                    ...savedPost,
                    user: {
                        id: user.id,
                        fullName: user.FullName,
                        designation: user.designation,
                    },
                },
            };
        }
        catch (e) {
            console.error(e);
            throw new common_1.HttpException(e.message || 'Internal server error', e.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async editPost(postId, updateDto, userId) {
        const post = await this.postRepo.findOne({
            where: { id: postId },
            relations: ['user'],
        });
        if (!post) {
            throw new common_1.HttpException('Post not found', common_1.HttpStatus.NOT_FOUND);
        }
        if (post.user.id !== userId) {
            throw new common_1.HttpException('Unauthorized', common_1.HttpStatus.UNAUTHORIZED);
        }
        post.content = updateDto.content || post.content;
        post.imageUrl = updateDto.imageUrl || post.imageUrl;
        post.videoUrl = updateDto.videoUrl || post.videoUrl;
        post.likes = updateDto.likes ?? post.likes;
        post.shares = updateDto.shares ?? post.shares;
        post.PostImage = updateDto.PostImage || post.PostImage;
        const updated = await this.postRepo.save(post);
        console.log('Updated post:', updated);
        return {
            message: 'Post updated successfully',
            post: {
                ...updated,
                user: {
                    id: updated.user.id,
                    fullName: updated.user.FullName,
                    designation: updated.user.designation,
                },
            },
        };
    }
    async deletePost(postId, userId) {
        const post = await this.postRepo.findOne({
            where: { id: postId },
            relations: ['user'],
        });
        if (!post) {
            throw new common_1.HttpException('Post not found', common_1.HttpStatus.NOT_FOUND);
        }
        if (post.user.id !== userId) {
            throw new common_1.HttpException('Unauthorized', common_1.HttpStatus.UNAUTHORIZED);
        }
        const deletedPost = { ...post };
        await this.postRepo.remove(post);
        return {
            message: 'Post deleted successfully',
            deletedPost: {
                id: deletedPost.id,
                content: deletedPost.content,
                imageUrl: deletedPost.imageUrl,
                videoUrl: deletedPost.videoUrl,
                likes: deletedPost.likes,
                shares: deletedPost.shares,
                visibility: deletedPost.visibility,
                user: {
                    id: deletedPost.user.id,
                    fullName: deletedPost.user.FullName,
                    designation: deletedPost.user.designation,
                },
            },
        };
    }
    async like(params, userId) {
        try {
            const { post_id } = params;
            const post = await this.postRepo.findOne({
                where: { id: post_id },
                relations: ['likedUsers', 'user'],
            });
            if (!post) {
                throw new common_1.HttpException('Post not found', common_1.HttpStatus.NOT_FOUND);
            }
            const user = await this.userRepo.findOne({ where: { id: userId } });
            if (!user) {
                throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
            }
            const hasLiked = post.likedUsers.some((u) => u.id === user.id);
            if (hasLiked) {
                post.likedUsers = post.likedUsers.filter((u) => u.id !== user.id);
            }
            else {
                post.likedUsers.push(user);
            }
            post.likes = post.likedUsers.length;
            post.dislikes = 0;
            await this.postRepo.save(post);
            return {
                message: hasLiked ? 'Post unliked successfully' : 'Post liked successfully',
                post: {
                    id: post.id,
                    likes: post.likes,
                    dislikes: post.dislikes,
                    likedUserIds: post.likedUsers.map((u) => u.id),
                },
            };
        }
        catch (e) {
            console.error(e);
            throw new common_1.HttpException(e.message, e.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async likess(params, userId) {
        try {
            const { post_id } = params;
            const post = await this.postRepo.findOne({
                where: { id: post_id },
                relations: ['likedUsers', 'user'],
            });
            if (!post) {
                throw new common_1.HttpException('Post not found', common_1.HttpStatus.NOT_FOUND);
            }
            const user = await this.userRepo.findOne({ where: { id: userId } });
            if (!user) {
                throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
            }
            const hasLiked = post.likedUsers.some((u) => u.id === user.id);
            if (hasLiked) {
                post.likedUsers = post.likedUsers.filter((u) => u.id !== user.id);
                post.likes = 0;
                post.dislikes = 1;
            }
            else {
                post.likedUsers.push(user);
                post.likes = 1;
                post.dislikes = 0;
            }
            await this.postRepo.save(post);
            return {
                message: hasLiked ? 'Post disliked successfully' : 'Post liked successfully',
                post: {
                    id: post.id,
                    likes: post.likes,
                    dislikes: post.dislikes,
                    likedUserIds: post.likedUsers.map((u) => u.id),
                },
            };
        }
        catch (e) {
            console.error(e);
            throw new common_1.HttpException(e.message, e.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async share(params, userId) {
        try {
            const { post_id } = params;
            const post = await this.postRepo.findOne({
                where: { id: post_id },
                relations: ['sharedUsers', 'user'],
            });
            if (!post) {
                throw new common_1.HttpException('Post not found', common_1.HttpStatus.NOT_FOUND);
            }
            const user = await this.userRepo.findOne({ where: { id: userId } });
            if (!user) {
                throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
            }
            post.shares += 1;
            const hasShared = post.sharedUsers.some((u) => u.id === user.id);
            if (!hasShared) {
                post.sharedUsers.push(user);
            }
            await this.postRepo.save(post);
            return {
                message: 'Post shared successfully',
                post,
                sharedUsers: post.sharedUsers,
            };
        }
        catch (e) {
            console.log(e);
            throw new common_1.HttpException(e.message, e.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async createComment(createCommentDto) {
        const { content, post_id, user_id } = createCommentDto;
        const post = await this.postRepo.findOne({
            where: { id: post_id },
            relations: ['comments', 'user'],
        });
        if (!post)
            throw new common_1.HttpException('Post not found', common_1.HttpStatus.NOT_FOUND);
        const user = await this.userRepo.findOne({ where: { id: user_id } });
        if (!user)
            throw new common_1.HttpException('User not found', common_1.HttpStatus.NOT_FOUND);
        const comment = this.commentRepo.create({ content, user, post });
        const savedComment = await this.commentRepo.save(comment);
        const commentCount = (post.comments?.length || 0) + 1;
        return {
            message: 'Comment created successfully',
            comment: {
                id: savedComment.id,
                content: savedComment.content,
                createdAt: savedComment.created_at,
                createdBy: {
                    id: user.id,
                    fullName: user.FullName,
                    designation: user.designation,
                },
                post: {
                    id: post.id,
                    content: post.content,
                    commentCount,
                    createdBy: {
                        id: post.user.id,
                        fullName: post.user.FullName,
                        designation: post.user.designation,
                    },
                },
            },
        };
    }
    async getAllPosts() {
        try {
            const posts = await this.postRepo.find({
                relations: [
                    'user',
                    'user.profile',
                    'comments',
                    'comments.user',
                    'comments.user.profile',
                    'likedUsers',
                ],
                order: { created_at: 'DESC' },
            });
            const formattedPosts = posts.map(({ profile, likedUsers, ...post }) => ({
                ...post,
                likedUserIds: likedUsers?.map(user => user.id) || [],
                user: post.user && post.user.profile
                    ? {
                        id: post.user.id,
                        profile: {
                            fullName: post.user.profile.fullName,
                            designation: post.user.designation,
                            location: post.user.profile.location,
                            bio: post.user.profile.bio,
                            profilePicture: post.user.profile.profilePicture,
                        },
                    }
                    : null,
                comments: post.comments?.map((comment) => ({
                    id: comment.id,
                    content: comment.content,
                    createdAt: comment.created_at,
                    createdBy: comment.user && comment.user.profile
                        ? {
                            id: comment.user.id,
                            profile: {
                                fullName: comment.user.profile.fullName,
                                profilePicture: comment.user.profile.profilePicture,
                            },
                        }
                        : null,
                })) || [],
            }));
            return {
                message: 'All posts fetched successfully',
                posts: formattedPosts,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getAllPostss() {
        try {
            const posts = await this.postRepo.find({
                relations: [
                    'user',
                    'user.profile',
                    'comments',
                    'comments.user',
                    'comments.user.profile',
                ],
                order: { created_at: 'DESC' },
            });
            const formattedPosts = posts.map(({ profile, ...post }) => ({
                ...post,
                user: post.user && post.user.profile
                    ? {
                        id: post.user.id,
                        profile: {
                            fullName: post.user.profile.fullName,
                            designation: post.user.designation,
                            location: post.user.profile.location,
                            bio: post.user.profile.bio,
                            profilePicture: post.user.profile.profilePicture,
                        },
                    }
                    : null,
                comments: post.comments?.map((comment) => ({
                    id: comment.id,
                    content: comment.content,
                    createdAt: comment.created_at,
                    createdBy: comment.user && comment.user.profile
                        ? {
                            id: comment.user.id,
                            profile: {
                                fullName: comment.user.profile.fullName,
                                profilePicture: comment.user.profile.profilePicture,
                            },
                        }
                        : null,
                })) || [],
            }));
            console.log('Fetched posts:', formattedPosts);
            return {
                message: 'All posts fetched successfully',
                posts: formattedPosts,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPostsByUssserId(userId) {
        try {
            const posts = await this.postRepo.find({
                where: { user: { id: userId } },
                relations: ['user'],
                order: { created_at: 'DESC' },
            });
            console.log('Fetched posts:', posts);
            const formattedPosts = posts.map((post) => ({
                ...post,
                user: post.user
                    ? {
                        id: post.user.id,
                        fullName: post.user.FullName,
                        designation: post.user.designation,
                    }
                    : null,
            }));
            return {
                message: `Posts fetched successfully for user ${userId}`,
                posts: formattedPosts,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getPostsByUserId(userId) {
        try {
            const posts = await this.postRepo.find({
                where: { user: { id: userId } },
                relations: [
                    'user',
                    'user.profile',
                    'comments',
                    'comments.user',
                    'comments.user.profile',
                    'likedUsers',
                ],
                order: { created_at: 'DESC' },
            });
            const formattedPosts = posts.map((post) => ({
                ...post,
                user: post.user
                    ? {
                        id: post.user.id,
                        fullName: post.user.profile?.fullName || null,
                        designation: post.user.designation,
                    }
                    : null,
                comments: post.comments?.map((comment) => ({
                    id: comment.id,
                    content: comment.content,
                    createdAt: comment.created_at,
                    createdBy: comment.user
                        ? {
                            id: comment.user.id,
                            profile: {
                                fullName: comment.user.profile?.fullName || null,
                                profilePicture: comment.user.profile?.profilePicture || '',
                            },
                        }
                        : null,
                })) || [],
                likes: post.likedUsers?.length || 0,
                likedUserIds: post.likedUsers?.map((user) => user.id) || [],
            }));
            return {
                message: `Posts fetched successfully for user ${userId}`,
                posts: formattedPosts,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message, error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
};
exports.PostService = PostService;
exports.PostService = PostService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(post_entities_1.Post)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(comment_entities_1.Comment)),
    __param(3, (0, typeorm_1.InjectRepository)(profile_entity_1.Profile)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], PostService);
//# sourceMappingURL=post.service.js.map