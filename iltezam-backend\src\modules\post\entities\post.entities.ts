import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
  OneToOne,

} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { BaseEntityClass } from '../../auth/entities/base.entity';
import { Comment } from './comment.entities';
import { Profile } from 'src/modules/profile/entities/profile.entity';


@Entity({ name: 'posts' })
export class Post extends BaseEntityClass {


  @Column({ type: 'text' })
  content: string;

 @Column('text', { array: true, nullable: true })
imageUrl?: string[];

@Column('text', { array: true, nullable: true})
videoUrl?: string[];


  @Column({ type: 'enum', enum: ['public', 'private'], default: 'public' })
  visibility: 'public' | 'private';


  @Column({ type: 'int', default: 0 })
  likes: number;

  @Column({ type: 'int', default: 0 })
  dislikes: number;

  @Column({ type: 'int', default: 0 })
  shares: number;
 
  @Column({ nullable: true })
  PostImage: string;
 
 @ManyToOne(() => User, (user) => user.posts, { eager: true }) // <-- Add `eager: true`
@JoinColumn({ name: 'user_id' })
user: User;

  
@OneToMany(() => Comment, (comment) => comment.post, { cascade: true })
comments: Comment[];

@ManyToMany(() => User, (user) => user.likedPosts)
@JoinTable({
  name: 'post_likes', // Join table name
  joinColumn: { name: 'post_id', referencedColumnName: 'id' },
  inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
})
likedUsers: User[];


@ManyToMany(() => User, (user) => user.sharedPosts, { cascade: true })
@JoinTable({
  name: 'post_shares',
  joinColumn: { name: 'post_id', referencedColumnName: 'id' },
  inverseJoinColumn: { name: 'user_id', referencedColumnName: 'id' },
})
sharedUsers: User[];

@OneToOne(() => Profile, profile => profile.user, { eager: true }) // Optional: eager
profile: Profile;


}

  
 

