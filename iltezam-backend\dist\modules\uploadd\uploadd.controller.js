"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploaddController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const uploadd_service_1 = require("./uploadd.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const swagger_1 = require("@nestjs/swagger");
const publicRoute_decorator_1 = require("../../shared/decorators/publicRoute.decorator");
let UploaddController = class UploaddController {
    uploadService;
    constructor(uploadService) {
        this.uploadService = uploadService;
    }
    async uploadPhoto(file) {
        if (!file)
            throw new common_1.BadRequestException('No file uploaded');
        const fileUrl = await this.uploadService.uploadFile(file, 'photos');
        return {
            message: 'Photo uploaded successfully',
            url: fileUrl,
            fileName: file.originalname,
            size: file.size,
        };
    }
    async uploadMultiplePhotos(files) {
        if (!files || files.length === 0)
            throw new common_1.BadRequestException('No files uploaded');
        const uploadResults = await Promise.all(files.map((file) => this.uploadService.uploadFile(file, 'photos')));
        return {
            message: 'Photos uploaded successfully',
            files: files.map((file, index) => ({
                url: uploadResults[index],
                fileName: file.originalname,
                size: file.size,
            })),
        };
    }
};
exports.UploaddController = UploaddController;
__decorate([
    (0, common_1.Post)('photo'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('photo', {
        limits: { fileSize: 500 * 1024 * 1024 },
        fileFilter: (req, file, callback) => {
            const allowedMimeTypes = [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'application/pdf',
                'video/mp4',
                'video/webm',
                'video/ogg',
                'video/x-matroska',
                'video/quicktime',
                'video/3gpp',
            ];
            if (allowedMimeTypes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new common_1.BadRequestException('Only image and video single files are allowed'), false);
            }
        },
    })),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                photo: { type: 'string', format: 'binary' },
            },
        },
    }),
    __param(0, (0, common_1.UploadedFile)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UploaddController.prototype, "uploadPhoto", null);
__decorate([
    (0, common_1.Post)('photos'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('photos', 10, {
        limits: { fileSize: 500 * 1024 * 1024 },
        fileFilter: (req, file, callback) => {
            const allowedMimeTypes = [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'application/pdf',
                'video/mp4',
                'video/webm',
                'video/ogg',
                'video/x-matroska',
                'video/quicktime',
                'video/3gpp',
            ];
            if (allowedMimeTypes.includes(file.mimetype)) {
                callback(null, true);
            }
            else {
                callback(new common_1.BadRequestException('Only image and videos  files are allowed'), false);
            }
        },
    })),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                photos: {
                    type: 'array',
                    items: { type: 'string', format: 'binary' },
                },
            },
        },
    }),
    __param(0, (0, common_1.UploadedFiles)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], UploaddController.prototype, "uploadMultiplePhotos", null);
exports.UploaddController = UploaddController = __decorate([
    (0, publicRoute_decorator_1.Public)(),
    (0, common_1.Controller)('uploadd'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [uploadd_service_1.UploaddService])
], UploaddController);
//# sourceMappingURL=uploadd.controller.js.map