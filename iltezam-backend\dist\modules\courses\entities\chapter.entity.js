"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Chapter = void 0;
const typeorm_1 = require("typeorm");
const class_validator_1 = require("class-validator");
const topic_entity_1 = require("./topic.entity");
const base_entity_1 = require("../../auth/entities/base.entity");
const quiz_entity_1 = require("./quiz.entity");
let Chapter = class Chapter extends base_entity_1.BaseEntityClass {
    chapterTitle;
    description;
    mediaUrl;
    duration;
    prerequisiteChapterIds;
    quizId;
    topics;
    quizzes;
};
exports.Chapter = Chapter;
__decorate([
    (0, typeorm_1.Column)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], Chapter.prototype, "chapterTitle", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], Chapter.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], Chapter.prototype, "mediaUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], Chapter.prototype, "duration", void 0);
__decorate([
    (0, typeorm_1.Column)('uuid', { array: true, nullable: true }),
    __metadata("design:type", Array)
], Chapter.prototype, "prerequisiteChapterIds", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], Chapter.prototype, "quizId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => topic_entity_1.Topic, (topic) => topic.chapters, { onDelete: 'CASCADE' }),
    __metadata("design:type", topic_entity_1.Topic)
], Chapter.prototype, "topics", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => quiz_entity_1.Quiz, quiz => quiz.chapter, { cascade: true, eager: true }),
    __metadata("design:type", Array)
], Chapter.prototype, "quizzes", void 0);
exports.Chapter = Chapter = __decorate([
    (0, typeorm_1.Entity)()
], Chapter);
//# sourceMappingURL=chapter.entity.js.map