"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
const socket_io_1 = require("socket.io");
const live_chat_service_1 = require("./modules/live-chat/live-chat.service");
const express_1 = require("express");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.use((0, express_1.json)({
        verify: (req, res, buf) => {
            if (req.headers['stripe-signature']) {
                req.rawBody = buf.toString();
            }
        }
    }));
    app.setGlobalPrefix(`/api`);
    app.enableCors();
    const config = new swagger_1.DocumentBuilder()
        .setTitle('ILTEZAM')
        .setDescription('Fexrip API description')
        .setVersion('1.0')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
    }, 'access-token')
        .addTag('ILTEZAM')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/api-docs', app, document);
    await app.listen(process.env.PORT ?? 3000);
    const server = app.getHttpServer();
    const io = new socket_io_1.Server(server, { cors: { origin: '*' } });
    console.log('Socket.IO server is running');
    const liveChatService = app.get(live_chat_service_1.LiveChatService);
    liveChatService.setIo(io);
    global.onlineUsers = new Map();
    io.on("connection", (socket) => {
        console.log("New client connected: " + socket.id);
        socket.on("register", (userID) => {
            global.onlineUsers.set(userID, socket.id);
            console.log("registered user: " + userID + " with socket ID: " + socket.id);
        });
        socket.on("disconnect", () => {
            for (const [userID, socketID] of global.onlineUsers.entries()) {
                if (socketID === socket.id) {
                    global.onlineUsers.delete(userID);
                    break;
                }
            }
            console.log("Client disconnected: " + socket.id);
        });
    });
    global.io = io;
    console.log(`Application is running on: ${await app.getUrl()} `);
}
bootstrap();
//# sourceMappingURL=main.js.map