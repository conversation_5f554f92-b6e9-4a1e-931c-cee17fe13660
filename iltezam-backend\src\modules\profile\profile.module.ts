import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Profile } from './entities/profile.entity';
import { ProfileService } from './profile.service';
import { ProfileController } from './profile.controller';
import { ProfileConnection } from './entities/profile.connection.entity';
import { User } from '../auth/entities/user.entity';
import { ChatRoom } from '../live-chat/entities/chat-room.entity';
import { OrganizationFollow } from 'src/organiser/entities/organization.follow.entities';

@Module({
  imports: [TypeOrmModule.forFeature([
    Profile,
    ProfileConnection,
    User, 
    ChatRoom,
   OrganizationFollow

  ])],

  controllers: [ProfileController],
  providers: [ProfileService],
  exports: [ProfileService],
})
export class ProfileModule {}
