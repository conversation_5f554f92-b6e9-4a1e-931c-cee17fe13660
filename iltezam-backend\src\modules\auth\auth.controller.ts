import {
  Body,
  Controller,
  Get,
  HttpException,
  Post,
  Put,
  Request,
  Query,
  UnauthorizedException,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { Public } from 'src/shared/decorators/publicRoute.decorator';
import { AuthLoginDto, verifyEmaildDto } from './dtos/auth.dto';
import { RegisterDto } from './dtos/auth.dto';
import {
  ApiBearerAuth,
  ApiHeader,
  ApiOperation,
  ApiResponse,
} from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { ResetNewPasswordDto } from './dtos/auth.dto';
import { VerifyEmailDto, ResendVerificationDto } from './dtos/auth.dto';
import { ForgotPasswordDto } from './dtos/auth.dto';
import { CompanyDto } from './dtos/auth.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('login')
  @ApiOperation({ summary: 'Sign in to account' })
  async login(@Body() authenticateRequest: AuthLoginDto) {
    try {
      return await this.authService.loginUser(authenticateRequest);
    } catch (e) {
      throw new HttpException(e.message, 401);
    }
  }

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'Register User' })
  async register(@Body() signUpUserDto: RegisterDto) {
    try {
      let result = await this.authService.registerUser(signUpUserDto);
      return {
        message: result.message,
        data: result.data,
      };
    } catch (e) {
      if (e instanceof HttpException) {
        throw e;
      }
      throw new HttpException(e.message, HttpStatus.BAD_REQUEST);
    }
  }

  @Public()
  @Post('verify-email')
  @ApiOperation({ summary: 'Verify Email' })
  async verifyEmail(@Body() params: verifyEmaildDto) {
    try {
      const result = await this.authService.verifyEmail(params);
      return {
        message: result.message,
      };
    } catch (e) {
      throw new UnauthorizedException(e.message);
    }
  }

  @Public()
  @Post('forgot-password')
  @ApiOperation({ summary: 'Forgot Password' })
  async forgotPassword(@Body() dto: ForgotPasswordDto) {
    try {
      const result = await this.authService.forgotPassword(dto.email);
      return {
        message: result.message,
      };
    } catch (e) {
      throw new UnauthorizedException(e.message);
    }
  }

  @Public()
  @Post('resend-verification')
  @ApiOperation({ summary: 'Resend Email Verification' })
  async resendVerification(@Body() dto: ResendVerificationDto) {
    try {
      const result = await this.authService.resendVerificationEmail(dto.email);
      return {
        message: result.message,
      };
    } catch (e) {
      throw new UnauthorizedException(e.message);
    }
  }

  // In your AuthController or UserController
@Public()
@Get('users')
async getUsers(@Request() req) {
  const userId: number | null = req.user?.id ? Number(req.user.id) : null;
  return await this.authService.getAllUsers(userId);
}

  @Post('create')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new company' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Company created successfully',
  })
  async createCompany(@Body() companyDto: CompanyDto, @Request() req) {
    return this.authService.createCompany(companyDto);
  }

 
  @Post('subscribe-email')
  @Public()
  async subscribeEmail(@Body() body: { email: string }) {
    const { email } = body;
    try {
      const result = await this.authService.emailSubscribe(email);
      return {
        message: result.message,
      };
    } catch (e) {
      throw new UnauthorizedException(e.message);
    }
  }
}
