import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Post } from './entities/post.entities';
import { CreateCommentDto, CreatePostDto } from './dto/post.dto';
import { User } from '../auth/entities/user.entity';
import { Comment } from './entities/comment.entities';
import { Profile } from '../profile/entities/profile.entity';

@Injectable()
export class PostService {

  constructor(
    @InjectRepository(Post)
    private readonly postRepo: Repository<Post>,

    @InjectRepository(User)
    private readonly userRepo: Repository<User>,

    @InjectRepository(Comment)
    private readonly commentRepo: Repository<Comment>,

    @InjectRepository(Profile)
    private readonly profileRepo: Repository<Profile>,
  ) {}

  async createPost(createPostDto: CreatePostDto, userId: number): Promise<any> {
    try {
      const user = await this.userRepo.findOne({ where: { id: userId } });

      if (!user) {
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      }

      const newPost = this.postRepo.create({
        content: createPostDto.content,
        imageUrl: createPostDto.imageUrl || [],
        videoUrl: createPostDto.videoUrl || [],
        likes: createPostDto.likes ?? 0,
        shares: createPostDto.shares ?? 0,
        user,
      });

      const savedPost = await this.postRepo.save(newPost);

      return {
        message: 'Post created successfully',
        post: {
          ...savedPost,
          user: {
            id: user.id,
            fullName: user.FullName,
            designation: user.designation,
          },
        },
      };
    } catch (e) {
      console.error(e);
      throw new HttpException(
        e.message || 'Internal server error',
        e.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }



  // Edit Post
async editPost(postId: number, updateDto: CreatePostDto, userId: number): Promise<any> {
  const post = await this.postRepo.findOne({
    where: { id: postId },
    relations: ['user'],
  });

  if (!post) {
    throw new HttpException('Post not found', HttpStatus.NOT_FOUND);
  }

  if (post.user.id !== userId) {
    throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
  }

  post.content = updateDto.content || post.content;
  post.imageUrl = updateDto.imageUrl || post.imageUrl;
  post.videoUrl = updateDto.videoUrl || post.videoUrl;
  post.likes = updateDto.likes ?? post.likes;
  post.shares = updateDto.shares ?? post.shares;
  post.PostImage=updateDto.PostImage || post.PostImage;

  const updated = await this.postRepo.save(post);
  console.log('Updated post:', updated);

  return {
    message: 'Post updated successfully',
    post: {
      ...updated,
      user: {
        id: updated.user.id,
        fullName: updated.user.FullName,
        designation: updated.user.designation,
      },
    },
  };
}

// Delete Post
async deletePost(postId: number, userId: number): Promise<any> {
  const post = await this.postRepo.findOne({
    where: { id: postId },
    relations: ['user'],
  });

  if (!post) {
    throw new HttpException('Post not found', HttpStatus.NOT_FOUND);
  }

  if (post.user.id !== userId) {
    throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
  }

  const deletedPost = { ...post }; 

  await this.postRepo.remove(post);

  return {
    message: 'Post deleted successfully',
    deletedPost: {
      id: deletedPost.id,
      content: deletedPost.content,
      imageUrl: deletedPost.imageUrl,
      videoUrl: deletedPost.videoUrl,
      likes: deletedPost.likes,
      shares: deletedPost.shares,
      visibility: deletedPost.visibility,
      user: {
        id: deletedPost.user.id,
        fullName: deletedPost.user.FullName,
        designation: deletedPost.user.designation,
      },
    },
  };
}


  async like(params: { post_id: number }, userId: number): Promise<any> {
  try {
    const { post_id } = params;

    const post = await this.postRepo.findOne({
      where: { id: post_id },
      relations: ['likedUsers', 'user'],
    });

    if (!post) {
      throw new HttpException('Post not found', HttpStatus.NOT_FOUND);
    }

    const user = await this.userRepo.findOne({ where: { id: userId } });

    if (!user) {
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }

    const hasLiked = post.likedUsers.some((u) => u.id === user.id);

    if (hasLiked) {
      // Remove like
      post.likedUsers = post.likedUsers.filter((u) => u.id !== user.id);
    } else {
      // Add like
      post.likedUsers.push(user);
    }

    // Update like/dislike count based on likedUsers list
    post.likes = post.likedUsers.length;
    post.dislikes = 0; // Assuming dislike is only a toggle in your logic

    await this.postRepo.save(post);

    return {
      message: hasLiked ? 'Post unliked successfully' : 'Post liked successfully',
      post: {
        id: post.id,
        likes: post.likes,
        dislikes: post.dislikes,
        likedUserIds: post.likedUsers.map((u) => u.id),
      },
    };
  } catch (e) {
    console.error(e);
    throw new HttpException(
      e.message,
      e.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}


async likess(params: { post_id: number }, userId: number): Promise<any> {
  try {
    const { post_id } = params;

    const post = await this.postRepo.findOne({
      where: { id: post_id },
      relations: ['likedUsers', 'user'],
    });

    if (!post) {
      throw new HttpException('Post not found', HttpStatus.NOT_FOUND);
    }

    const user = await this.userRepo.findOne({ where: { id: userId } });

    if (!user) {
      throw new HttpException('User not found', HttpStatus.NOT_FOUND);
    }

    const hasLiked = post.likedUsers.some((u) => u.id === user.id);

    if (hasLiked) {
      // User already liked it, now they are "disliking"
      post.likedUsers = post.likedUsers.filter((u) => u.id !== user.id);
      post.likes = 0;
      post.dislikes = 1;
    } else {
      // User is liking it
      post.likedUsers.push(user);
      post.likes = 1;
      post.dislikes = 0;
    }

    await this.postRepo.save(post);

    return {
      message: hasLiked ? 'Post disliked successfully' : 'Post liked successfully',
      post: {
        id: post.id,
        likes: post.likes,
        dislikes: post.dislikes,
        likedUserIds: post.likedUsers.map((u) => u.id),
      },
    };
  } catch (e) {
    console.error(e);
    throw new HttpException(
      e.message,
      e.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}



  async share(params: { post_id: number }, userId: number): Promise<any> {
    try {
      const { post_id } = params;

      // Load the post with sharedUsers
      const post = await this.postRepo.findOne({
        where: { id: post_id },
        relations: ['sharedUsers', 'user'],
      });

      if (!post) {
        throw new HttpException('Post not found', HttpStatus.NOT_FOUND);
      }

      const user = await this.userRepo.findOne({ where: { id: userId } });
      if (!user) {
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      }

      // Always increment shares, even if user already shared
      post.shares += 1;

      // Add user to sharedUsers (avoid duplicates)
      const hasShared = post.sharedUsers.some((u) => u.id === user.id);
      if (!hasShared) {
        post.sharedUsers.push(user);
      }

      await this.postRepo.save(post);

      return {
        message: 'Post shared successfully',
        post,
        sharedUsers: post.sharedUsers,
      };
    } catch (e) {
      console.log(e);
      throw new HttpException(
        e.message,
        e.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createComment(createCommentDto: CreateCommentDto): Promise<any> {
    const { content, post_id, user_id } = createCommentDto;

    const post = await this.postRepo.findOne({
      where: { id: post_id },
      relations: ['comments', 'user'], // 'user' here is the creator of the post
    });

    if (!post) throw new HttpException('Post not found', HttpStatus.NOT_FOUND);

    const user = await this.userRepo.findOne({ where: { id: user_id } });
    if (!user) throw new HttpException('User not found', HttpStatus.NOT_FOUND);

    const comment = this.commentRepo.create({ content, user, post });
    const savedComment = await this.commentRepo.save(comment);

    const commentCount = (post.comments?.length || 0) + 1;

    return {
      message: 'Comment created successfully',
      comment: {
        id: savedComment.id,
        content: savedComment.content,
        createdAt: savedComment.created_at,
        createdBy: {
          id: user.id,
          fullName: user.FullName,
          designation: user.designation,
        },
        post: {
          id: post.id,
          content: post.content,
          commentCount,
          createdBy: {
            id: post.user.id,
            fullName: post.user.FullName,
            designation: post.user.designation,
          },
        },
      },
    };
  }

  async getAllPosts(): Promise<any> {
  try {
    const posts = await this.postRepo.find({
      relations: [
        'user',
        'user.profile',
        'comments',
        'comments.user',
        'comments.user.profile',
        'likedUsers', // 🟢 Include likedUsers relation
      ],
      order: { created_at: 'DESC' },
    });

    const formattedPosts = posts.map(({ profile, likedUsers, ...post }) => ({
      ...post,
      likedUserIds: likedUsers?.map(user => user.id) || [], // 🟢 Add liked user IDs
      user:
        post.user && post.user.profile
          ? {
              id: post.user.id,
              profile: {
                fullName: post.user.profile.fullName,
                designation: post.user.designation,
                location: post.user.profile.location,
                bio: post.user.profile.bio,
                profilePicture: post.user.profile.profilePicture,
              },
            }
          : null,
      comments:
        post.comments?.map((comment) => ({
          id: comment.id,
          content: comment.content,
          createdAt: comment.created_at,
          createdBy:
            comment.user && comment.user.profile
              ? {
                  id: comment.user.id,
                  profile: {
                    fullName: comment.user.profile.fullName,
                    profilePicture: comment.user.profile.profilePicture,
                  },
                }
              : null,
        })) || [],
    }));

    return {
      message: 'All posts fetched successfully',
      posts: formattedPosts,
    };
  } catch (error) {
    throw new HttpException(
      error.message,
      error.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}




  async getAllPostss(): Promise<any> {
    try {
      const posts = await this.postRepo.find({
        relations: [
          'user',
          'user.profile',
          'comments',
          'comments.user',
          'comments.user.profile',
        ],
        order: { created_at: 'DESC' },
      });
      const formattedPosts = posts.map(({ profile, ...post }) => ({
        ...post,
        user:
          post.user && post.user.profile
            ? {
                id: post.user.id,
                profile: {
                  fullName: post.user.profile.fullName,
                  designation: post.user.designation,
                  location: post.user.profile.location,
                  bio: post.user.profile.bio,
                  profilePicture: post.user.profile.profilePicture,
                },
              }
            : null,
        comments:
          post.comments?.map((comment) => ({
            id: comment.id,
            content: comment.content,
            createdAt: comment.created_at,
            createdBy:
              comment.user && comment.user.profile
                ? {
                    id: comment.user.id,
                    profile: {
                      fullName: comment.user.profile.fullName,
                      // designation: comment.user.profile.designation,
                      profilePicture: comment.user.profile.profilePicture,
                    },
                  }
                : null,
          })) || [],
      }));
      console.log('Fetched posts:', formattedPosts);
      return {
        message: 'All posts fetched successfully',
        posts: formattedPosts,
      };
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }





  async getPostsByUssserId(userId: number): Promise<any> {
    try {
      const posts = await this.postRepo.find({
        where: { user: { id: userId } },
        relations: ['user'],
        order: { created_at: 'DESC' },
      });

      console.log('Fetched posts:', posts); // Debug output

      const formattedPosts = posts.map((post) => ({
        ...post,
        user: post.user
          ? {
              id: post.user.id,
              fullName: post.user.FullName,
              designation: post.user.designation,
            }
          : null,
      }));

      return {
        message: `Posts fetched successfully for user ${userId}`,
        posts: formattedPosts,
      };
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


  async getPostsByUserId(userId: number): Promise<any> {
  try {
    const posts = await this.postRepo.find({
      where: { user: { id: userId } },
      relations: [
        'user',
        'user.profile',
        'comments',
        'comments.user',
        'comments.user.profile',
        'likedUsers',
      ],
      order: { created_at: 'DESC' },
    });

    const formattedPosts = posts.map((post) => ({
      ...post,
      user: post.user
        ? {
            id: post.user.id,
            fullName: post.user.profile?.fullName || null,
            designation: post.user.designation,
          }
        : null,
      comments: post.comments?.map((comment) => ({
        id: comment.id,
        content: comment.content,
        createdAt: comment.created_at,
        createdBy: comment.user
          ? {
              id: comment.user.id,
              profile: {
                fullName: comment.user.profile?.fullName || null,
                profilePicture: comment.user.profile?.profilePicture || '',
              },
            }
          : null,
      })) || [],
      likes: post.likedUsers?.length || 0,
      likedUserIds: post.likedUsers?.map((user) => user.id) || [],
    }));

    return {
      message: `Posts fetched successfully for user ${userId}`,
      posts: formattedPosts,
    };
  } catch (error) {
    throw new HttpException(
      error.message,
      error.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

}
