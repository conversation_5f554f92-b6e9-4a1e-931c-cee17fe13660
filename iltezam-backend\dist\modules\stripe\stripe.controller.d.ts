import { StripeService } from './stripe.service';
import { CreatePaymentIntentDto } from './dtos/stripe-payment.dto';
import { SaveCardDetailsDto } from './dtos/save-card.dto';
export declare class StripeController {
    private readonly stripeService;
    constructor(stripeService: StripeService);
    createPaymentIntent(createPaymentIntentDto: CreatePaymentIntentDto): Promise<{
        clientSecret: string | null;
    }>;
    saveCardDetails(saveCardDetailsDto: SaveCardDetailsDto, req: any): Promise<{
        success: boolean;
        message: string;
        cardDetails: {
            id: number;
            cardNumber: string;
            expiryMonth: string;
            expiryYear: string;
            cardHolderName: string;
            cvv: string;
        };
    }>;
    getSavedCards(req: any): Promise<{
        success: boolean;
        cards: import("./entities/card-details.entity").CardDetails[];
    }>;
    handleWebhook(req: any, res: any, signature: string): Promise<any>;
}
