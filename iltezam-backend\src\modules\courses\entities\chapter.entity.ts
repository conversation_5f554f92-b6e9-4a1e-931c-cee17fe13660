import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON>ne, OneToMany } from 'typeorm';
import { IsNotEmpty } from 'class-validator';
import { Topic } from './topic.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { Quiz } from './quiz.entity';

@Entity()
export class Chapter extends BaseEntityClass {
  @Column()
  @IsNotEmpty()
  chapterTitle: string;

  @Column('text')
  @IsNotEmpty()
  description: string;

  @Column({ nullable: true })
  @IsNotEmpty()
  mediaUrl: string;

  @Column({ nullable: true })
  duration: number;

  @Column('uuid', { array: true, nullable: true })
  prerequisiteChapterIds: string[];

  @Column({ nullable: true })
  quizId?: number;

  @ManyToOne(() => Topic, (topic) => topic.chapters)
  topics: Topic;

  @OneToMany(() => Quiz, quiz => quiz.chapter, { cascade: true, eager: true })
quizzes: Quiz[];
}
