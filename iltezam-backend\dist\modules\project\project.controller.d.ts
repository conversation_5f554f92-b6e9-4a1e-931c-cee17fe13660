import { HttpStatus } from '@nestjs/common';
import { ProjectService } from './project.service';
import { CreateProjectDto, UpdateProjectDto } from './dtos/project.dto';
import { CreateDonationDto } from './dtos/donation.dto';
import { CreateVolunteerApplicationDto } from './dtos/volunteer-app.dto';
import { ApplicationStatus } from './entities/volunteer-application.entity';
export declare class ProjectController {
    private readonly projectService;
    constructor(projectService: ProjectService);
    createProject(createProjectDto: CreateProjectDto, req: any): Promise<import("./entities/project.entity").Project>;
    findAll(category?: string, location?: string): Promise<any[]>;
    getMyProjects(req: any): Promise<any[]>;
    getOrganizationProjects(req: any): Promise<import("./entities/project.entity").Project[]>;
    getProjectsByOrganizationId(organizationUserId: string): Promise<{
        statusCode: HttpStatus;
        message: string;
        data: any[];
    }>;
    findOne(id: string): Promise<any>;
    update(id: string, updateProjectDto: UpdateProjectDto, req: any): Promise<any>;
    remove(id: string, req: any): Promise<void>;
    closeProject(id: string, req: any): Promise<import("./entities/project.entity").Project>;
    createDonation(createDonationDto: CreateDonationDto, req: any): Promise<any>;
    getMyDonations(req: any): Promise<any[]>;
    getProjectDonations(projectId: string): Promise<any[]>;
    getDonationById(donationId: string): Promise<import("./entities/donation.entity").Donation | null>;
    getVolunteerProjectsWithPositions(): Promise<import("./entities/project.entity").Project[]>;
    getVolunteerPosition(projectId: string, positionId: string): Promise<any>;
    createApplication(projectId: string, positionId: string, createApplicationDto: CreateVolunteerApplicationDto, req: any): Promise<import("./entities/volunteer-application.entity").VolunteerApplication>;
    getAllApplicationAgainstProject(projectId: string): Promise<any[]>;
    getMyApplications(req: any): Promise<import("./entities/volunteer-application.entity").VolunteerApplication[]>;
    getProjectApplications(projectId: string): Promise<import("./entities/volunteer-application.entity").VolunteerApplication[]>;
    updateApplicationStatus(id: string, status: ApplicationStatus, req: any): Promise<import("./entities/volunteer-application.entity").VolunteerApplication>;
    getOrganizationDonations(req: any): Promise<any[]>;
}
