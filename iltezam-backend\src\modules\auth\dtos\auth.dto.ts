import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayNotEmpty,
  IsOptional,
  IsArray,
  IsBoolean,
  IsEmail,
  IsInt,
  IsNotEmpty,
  IsString,
  Length,
} from 'class-validator';

export class AuthLoginDto {
  @ApiProperty({ description: 'email', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty({ description: 'passwrod', example: 'shahid123' })
  @IsNotEmpty()
  @Length(8, 20)
  readonly password: string;
}

export class RegisterDto {
  @ApiProperty({ description: 'Email', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty({ description: 'Password', example: 'password123' })
  @IsNotEmpty()
  @Length(8, 20)
  readonly password: string;

  @ApiProperty({ description: 'Confirm password', example: 'password123' })
  @IsNotEmpty()
  @Length(8, 20)
  readonly confirmPassword: string;

  @ApiProperty({
    description: 'Is registering as organization',
    example: false,
  })
  @IsNotEmpty()
  @IsBoolean()
  readonly isOrganization: boolean;

  @ApiProperty({
    description: 'Organization name',
    example: 'Helping Hands NGO',
  })
  @IsNotEmpty()
  @IsString()
  readonly organizationName?: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: 'John Doe',
  })
  @IsString()
  readonly FullName: string;

  @IsOptional()
  @IsString()
  readonly designation?: string;
}

export class ForgotPasswordDto {
  @ApiProperty({ description: 'Email', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;
}

export class ResetNewPasswordDto {
  @ApiProperty({ description: 'Email address' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'New password', minLength: 8, maxLength: 20 })
  @IsString()
  @Length(8, 20)
  password: string;
}

export class verifyEmaildDto {
  @ApiProperty({ description: 'Email' })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'code' })
  @IsString()
  @Length(8, 20)
  code: string;
}

export class VerifyEmailDto {
  @ApiProperty({
    description: 'The unique email verification code sent to the user',
    example: 'e7e5971f-cac4-4aa4-b5ce-a23524c78455',
  })
  @IsNotEmpty()
  @IsString()
  readonly code: string;
}

export class ResendVerificationDto {
  @ApiProperty({
    description: 'Email address to resend the verification email',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;
}

export class CompanyDto {
  @ApiProperty({
    description: 'Email address of the company contact',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  readonly email: string;

  @ApiProperty({
    description: 'Name of the contact person',
    example: 'John Smith',
  })
  @IsNotEmpty()
  @IsString()
  readonly name: string;

  @ApiProperty({
    description: 'Registered name of the company',
    example: 'Tech Innovations Ltd.',
  })
  @IsNotEmpty()
  @IsString()
  readonly companyName: string;
}