import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from 'typeorm';

import { BaseEntityClass } from '../../auth/entities/base.entity';
import { User } from '../../auth/entities/user.entity';

@Entity({ name: 'notifications' })
export class Notification extends BaseEntityClass {
  @Column()
  title: string;

  @Column({ type: 'text' })
  content: string;

  @Column({ default: false })
  isRead: boolean;

  @ManyToOne(() => User, user => user.notifications, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: number;
}