"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Quiz = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../auth/entities/base.entity");
const chapter_entity_1 = require("./chapter.entity");
const question_entity_1 = require("./question.entity");
const user_entity_1 = require("../../auth/entities/user.entity");
const quiz_attempt_entity_1 = require("./quiz-attempt.entity");
let Quiz = class Quiz extends base_entity_1.BaseEntityClass {
    chapter;
    questions;
    createdBy;
    attempts;
};
exports.Quiz = Quiz;
__decorate([
    (0, typeorm_1.ManyToOne)(() => chapter_entity_1.Chapter, chapter => chapter.quizzes, { onDelete: 'CASCADE' }),
    __metadata("design:type", chapter_entity_1.Chapter)
], Quiz.prototype, "chapter", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => question_entity_1.Question, question => question.quiz, { cascade: true, eager: true }),
    __metadata("design:type", Array)
], Quiz.prototype, "questions", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.quizzes, { onDelete: 'CASCADE' }),
    __metadata("design:type", user_entity_1.User)
], Quiz.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => quiz_attempt_entity_1.QuizAttempt, (attempt) => attempt.quiz, { cascade: true }),
    __metadata("design:type", Array)
], Quiz.prototype, "attempts", void 0);
exports.Quiz = Quiz = __decorate([
    (0, typeorm_1.Entity)()
], Quiz);
//# sourceMappingURL=quiz.entity.js.map