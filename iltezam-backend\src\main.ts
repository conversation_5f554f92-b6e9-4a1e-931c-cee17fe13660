import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Server } from 'socket.io';
import { LiveChatService } from './modules/live-chat/live-chat.service';
import { json } from 'express';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

   app.use(json({
    verify: (req: any, res, buf) => {
      if (req.headers['stripe-signature']) {
        req.rawBody = buf.toString();
      }
    }
  }));
 
  app.setGlobalPrefix(`/api`);
  app.enableCors();
  const config = new DocumentBuilder()
    .setTitle('ILTEZAM')
    .setDescription('Fexrip API description')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT', // Optional: Specify the token format
      },
      'access-token', // Name of the security scheme
    )
    .addTag('ILTEZAM')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/api-docs', app, document);
  await app.listen(process.env.PORT ?? 3000);
  const server = app.getHttpServer();
  const io = new Server(server, { cors: { origin: '*' } });
  console.log('Socket.IO server is running');
  const liveChatService = app.get(LiveChatService);
  liveChatService.setIo(io);

  global.onlineUsers = new Map(); // Store connected users
  io.on("connection", (socket) => {
  console.log("New client connected: " + socket.id);

 
  socket.on("register", (userID) => {
    global.onlineUsers.set(userID, socket.id);
    console.log("registered user: " + userID + " with socket ID: " + socket.id);
    
  });

  
  socket.on("disconnect", () => {
    for (const [userID, socketID] of global.onlineUsers.entries()) {
      if (socketID === socket.id) {
        global.onlineUsers.delete(userID);
        break;
      }
    }
    console.log("Client disconnected: " + socket.id);
  });
});
  global.io = io;
  console.log(`Application is running on: ${await app.getUrl()} `);

}
bootstrap();
