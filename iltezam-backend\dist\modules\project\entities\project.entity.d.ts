import { BaseEntityClass } from '../../auth/entities/base.entity';
import { User } from '../../auth/entities/user.entity';
import { Position } from './position.entity';
import { Organization } from 'src/organiser/entities/organiser.entities';
export declare enum ProjectStatus {
    LIVE = "live",
    CLOSED = "closed"
}
export declare enum ProjectCategory {
    DONATION = "donation",
    VOLUNTEER = "volunteer",
    OTHER = "other"
}
export declare class Project extends BaseEntityClass {
    projectName: string;
    projectCategory: ProjectCategory;
    status: ProjectStatus;
    images: string[];
    fundRaisingGoal: number;
    fundsCollected: number;
    location: string;
    startDate: Date;
    finishDate: Date;
    projectDescription: string;
    thumbnailImage: string;
    totalClicks: number;
    createdBy: User;
    totalFilledPositions: string[];
    positions: Position[];
    organization: Organization;
    donations: any;
}
