"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePositionDto = exports.UpdateProjectDto = exports.PositionDto = exports.CreateProjectDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const project_entity_1 = require("../entities/project.entity");
class CreateProjectDto {
    projectName;
    projectCategory;
    status = project_entity_1.ProjectStatus.LIVE;
    images;
    thumbnailImage;
    fundRaisingGoal;
    fundsCollected;
    location;
    startDate;
    finishDate;
    projectDescription;
    requiredVolunteers;
    totalFilledPositions;
    positions;
}
exports.CreateProjectDto = CreateProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project name' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "projectName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project category',
        enum: project_entity_1.ProjectCategory,
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsEnum)(project_entity_1.ProjectCategory),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "projectCategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project status',
        enum: project_entity_1.ProjectStatus,
        default: project_entity_1.ProjectStatus.LIVE,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_entity_1.ProjectStatus),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project images',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "images", void 0);
__decorate([
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "thumbnailImage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Fund raising goal', required: false }),
    (0, class_validator_1.ValidateIf)((o) => o.projectCategory === project_entity_1.ProjectCategory.DONATION),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "fundRaisingGoal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project location', required: false }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "fundsCollected", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project location', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project start date' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project finish date' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "finishDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project description' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "projectDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of required volunteers',
        default: 1,
        required: false,
    }),
    (0, class_validator_1.ValidateIf)((o) => o.projectCategory === project_entity_1.ProjectCategory.VOLUNTEER),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "requiredVolunteers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total filled volunteer positions',
        required: false,
    }),
    (0, class_validator_1.ValidateIf)((o) => o.projectCategory === project_entity_1.ProjectCategory.VOLUNTEER),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "totalFilledPositions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project positions',
        type: [Object],
        required: false,
    }),
    (0, class_validator_1.ValidateIf)((o) => o.projectCategory === project_entity_1.ProjectCategory.VOLUNTEER),
    (0, class_validator_1.IsArray)(),
    (0, swagger_1.ApiProperty)({
        description: 'Project positions (only allowed for volunteer category)',
        type: [Object],
        required: false,
    }),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "positions", void 0);
class PositionDto {
    title;
    description;
    requiredSkills;
    volunteersNeeded;
}
exports.PositionDto = PositionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Position title' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PositionDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Position description' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PositionDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Required skills', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PositionDto.prototype, "requiredSkills", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of volunteers needed', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], PositionDto.prototype, "volunteersNeeded", void 0);
class UpdateProjectDto {
    projectName;
    projectCategory;
    status;
    images;
    fundRaisingGoal;
    location;
    startDate;
    finishDate;
    projectDescription;
    requiredVolunteers;
    positions;
    ProjectStatus = project_entity_1.ProjectStatus.LIVE;
}
exports.UpdateProjectDto = UpdateProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project name', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "projectName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project category',
        enum: project_entity_1.ProjectCategory,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_entity_1.ProjectCategory),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "projectCategory", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project status',
        enum: project_entity_1.ProjectStatus,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_entity_1.ProjectStatus),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project images',
        type: [String],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], UpdateProjectDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Fund raising goal', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateProjectDto.prototype, "fundRaisingGoal", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project location', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project start date', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project finish date', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "finishDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project description', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateProjectDto.prototype, "projectDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of required volunteers',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], UpdateProjectDto.prototype, "requiredVolunteers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project positions',
        type: [PositionDto],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => PositionDto),
    __metadata("design:type", Array)
], UpdateProjectDto.prototype, "positions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Project status',
        enum: project_entity_1.ProjectStatus,
        default: project_entity_1.ProjectStatus.LIVE,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_entity_1.ProjectStatus),
    __metadata("design:type", Object)
], UpdateProjectDto.prototype, "ProjectStatus", void 0);
class CreatePositionDto {
    positionName;
    requiredVolunteers;
    positionDescription;
}
exports.CreatePositionDto = CreatePositionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Position title' }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePositionDto.prototype, "positionName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Required number of volunteers' }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreatePositionDto.prototype, "requiredVolunteers", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Position description' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePositionDto.prototype, "positionDescription", void 0);
//# sourceMappingURL=project.dto.js.map