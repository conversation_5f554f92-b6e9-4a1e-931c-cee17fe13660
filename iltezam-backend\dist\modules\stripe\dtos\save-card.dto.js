"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SaveCardDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class SaveCardDetailsDto {
    cardNumber;
    expiryMonth;
    expiryYear;
    cardHolderName;
    cvv;
}
exports.SaveCardDetailsDto = SaveCardDetailsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Card number (16 digits)',
        example: '****************',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(16, 16, { message: 'Card number must be 16 digits' }),
    (0, class_validator_1.Matches)(/^[0-9]+$/, { message: 'Card number must contain only digits' }),
    __metadata("design:type", String)
], SaveCardDetailsDto.prototype, "cardNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Expiry month (2 digits)',
        example: '12',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(1, 2, { message: 'Expiry month must be 1 or 2 digits' }),
    (0, class_validator_1.Matches)(/^(0?[1-9]|1[0-2])$/, { message: 'Expiry month must be between 1 and 12' }),
    __metadata("design:type", String)
], SaveCardDetailsDto.prototype, "expiryMonth", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Expiry year (4 digits)',
        example: '2025',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Length)(4, 4, { message: 'Expiry year must be 4 digits' }),
    (0, class_validator_1.Matches)(/^20[2-9][0-9]$/, { message: 'Expiry year must be a valid future year' }),
    __metadata("design:type", String)
], SaveCardDetailsDto.prototype, "expiryYear", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Card holder name',
        example: 'John Doe',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SaveCardDetailsDto.prototype, "cardHolderName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Card holder cvv',
        example: '123',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SaveCardDetailsDto.prototype, "cvv", void 0);
//# sourceMappingURL=save-card.dto.js.map