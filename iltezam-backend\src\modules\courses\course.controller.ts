import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
  ForbiddenException,
  Patch,
  Delete,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Request,
  UnauthorizedException,
  ParseIntPipe,
  BadRequestException,
  NotFoundException,
  Req,
  Put,
} from '@nestjs/common';

import { ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { CourseService } from './course.service';

import {
  CreateCourseDto,
  CreateQuizDto,
  UpdateCourseDto,
  QuizAttemptDto,
} from './dtos/create.course.dto';
import { User } from 'src/modules/auth/entities/user.entity';
import { Chapter } from './entities/chapter.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthGuard } from '@nestjs/passport';
import { Public } from 'src/shared/decorators/publicRoute.decorator';

@Controller('courses')
export class CourseController {
  constructor(
    private readonly courseService: CourseService,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Chapter)
    private readonly chapterRepo: Repository<Chapter>,
  ) { }

  @Post('/create-course')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Create a new course (only for organizations)' })
  async createCourse(@Body() createCourseDto: CreateCourseDto, @Request() req) {
    try {
      const userId = req.user.id;
      const user = await this.userRepo.findOne({ where: { id: userId } });
      if (!user || !user.isOrganization) {
        throw new ForbiddenException('Only organizations can create courses');
      }

      const result = await this.courseService.createCourse(
        createCourseDto,
        user,
      );
      return {
        message: 'Course created successfully',
        course: result,
      };
    } catch (error) {
      console.error(error);
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/get-courseById/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async CourseById(@Param('id') id: string) {
    try {
      const course = await this.courseService.getSimpleCourseById(Number(id));
      if (!course) {
        throw new NotFoundException('Course not found');
      }

      return {
        statusCode: 200,
        message: 'success',
        data: course,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


  @Put('update-course/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async updateCourse(
    @Param('id') id: number,
    @Body() dto: UpdateCourseDto,
    @Req() req: any,
  ) {
    const creator = req.user;
    return this.courseService.updateCourseById(id, dto, creator);
  }

  @Delete('delete-course/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async deleteCourse(@Param('id') id: number): Promise<any> {
    try {
      await this.courseService.deleteCourseById(id);
      return { message: 'Course deleted successfully' };
    } catch (error) {
      throw new BadRequestException(
        error?.message || 'Failed to delete course'
      );
    }
  }

  @Public()
  @Get('/all')
  async findAllCourses(
    @Query('language') language?: string,
    @Query('tags') tags?: string,
    @Query('organizationName') organizationName?: string,
    @Query('courseName') courseName?: string,
    @Query('search') searchQuery?: string,
    @Query('status') status?: string,
  ) {
    try {
      

      //  Validate status
      const validStatus = status === 'active' || status === 'closed' ? status : 'active';

      const filters = {
        language,
        tags: tags ? tags.split(',') : undefined,
        organizationName,
        courseName,
        searchQuery,
        status: validStatus as 'active' | 'closed',
      };

      return await this.courseService.findAllWithCreatorInfo({

        filters,
      });
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/courseById/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getCourseById(@Param('id') courseId: string, @Req() req) {
    try {
      const userId = req.user.id;
      const course = await this.courseService.getCourseById(courseId, userId);
      if (!course) {
        throw new NotFoundException('Course not found');
      }
      return course;
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('update-progress')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async updateProgressAndReturnCourse(
    @Request() req,
    @Body()
    body: {
      courseId: number;
      topicId: number;
      chapterId: number;
      progressInSeconds: number;
      duration: number;
    },
  ) {
    const { courseId, topicId, chapterId, progressInSeconds, duration } = body;

    const userId = req.user?.id;

    return this.courseService.updateChapterProgress(
      courseId,
      topicId,
      chapterId,
      userId,
      progressInSeconds,
      duration,
    );
  }

  @Get('/in-progress')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getUserProgress(@Req() req) {
    try {
      const userId = req.user.id;
      return await this.courseService.getUserCourseProgress(userId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:userId/completed-courses')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  getCompletedCourses(@Param('userId', ParseIntPipe) userId: number) {
    return this.courseService.getAllCompletedCoursesByUser(userId);
  }

  @Get('/courses-by-type')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getCourses(
    @Req() req,
    @Query('type') type: 'all' | 'in-progress' | 'completed' | 'saved',
    @Query('language') language?: string, 
    @Query('tags') tags?: string[] | string, 
    @Query('organizationName') organizationName?: string,
    @Query('courseName') courseName?: string,
    @Query('searchQuery') searchQuery?: string,
  ) {
    const userId = req.user.id;

    const parsedTags = Array.isArray(tags)
      ? tags
      : typeof tags === 'string'
        ? tags.split(',').map((t) => t.trim())
        : [];

    const filters = {
      language,
      tags: parsedTags.length > 0 ? parsedTags : undefined,
      organizationName,
      courseName,
      searchQuery,
    };

    return this.courseService.findCoursesByType({
      userId,
      type,
      filters,
    });
  }


  @Get('status/:status')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get courses by status (active or closed)' })
  async findByStatus(
    @Param('status') status: 'active' | 'closed',
    @Req() req: any,
  ) {
    const userId = req.user.id;
    return this.courseService.findByStatus(status, userId);
  }

  @Patch('close-course/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async closeCourse(@Param('id') id: number, @Request() req) {
    const result = await this.courseService.closeCourse(+id, req.user.id);
    return { message: result }
  }

  // @Patch('/update-course/:id')
  // @UseGuards(AuthGuard('jwt'))
  // @ApiBearerAuth('access-token')
  // @ApiOperation({ summary: 'Update course fields' })
  // async updateCourse(@Param('id') id: string, @Body() body: UpdateCourseDto) {
  //   return this.courseService.updateCourseFields(Number(id), body);
  // }

  // @Patch('/update-topics/:id')
  // @UseGuards(AuthGuard('jwt'))
  // @ApiBearerAuth('access-token')
  // @ApiOperation({ summary: 'Update topic fields' })
  // async updateTopic(
  //   @Param('id') id: string,
  //   @Body() body: { topicTitle?: string; numberOfChapters?: number },
  // ) {
  //   return this.courseService.updateTopicFields(Number(id), body);
  // }

  // @Patch('/update-chapters/:id')
  // @UseGuards(AuthGuard('jwt'))
  // @ApiBearerAuth('access-token')
  // @ApiOperation({ summary: 'Update chapter fields' })
  // async updateChapter(
  //   @Param('id') id: string,
  //   @Body()
  //   body: {
  //     chapterTitle?: string;
  //     description?: string;
  //     mediaUrl?: string;
  //     prerequisiteChapterIds?: string[];
  //   },
  // ) {
  //   const newBody = {
  //     ...body,
  //     prerequisiteChapterIds: body.prerequisiteChapterIds?.map(Number),
  //   };
  //   return this.courseService.updateChapterFields(Number(id), newBody);
  // }

  @Post('/quizzes/attempt')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async attemptQuiz(@Body() body: QuizAttemptDto, @Request() req) {
    const user = await this.userRepo.findOne({ where: { id: req.user.id } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    return this.courseService.attemptQuiz(body, user);
  }

  @Get('/quizzes/:quizId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getQuizById(@Param('quizId') quizId: number) {
    return this.courseService.getQuizById(quizId);
  }

  @Get('/quizzes/:quizId/latest-attempt')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getLatestAttempt(@Param('quizId') quizId: number, @Request() req) {
    const userId = req.user.id;
    return this.courseService.getLatestQuizAttemptDetailed(userId, quizId);
  }

  // Toggle save/unsave a course
  @Post('/:courseId/toggle-save')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async toggleSaveCourse(
    @Param('courseId') courseId: string,
    @Body('userId') userId: number,
  ) {
    return this.courseService.toggleSaveCourse(userId, Number(courseId));
  }

  // Get all saved courses for a user
  @Get('/:userId/saved-courses')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getSavedCourses(@Param('userId') userId: string) {
    return this.courseService.getSavedCourses(Number(userId));
  }

  @Post('/enroll/:userId/:courseId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async enrollUser(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('courseId', ParseIntPipe) courseId: number,
  ) {
    return this.courseService.enrollUser(userId, courseId);
  }

  @Get('/:courseId/enrolled-users')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getEnrolledUsers(@Param('courseId') courseId: string) {
    return this.courseService.getEnrolledUsers(Number(courseId));
  }

  @Public()
  @Get('/:courseId/enrollment-count')
  async getEnrollmentCount(@Param('courseId') courseId: string) {
    return this.courseService.getEnrollmentCount(Number(courseId));
  }


  @Post('assign/:userId/:courseId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async assignCertificateToUser(
    @Param('userId') userId: number,
    @Param('courseId') courseId: number,
    @Body('pdfUrl') pdfUrl: string,
  ) {
    if (!pdfUrl) {
      throw new BadRequestException('PDF URL is required');
    }

    return this.courseService.assignCertificateToUser(userId, courseId, pdfUrl);
  }


}

function ReqUser(): (
  target: CourseController,
  propertyKey: 'getCourseById',
  parameterIndex: 1,
) => void {
  throw new Error('Function not implemented.');
}
