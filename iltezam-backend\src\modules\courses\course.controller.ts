import {
  <PERSON>,
  Post,
  Body,
  Get,
  Param,
  ForbiddenException,
  Patch,
  Delete,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Request,
  UnauthorizedException,
  ParseIntPipe,
  BadRequestException,
  NotFoundException,
  Req,
} from '@nestjs/common';

import { ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { CourseService } from './course.service';

import {
  CreateCourseDto,
  CreateQuizDto,
  UpdateCourseDto,
  QuizAttemptDto,
} from './dtos/create.course.dto';
import { User } from 'src/modules/auth/entities/user.entity';
import { Chapter } from './entities/chapter.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthGuard } from '@nestjs/passport';

@Controller('courses')
export class CourseController {
  constructor(
    private readonly courseService: CourseService,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
    @InjectRepository(Chapter)
    private readonly chapterRepo: Repository<Chapter>,
  ) {}

  @Post('/create-course')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Create a new course (only for organizations)' })
  async createCourse(@Body() createCourseDto: CreateCourseDto, @Request() req) {
    try {
      const userId = req.user.id;
      const user = await this.userRepo.findOne({ where: { id: userId } });
      if (!user || !user.isOrganization) {
        throw new ForbiddenException('Only organizations can create courses');
      }

      const result = await this.courseService.createCourse(
        createCourseDto,
        user,
      );
      return {
        message: 'Course created successfully',
        course: result,
      };
    } catch (error) {
      console.error(error);
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/get-courseById/:id')
  async CourseById(@Param('id') id: string) {
    try {
      const course = await this.courseService.getSimpleCourseById(Number(id));
      if (!course) {
        throw new NotFoundException('Course not found');
      }

      return {
        statusCode: 200,
        message: 'success',
        data: course,
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/all')
  async findAllCourses(
    @Request() req,
    @Query('language') language?: string,
    @Query('tags') tags?: string,
    @Query('organizationName') organizationName?: string,
    @Query('courseName') courseName?: string,
    @Query('search') searchQuery?: string,
  ) {
    try {
      const userId = req.user.id;
      const user = await this.userRepo.findOne({ where: { id: userId } });
      if (!user) {
        throw new ForbiddenException('Unauthorized access');
      }

      const filters = {
        language,
        tags: tags ? tags.split(',') : undefined,
        organizationName,
        courseName,
        searchQuery,
      };

      return await this.courseService.findAllWithCreatorInfo({
        userId,
        filters,
      });
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/courseById/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getCourseById(@Param('id') courseId: string, @Req() req) {
    try {
      const userId = req.user.id;
      const course = await this.courseService.getCourseById(courseId, userId);
      if (!course) {
        throw new NotFoundException('Course not found');
      }
      return course;
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('update-progress')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async updateProgressAndReturnCourse(
    @Request() req,
    @Body()
    body: {
      courseId: number;
      topicId: number;
      chapterId: number;
      progressInSeconds: number;
      duration: number;
    },
  ) {
    const { courseId, topicId, chapterId, progressInSeconds, duration } = body;

    const userId = req.user?.id;

    return this.courseService.updateChapterProgress(
      courseId,
      topicId,
      chapterId,
      userId,
      progressInSeconds,
      duration,
    );
  }

  @Get('/in-progress')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getUserProgress(@Req() req) {
    try {
      const userId = req.user.id;
      return await this.courseService.getUserCourseProgress(userId);
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('/:userId/completed-courses')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  getCompletedCourses(@Param('userId', ParseIntPipe) userId: number) {
    return this.courseService.getAllCompletedCoursesByUser(userId);
  }

  @Get('status/:status')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get courses by status (active or closed)' })
  async findByStatus(
    @Param('status') status: 'active' | 'closed',
    @Req() req: any,
  ) {
    const userId = req.user.id;
    return this.courseService.findByStatus(status, userId);
  }

  @Patch('/update-course/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Update course fields' })
  async updateCourse(@Param('id') id: string, @Body() body: UpdateCourseDto) {
    return this.courseService.updateCourseFields(Number(id), body);
  }

  @Patch('/update-topics/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Update topic fields' })
  async updateTopic(
    @Param('id') id: string,
    @Body() body: { topicTitle?: string; numberOfChapters?: number },
  ) {
    return this.courseService.updateTopicFields(Number(id), body);
  }

  @Patch('/update-chapters/:id')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Update chapter fields' })
  async updateChapter(
    @Param('id') id: string,
    @Body()
    body: {
      chapterTitle?: string;
      description?: string;
      mediaUrl?: string;
      prerequisiteChapterIds?: string[];
    },
  ) {
    const newBody = {
      ...body,
      prerequisiteChapterIds: body.prerequisiteChapterIds?.map(Number),
    };
    return this.courseService.updateChapterFields(Number(id), newBody);
  }

  // @Post('/create-quiz/:chapterId')
  // @UseGuards(AuthGuard('jwt'))
  // @ApiBearerAuth('access-token')
  // async createQuiz(
  //   @Param('chapterId') chapterId: string,
  //   @Body() data: CreateQuizDto,
  //   @Request() req,
  // ) {
  //   const chapter = await this.chapterRepo.findOne({
  //     where: { id: Number(chapterId) },
  //   });
  //   if (!chapter) {
  //     throw new HttpException('Chapter not found', HttpStatus.NOT_FOUND);
  //   }
  //   const user = await this.userRepo.findOne({ where: { id: req.user.id } });

  //   if (!user?.isOrganization) {
  //     throw new ForbiddenException('Only organizations can create quizzes');
  //   }

  //   return this.courseService.createQuiz(data, chapter, user);
  // }

  @Post('/quizzes/attempt')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async attemptQuiz(@Body() body: QuizAttemptDto, @Request() req) {
    const user = await this.userRepo.findOne({ where: { id: req.user.id } });
    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    if (user.isOrganization) {
      throw new ForbiddenException('Organizations cannot attempt quizzes');
    }

    return this.courseService.attemptQuiz(body, user);
  }

  @Get('/quizzes/:quizId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getQuizById(@Param('quizId') quizId: number) {
    return this.courseService.getQuizById(quizId);
  }

  @Get('/quizzes/:quizId/latest-attempt')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getLatestAttempt(@Param('quizId') quizId: number, @Request() req) {
    const userId = req.user.id;
    return this.courseService.getLatestQuizAttemptDetailed(userId, quizId);
  }

  // Toggle save/unsave a course
  @Post('/:courseId/toggle-save')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async toggleSaveCourse(
    @Param('courseId') courseId: string,
    @Body('userId') userId: number,
  ) {
    return this.courseService.toggleSaveCourse(userId, Number(courseId));
  }

  // Get all saved courses for a user
  @Get('/:userId/saved-courses')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getSavedCourses(@Param('userId') userId: string) {
    return this.courseService.getSavedCourses(Number(userId));
  }

  @Post('/enroll/:userId/:courseId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async enrollUser(
    @Param('userId', ParseIntPipe) userId: number,
    @Param('courseId', ParseIntPipe) courseId: number,
  ) {
    return this.courseService.enrollUser(userId, courseId);
  }

  @Get('/:courseId/enrolled-users')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async getEnrolledUsers(@Param('courseId') courseId: string) {
    return this.courseService.getEnrolledUsers(Number(courseId));
  }

  @Get('/:courseId/enrollment-count')
  async getEnrollmentCount(@Param('courseId') courseId: string) {
    return this.courseService.getEnrollmentCount(Number(courseId));
  }

  // @Get('course/:courseId/user/:userId')
  // @UseGuards(AuthGuard('jwt'))
  // @ApiBearerAuth('access-token')
  // async getCourseContent(
  //   @Param('courseId') courseId: number,
  //   @Param('userId') userId: number,
  // ) {
  //   return this.courseService.getCourseContent(courseId, userId);
  // }

  // @Post(':userId/:courseId/:chapterId/progress')
  // @UseGuards(AuthGuard('jwt'))
  // @ApiBearerAuth('access-token')
  // async updateChapterProgress(
  //   @Param('userId') userId: number,
  //   @Param('courseId', ParseIntPipe) courseId: number,
  //   @Param('chapterId', ParseIntPipe) chapterId: number,
  //   @Body('progress') progress: number,
  // ) {
  //   return this.courseService.updateChapterProgress(
  //     userId,
  //     courseId,
  //     chapterId,
  //     progress,
  //   );
  // }

  // // @Get(':userId/:courseId')
  // // @UseGuards(AuthGuard('jwt'))
  // // @ApiBearerAuth('access-token')
  // // async getCourseWithProgressAndUpdate(
  // //   @Param('userId') userId: number,
  // //   @Param('courseId', ParseIntPipe) courseId: number,
  // // ) {
  //   return this.courseService.getCourseWithUserProgress(userId, courseId);
  // }

  @Post('assign/:userId/:courseId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  async assignCertificateToUser(
    @Param('userId') userId: number,
    @Param('courseId') courseId: number,
    @Body('pdfUrl') pdfUrl: string,
  ) {
    if (!pdfUrl) {
      throw new BadRequestException('PDF URL is required');
    }

    return this.courseService.assignCertificateToUser(userId, courseId, pdfUrl);
  }
}

function ReqUser(): (
  target: CourseController,
  propertyKey: 'getCourseById',
  parameterIndex: 1,
) => void {
  throw new Error('Function not implemented.');
}
