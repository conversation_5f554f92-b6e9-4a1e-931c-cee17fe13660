"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeOfEthics = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../auth/entities/base.entity");
let CodeOfEthics = class CodeOfEthics extends base_entity_1.BaseEntityClass {
    image;
    organizationName;
    organizationImage;
    title;
    description;
    student;
    languages;
};
exports.CodeOfEthics = CodeOfEthics;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CodeOfEthics.prototype, "image", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CodeOfEthics.prototype, "organizationName", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CodeOfEthics.prototype, "organizationImage", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CodeOfEthics.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], CodeOfEthics.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], CodeOfEthics.prototype, "student", void 0);
__decorate([
    (0, typeorm_1.Column)('simple-array'),
    __metadata("design:type", Array)
], CodeOfEthics.prototype, "languages", void 0);
exports.CodeOfEthics = CodeOfEthics = __decorate([
    (0, typeorm_1.Entity)()
], CodeOfEthics);
//# sourceMappingURL=codeOfEthics.entity.js.map