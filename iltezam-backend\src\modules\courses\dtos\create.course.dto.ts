export class CreateCourseDto {
  courseName: string;
  language: string;
  whatTheyLearn: string;
  tags: string[];
  topicsTag: string[];
  thumbnailUrl?: string;
  status?: 'active' | 'closed';
  topics: CreateTopicDto[];
  estimatedDuration?: string;
  previewVideoUrl?: string;
}

export class CreateTopicDto {
  topicTitle: string;
  numberOfChapters: number;
  chapters: CreateChapterDto[];
}

export class CreateChapterDto {
  chapterTitle: string;
  description: string;
  mediaUrl?: string;
  duration?: number;
  prerequisiteChapterIds?: string[];
  quiz?: {
    questions: {
      questionText: string;
      options: string[];
      correctOption: string;
    }[];
  };
}

export class CreateQuizDto {
  chapterId: string;
  questions: {
    questionText: string;
    options: string[];
    correctOption: string;
  }[];
}

export class UpdateCourseDto {
  courseName?: string;
  language?: string;
  whatTheyLearn?: string;
  tags?: string[];
  topicsTag?: string[];
  thumbnailUrl?: string;
  status?: 'active' | 'closed';
  topics?: CreateTopicDto[];
}

export class QuizAttemptDto {
  userId: number;
  quizId: number;
  answers: number[];
}
