import { PaymentMethod, PaymentFrequency } from '../entities/donation.entity';
export declare class CreateDonationDto {
    projectId: string;
    paymentMethod: PaymentMethod;
    frequency: PaymentFrequency;
    amount: number;
    notes?: string;
}
export declare class DonationResponseDto {
    id: string;
    projectId: string;
    projectName: string;
    paymentMethod: PaymentMethod;
    frequency: PaymentFrequency;
    amount: number;
    status: string;
    transactionId?: string;
    paymentDate?: Date;
    notes?: string;
}
