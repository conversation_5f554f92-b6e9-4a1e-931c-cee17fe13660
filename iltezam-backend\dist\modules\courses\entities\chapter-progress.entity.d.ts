import { User } from 'src/modules/auth/entities/user.entity';
import { Chapter } from './chapter.entity';
import { Course } from './course.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
export declare class ChapterProgress extends BaseEntityClass {
    user: User;
    chapter: Chapter;
    course: Course;
    progressInSeconds: number;
    isCompleted: boolean;
    isLocked: boolean;
}
