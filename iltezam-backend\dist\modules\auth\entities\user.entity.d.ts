import { BaseEntityClass } from './base.entity';
import { Post } from 'src/modules/post/entities/post.entities';
import { Organization } from 'src/organiser/entities/organiser.entities';
import { Enrollment } from 'src/modules/courses/entities/enrollment.entity';
import { Profile } from 'src/modules/profile/entities/profile.entity';
import { Notification } from 'src/modules/notification/entities/notification.entity';
import { CourseProgress } from 'src/modules/courses/entities/course-progress.entity';
import { Course } from 'src/modules/courses/entities/course.entity';
import { Quiz } from 'src/modules/courses/entities/quiz.entity';
import { ProfileConnection } from 'src/modules/profile/entities/profile.connection.entity';
import { ChapterProgress } from 'src/modules/courses/entities/chapter-progress.entity';
export declare class User extends BaseEntityClass {
    email: string;
    FullName: string;
    password: string;
    designation: string;
    profilePicture: string;
    isOrganization: boolean;
    isFormFillUp: boolean;
    organizationName?: string;
    isEmailVerified: boolean;
    verificationCode: string;
    resetPasswordToken: string;
    forgotPasswordTokenMatch: boolean;
    resetPasswordTokenExpiry: Date;
    courseProgress: CourseProgress[];
    savedCourses: Course[];
    courses: Course[];
    chapterProgress: ChapterProgress[];
    quizzes: Quiz[];
    enrollments: Enrollment[];
    posts: Post[];
    likedPosts: Post[];
    sharedPosts: Post[];
    organizationDetails: Organization;
    profile: Profile;
    notifications: Notification[];
    sentConnections: ProfileConnection[];
    receivedConnections: ProfileConnection[];
}
