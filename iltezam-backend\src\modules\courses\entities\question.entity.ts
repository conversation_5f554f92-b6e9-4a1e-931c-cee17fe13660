import { <PERSON><PERSON>ty, <PERSON>ToOne, Column } from 'typeorm';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { Quiz } from './quiz.entity';
import { IsNotEmpty } from 'class-validator';

@Entity()
export class Question extends BaseEntityClass {
  @Column('text')
  @IsNotEmpty()
  questionText: string;

  @Column('text', { array: true })
  @IsNotEmpty()
  options: string[];

  @Column({ default: '' })
  @IsNotEmpty()
  correctOption: string;

  @ManyToOne(() => Quiz, (quiz) => quiz.questions)
  quiz: Quiz;
}
