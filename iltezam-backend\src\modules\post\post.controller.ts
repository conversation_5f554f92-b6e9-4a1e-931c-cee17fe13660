import {
  Controller,
  Post,
  Body,
  UseGuards,
  Request,
  HttpException,
  HttpStatus,
  Get,
  Param,
  Patch,
  Delete,
} from '@nestjs/common';
import { PostService } from './post.service';
import {
  CreateCommentDto,
  CreatePostDto,
  LikePostDto,
  sharePostDto,
} from './dto/post.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { Public } from 'src/shared/decorators/publicRoute.decorator';

@Controller('post')
export class PostController {
  constructor(private readonly postService: PostService) {}

  @Post('create')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Create a new post' })
  async createPost(@Body() createPostDto: CreatePostDto, @Request() req) {
    try {
      console.log(createPostDto);
      const userId = req.user.id;
      const result = await this.postService.createPost(createPostDto, userId);
      return {
        message: result.message,
        post: result.post,
      };
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


  // Edit post
@Patch('edit/:id')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiOperation({ summary: 'Edit a post' })
async editPost(
  @Param('id') id: number,
  @Body() updatePostDto: CreatePostDto, 
  @Request() req,
) {
  try {
    const userId = req.user.id;
    const result = await this.postService.editPost(id, updatePostDto, userId);
    return {
      message: result.message,
      post: result.post,
    };
  } catch (error) {
    console.log(error);
    throw new HttpException(
      error.message,
      error.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

// Delete post
@Delete('delete/:id')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiOperation({ summary: 'Delete a post' })
async deletePost(@Param('id') id: number, @Request() req) {
  try {
    const userId = req.user.id;
    const result = await this.postService.deletePost(id, userId);
    return { message: result.message };
  } catch (error) {
    console.log(error);
    throw new HttpException(
      error.message,
      error.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}


  @Get('all')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all posts' })
  async getAllPosts() {
    try {
      const result = await this.postService.getAllPosts();
      return {
        message: result.message,
        posts: result.posts,
      };
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('like')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Like post' })
  async likesPost(@Body() createPostDto: LikePostDto, @Request() req) {
    try {
      const result = await this.postService.like(createPostDto, req.user.id);
      return {
        statusCode: 200,
        message: result.message,
        data: {
          post: result.post,
          user: result.user,
        },
      };
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('share')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Share a post' })
  async sharePost(@Body() sharePostDto: { post_id: number }, @Request() req) {
    try {
      const result = await this.postService.share(sharePostDto, req.user.id);
      return {
        message: result.message,
        post: result.post,
        sharedUsers: result.sharedUsers,
      };
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('comment')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Add comment to a post' })
  async createComment(
    @Body() createCommentDto: CreateCommentDto,
    @Request() req,
  ) {
    try {
      const userId = req.user.id;
      const result = await this.postService.createComment({
        ...createCommentDto,
        user_id: userId,
      });

      return {
        statusCode: 200,
        message: result.message,
        data: {
          comment: result.comment, // ✅ make sure this key matches
        },
      };
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('user/:userId')
  @UseGuards(AuthGuard('jwt'))
  @ApiBearerAuth('access-token')
  @ApiOperation({ summary: 'Get all posts for a specific user' })
  async getPostsByUserId(@Param('userId') userId: number) {
    try {
      const result = await this.postService.getPostsByUserId(userId);
      return {
        message: result.message,
        posts: result.posts,
      };
    } catch (error) {
      console.log(error);
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
