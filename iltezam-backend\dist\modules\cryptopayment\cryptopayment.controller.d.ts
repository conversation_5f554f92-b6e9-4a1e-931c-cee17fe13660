import { CryptopaymentService } from './cryptopayment.service';
import { CreateCryptoPaymentDto } from './dtos/crypto-payment.dto';
import { CryptoPaymentResponseDto, SupportedCurrenciesResponseDto, TransactionStatusResponseDto } from './dtos/crypto-payment-response.dto';
export declare class CryptopaymentController {
    private readonly cryptopaymentService;
    constructor(cryptopaymentService: CryptopaymentService);
    createPayment(createPaymentDto: CreateCryptoPaymentDto): Promise<CryptoPaymentResponseDto>;
    getSupportedCurrencies(): Promise<SupportedCurrenciesResponseDto>;
    getTransactionStatus(txnId: string): Promise<TransactionStatusResponseDto>;
}
