import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CodeOfEthics } from './Entity/codeOfEthics.entity';
import { CreateCodeOfEthicsDto } from './DTO/codeOfEthics.dto';

@Injectable()
export class CodeOfEthicsService {
  constructor(
    @InjectRepository(CodeOfEthics)
    private readonly codeOfEthicsRepository: Repository<CodeOfEthics>,
  ) {}

  async create(
    createCodeOfEthicsDto: CreateCodeOfEthicsDto,
  ): Promise<CodeOfEthics> {
    const codeOfEthics = this.codeOfEthicsRepository.create(
      createCodeOfEthicsDto,
    );
    return await this.codeOfEthicsRepository.save(codeOfEthics);
  }

  async findAll(): Promise<CodeOfEthics[]> {
    return await this.codeOfEthicsRepository.find();
  }

  //   async findOne(id: number): Promise<CodeOfEthics> {
  //     const codeOfEthics = await this.codeOfEthicsRepository.findOne({
  //       where: { id },
  //     });
  //     if (!codeOfEthics) {
  //       throw new NotFoundException(`Code of Ethics with ID ${id} not found`);
  //     }
  //     return codeOfEthics;
  //   }

  //   async remove(id: number): Promise<void> {
  //     const result = await this.codeOfEthicsRepository.delete(id);
  //     if (result.affected === 0) {
  //       throw new NotFoundException(`Code of Ethics with ID ${id} not found`);
  //     }
  //   }
}
