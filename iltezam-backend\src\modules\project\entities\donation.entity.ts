import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { BaseEntityClass } from '../../auth/entities/base.entity';
import { User } from '../../auth/entities/user.entity';
import { Project } from './project.entity';

export enum PaymentMethod {
  STRIPE = 'stripe',
  CRYPTO = 'crypto',
}

export enum PaymentFrequency {
  ONE_TIME = 'one_time',
  MONTHLY = 'monthly',
}

export enum PaymentStatus {
  SUCCESS = 'success',
  FAILED = 'failed',
  PENDING = 'pending',
}

@Entity({ name: 'donations' })
export class Donation extends BaseEntityClass {
  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'donor_id' })
  donor: User;

  @ManyToOne(() => Project, { eager: true })
  @JoinColumn({ name: 'project_id' })
  project: Project;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
  })
  paymentMethod: PaymentMethod;

  @Column({
    type: 'enum',
    enum: PaymentFrequency,
  })
  frequency: PaymentFrequency;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  status: PaymentStatus;

  @Column({ nullable: true })
  transactionId: string;

  @Column({ nullable: true })
  paymentDate: Date;

  @Column({ nullable: true })
  notes: string;
}
