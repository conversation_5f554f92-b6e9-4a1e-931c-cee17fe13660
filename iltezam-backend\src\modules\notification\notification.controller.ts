import {
  Controller,
  Get,
  Post,
  Param,
  Body,
  UseGuards,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import { NotificationService } from './notification.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';

interface AuthenticatedRequest extends Request {
  user: {
    id: number;
  };
}

@ApiTags('Notifications')
@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all notifications for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Returns all notifications' })
  async getUserNotifications(@Req() req: AuthenticatedRequest) {
    const userId = req.user.id;
    const notifications =
      await this.notificationService.getUserNotifications(userId);
    return { notifications };
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a specific notification by ID' })
  @ApiResponse({ status: 200, description: 'Returns the notification' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async getNotificationById(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user.id;
    const notification = await this.notificationService.getNotificationById(
      id,
      userId,
    );
    return { notification };
  }

  @Post(':id/mark-as-read')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Mark a notification as read' })
  @ApiResponse({ status: 200, description: 'Notification marked as read' })
  @ApiResponse({ status: 404, description: 'Notification not found' })
  async markAsRead(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user.id;
    const notification = await this.notificationService.markAsRead(id, userId);
    return { notification, message: 'Notification marked as read' };
  }

  @Post('notify')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a notification for a user' })
  @ApiResponse({ status: 201, description: 'Notification created' })
  async notifyUser(
    @Body() notificationData: { title: string; content: string },
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user.id;
    const notification = await this.notificationService.notifyUser({
      userId,
      title: notificationData.title,
      content: notificationData.content,
    });
    return { notification, message: 'Notification created successfully' };
  }
}
