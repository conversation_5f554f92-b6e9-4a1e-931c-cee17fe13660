"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuizAttempt = void 0;
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../auth/entities/user.entity");
const base_entity_1 = require("../../auth/entities/base.entity");
const quiz_entity_1 = require("./quiz.entity");
const class_validator_1 = require("class-validator");
let QuizAttempt = class QuizAttempt extends base_entity_1.BaseEntityClass {
    user;
    quiz;
    score;
    attemptNumber;
    answers;
    attemptedAt;
    users;
};
exports.QuizAttempt = QuizAttempt;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    __metadata("design:type", user_entity_1.User)
], QuizAttempt.prototype, "user", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => quiz_entity_1.Quiz),
    __metadata("design:type", quiz_entity_1.Quiz)
], QuizAttempt.prototype, "quiz", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], QuizAttempt.prototype, "score", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], QuizAttempt.prototype, "attemptNumber", void 0);
__decorate([
    (0, typeorm_1.Column)('int', { array: true, nullable: true }),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", Array)
], QuizAttempt.prototype, "answers", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' }),
    __metadata("design:type", Date)
], QuizAttempt.prototype, "attemptedAt", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: false }),
    __metadata("design:type", user_entity_1.User)
], QuizAttempt.prototype, "users", void 0);
exports.QuizAttempt = QuizAttempt = __decorate([
    (0, typeorm_1.Entity)()
], QuizAttempt);
//# sourceMappingURL=quiz-attempt.entity.js.map