"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProfileConnection = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../auth/entities/base.entity");
const user_entity_1 = require("../../auth/entities/user.entity");
let ProfileConnection = class ProfileConnection extends base_entity_1.BaseEntityClass {
    requester;
    receiver;
    status;
    createdAt;
};
exports.ProfileConnection = ProfileConnection;
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.sentConnections),
    __metadata("design:type", user_entity_1.User)
], ProfileConnection.prototype, "requester", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.receivedConnections),
    __metadata("design:type", user_entity_1.User)
], ProfileConnection.prototype, "receiver", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'pending' }),
    __metadata("design:type", String)
], ProfileConnection.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ProfileConnection.prototype, "createdAt", void 0);
exports.ProfileConnection = ProfileConnection = __decorate([
    (0, typeorm_1.Entity)()
], ProfileConnection);
//# sourceMappingURL=profile.connection.entity.js.map