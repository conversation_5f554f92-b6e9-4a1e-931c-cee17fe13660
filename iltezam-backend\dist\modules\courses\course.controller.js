"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const course_service_1 = require("./course.service");
const create_course_dto_1 = require("./dtos/create.course.dto");
const user_entity_1 = require("../auth/entities/user.entity");
const chapter_entity_1 = require("./entities/chapter.entity");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const passport_1 = require("@nestjs/passport");
let CourseController = class CourseController {
    courseService;
    userRepo;
    chapterRepo;
    constructor(courseService, userRepo, chapterRepo) {
        this.courseService = courseService;
        this.userRepo = userRepo;
        this.chapterRepo = chapterRepo;
    }
    async createCourse(createCourseDto, req) {
        try {
            const userId = req.user.id;
            const user = await this.userRepo.findOne({ where: { id: userId } });
            if (!user || !user.isOrganization) {
                throw new common_1.ForbiddenException('Only organizations can create courses');
            }
            const result = await this.courseService.createCourse(createCourseDto, user);
            return {
                message: 'Course created successfully',
                course: result,
            };
        }
        catch (error) {
            console.error(error);
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async CourseById(id) {
        try {
            const course = await this.courseService.getSimpleCourseById(Number(id));
            if (!course) {
                throw new common_1.NotFoundException('Course not found');
            }
            return {
                statusCode: 200,
                message: 'success',
                data: course,
            };
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async findAllCourses(req, language, tags, organizationName, courseName, searchQuery) {
        try {
            const userId = req.user.id;
            const user = await this.userRepo.findOne({ where: { id: userId } });
            if (!user) {
                throw new common_1.ForbiddenException('Unauthorized access');
            }
            const filters = {
                language,
                tags: tags ? tags.split(',') : undefined,
                organizationName,
                courseName,
                searchQuery,
            };
            return await this.courseService.findAllWithCreatorInfo({
                userId,
                filters,
            });
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getCourseById(courseId, req) {
        try {
            const userId = req.user.id;
            const course = await this.courseService.getCourseById(courseId, userId);
            if (!course) {
                throw new common_1.NotFoundException('Course not found');
            }
            return course;
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateProgressAndReturnCourse(req, body) {
        const { courseId, topicId, chapterId, progressInSeconds, duration } = body;
        const userId = req.user?.id;
        return this.courseService.updateChapterProgress(courseId, topicId, chapterId, userId, progressInSeconds, duration);
    }
    async getUserProgress(req) {
        try {
            const userId = req.user.id;
            return await this.courseService.getUserCourseProgress(userId);
        }
        catch (error) {
            throw new common_1.HttpException(error.message || 'Internal server error', error.status || common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    getCompletedCourses(userId) {
        return this.courseService.getAllCompletedCoursesByUser(userId);
    }
    async findByStatus(status, req) {
        const userId = req.user.id;
        return this.courseService.findByStatus(status, userId);
    }
    async updateCourse(id, body) {
        return this.courseService.updateCourseFields(Number(id), body);
    }
    async updateTopic(id, body) {
        return this.courseService.updateTopicFields(Number(id), body);
    }
    async updateChapter(id, body) {
        const newBody = {
            ...body,
            prerequisiteChapterIds: body.prerequisiteChapterIds?.map(Number),
        };
        return this.courseService.updateChapterFields(Number(id), newBody);
    }
    async attemptQuiz(body, req) {
        const user = await this.userRepo.findOne({ where: { id: req.user.id } });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        if (user.isOrganization) {
            throw new common_1.ForbiddenException('Organizations cannot attempt quizzes');
        }
        return this.courseService.attemptQuiz(body, user);
    }
    async getQuizById(quizId) {
        return this.courseService.getQuizById(quizId);
    }
    async getLatestAttempt(quizId, req) {
        const userId = req.user.id;
        return this.courseService.getLatestQuizAttemptDetailed(userId, quizId);
    }
    async toggleSaveCourse(courseId, userId) {
        return this.courseService.toggleSaveCourse(userId, Number(courseId));
    }
    async getSavedCourses(userId) {
        return this.courseService.getSavedCourses(Number(userId));
    }
    async enrollUser(userId, courseId) {
        return this.courseService.enrollUser(userId, courseId);
    }
    async getEnrolledUsers(courseId) {
        return this.courseService.getEnrolledUsers(Number(courseId));
    }
    async getEnrollmentCount(courseId) {
        return this.courseService.getEnrollmentCount(Number(courseId));
    }
    async assignCertificateToUser(userId, courseId, pdfUrl) {
        if (!pdfUrl) {
            throw new common_1.BadRequestException('PDF URL is required');
        }
        return this.courseService.assignCertificateToUser(userId, courseId, pdfUrl);
    }
};
exports.CourseController = CourseController;
__decorate([
    (0, common_1.Post)('/create-course'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new course (only for organizations)' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_course_dto_1.CreateCourseDto, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "createCourse", null);
__decorate([
    (0, common_1.Get)('/get-courseById/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "CourseById", null);
__decorate([
    (0, common_1.Get)('/all'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Query)('language')),
    __param(2, (0, common_1.Query)('tags')),
    __param(3, (0, common_1.Query)('organizationName')),
    __param(4, (0, common_1.Query)('courseName')),
    __param(5, (0, common_1.Query)('search')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "findAllCourses", null);
__decorate([
    (0, common_1.Get)('/courseById/:id'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "getCourseById", null);
__decorate([
    (0, common_1.Post)('update-progress'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Request)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "updateProgressAndReturnCourse", null);
__decorate([
    (0, common_1.Get)('/in-progress'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "getUserProgress", null);
__decorate([
    (0, common_1.Get)('/:userId/completed-courses'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], CourseController.prototype, "getCompletedCourses", null);
__decorate([
    (0, common_1.Get)('status/:status'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, swagger_1.ApiOperation)({ summary: 'Get courses by status (active or closed)' }),
    __param(0, (0, common_1.Param)('status')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "findByStatus", null);
__decorate([
    (0, common_1.Patch)('/update-course/:id'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, swagger_1.ApiOperation)({ summary: 'Update course fields' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_course_dto_1.UpdateCourseDto]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "updateCourse", null);
__decorate([
    (0, common_1.Patch)('/update-topics/:id'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, swagger_1.ApiOperation)({ summary: 'Update topic fields' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "updateTopic", null);
__decorate([
    (0, common_1.Patch)('/update-chapters/:id'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    (0, swagger_1.ApiOperation)({ summary: 'Update chapter fields' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "updateChapter", null);
__decorate([
    (0, common_1.Post)('/quizzes/attempt'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_course_dto_1.QuizAttemptDto, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "attemptQuiz", null);
__decorate([
    (0, common_1.Get)('/quizzes/:quizId'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('quizId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "getQuizById", null);
__decorate([
    (0, common_1.Get)('/quizzes/:quizId/latest-attempt'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('quizId')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "getLatestAttempt", null);
__decorate([
    (0, common_1.Post)('/:courseId/toggle-save'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('courseId')),
    __param(1, (0, common_1.Body)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "toggleSaveCourse", null);
__decorate([
    (0, common_1.Get)('/:userId/saved-courses'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('userId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "getSavedCourses", null);
__decorate([
    (0, common_1.Post)('/enroll/:userId/:courseId'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('userId', common_1.ParseIntPipe)),
    __param(1, (0, common_1.Param)('courseId', common_1.ParseIntPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "enrollUser", null);
__decorate([
    (0, common_1.Get)('/:courseId/enrolled-users'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('courseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "getEnrolledUsers", null);
__decorate([
    (0, common_1.Get)('/:courseId/enrollment-count'),
    __param(0, (0, common_1.Param)('courseId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "getEnrollmentCount", null);
__decorate([
    (0, common_1.Post)('assign/:userId/:courseId'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    (0, swagger_1.ApiBearerAuth)('access-token'),
    __param(0, (0, common_1.Param)('userId')),
    __param(1, (0, common_1.Param)('courseId')),
    __param(2, (0, common_1.Body)('pdfUrl')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String]),
    __metadata("design:returntype", Promise)
], CourseController.prototype, "assignCertificateToUser", null);
exports.CourseController = CourseController = __decorate([
    (0, common_1.Controller)('courses'),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(2, (0, typeorm_1.InjectRepository)(chapter_entity_1.Chapter)),
    __metadata("design:paramtypes", [course_service_1.CourseService,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CourseController);
function ReqUser() {
    throw new Error('Function not implemented.');
}
//# sourceMappingURL=course.controller.js.map