"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CryptopaymentService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const coinpayments_1 = require("coinpayments");
let CryptopaymentService = class CryptopaymentService {
    configService;
    coinpaymentsClient;
    constructor(configService) {
        this.configService = configService;
        const publicKey = this.configService.get('COINPAYMENTS_PUBLIC_KEY');
        const privateKey = this.configService.get('COINPAYMENTS_PRIVATE_KEY');
        if (!publicKey || !privateKey) {
            throw new Error('CoinPayments API credentials are not configured');
        }
        const credentials = {
            key: publicKey,
            secret: privateKey,
        };
        this.coinpaymentsClient = new coinpayments_1.default(credentials);
    }
    async createPayment(createPaymentDto) {
        try {
            const transactionData = {
                currency1: 'USD',
                currency2: createPaymentDto.currency,
                amount: createPaymentDto.amount,
                buyer_email: createPaymentDto.buyerEmail,
                buyer_name: createPaymentDto.buyerName || '',
                item_name: createPaymentDto.itemName || 'Donation',
                item_number: createPaymentDto.itemNumber || '',
                invoice: createPaymentDto.invoice || '',
                custom: createPaymentDto.custom || '',
                ipn_url: createPaymentDto.ipnUrl || '',
                success_url: createPaymentDto.successUrl || '',
                cancel_url: createPaymentDto.cancelUrl || '',
            };
            const response = await this.coinpaymentsClient.createTransaction(transactionData);
            return {
                success: true,
                transactionId: response.txn_id,
                amount: response.amount,
                currency: createPaymentDto.currency,
                address: response.address,
                confirmsNeeded: parseInt(response.confirms_needed),
                timeout: response.timeout,
                checkoutUrl: response.checkout_url,
                statusUrl: response.status_url,
                qrcodeUrl: response.qrcode_url,
                message: 'Payment transaction created successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Failed to create crypto payment: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getSupportedCurrencies() {
        try {
            const response = await this.coinpaymentsClient.rates({ accepted: 1 });
            const currencies = Object.keys(response)
                .filter(key => response[key].accepted === 1 && response[key].is_fiat === 0)
                .map(key => ({
                code: key,
                name: response[key].name,
                rateBtc: response[key].rate_btc,
                txFee: response[key].tx_fee,
                status: response[key].status,
                confirms: parseInt(response[key].confirms),
                capabilities: response[key].capabilities || [],
            }));
            return {
                success: true,
                currencies,
                message: 'Supported currencies retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Failed to get supported currencies: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
    async getTransactionStatus(txnId) {
        try {
            const response = await this.coinpaymentsClient.getTx({ txid: txnId });
            return {
                success: true,
                transactionId: txnId,
                status: response.status,
                statusText: response.status_text,
                type: response.type,
                coin: response.coin,
                amount: response.amountf,
                received: response.receivedf,
                receivedConfirms: response.recv_confirms,
                paymentAddress: response.payment_address,
                timeCreated: new Date(response.time_created * 1000),
                timeExpires: new Date(response.time_expires * 1000),
                message: 'Transaction status retrieved successfully',
            };
        }
        catch (error) {
            throw new common_1.HttpException(`Failed to get transaction status: ${error.message}`, common_1.HttpStatus.BAD_REQUEST);
        }
    }
};
exports.CryptopaymentService = CryptopaymentService;
exports.CryptopaymentService = CryptopaymentService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], CryptopaymentService);
//# sourceMappingURL=cryptopayment.service.js.map