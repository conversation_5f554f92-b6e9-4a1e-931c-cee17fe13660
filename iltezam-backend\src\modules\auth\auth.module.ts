import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { User } from './entities/user.entity';
import { JwtStrategy } from './strategies/jwt.strategy';
import { UserService } from './user/user.service';
import { UserController } from './user/user.controller';
import { MailService } from '../../core/utils/sendemail';
import { Profile } from '../profile/entities/profile.entity';
import { Company } from './entities/company.entity';
import { SubscribedEmail } from './entities/subscribe.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Profile, Company, SubscribedEmail]),
    HttpModule,
    ConfigModule,
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
        secret: config.get('JWT_SECRET'),
        signOptions: { expiresIn: '7d' },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, UserService, MailService],
})
export class AuthModule {}
