import { Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';

@Injectable()
export class MailService {
  constructor() {
    sgMail.setApiKey(process.env.SENDGRID_API_KEY!);
  }

  async sendVerificationEmail(
    email: string,
    verificationCode: string,
  ): Promise<void> {
    const msg = {
      to: email,
      from: `ILTEZAM <${process.env.EMAIL}>`,
      subject: 'Your ILTEZAM Verification Code',
      html: `
        <div style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 30px;">
          <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
            <h2 style="text-align: center; color: #2c3e50;">Email Verification</h2>
            <p style="font-size: 16px; color: #333;">Hi there,</p>
            <p style="font-size: 16px; color: #333;">Thank you for registering with <strong>ILTEZAM</strong>.</p>
            <p style="font-size: 16px; color: #333;">Please use the following verification code to complete your signup:</p>
            <div style="text-align: center; margin: 20px 0;">
              <span style="display: inline-block; font-size: 24px; background-color: #f0f0f0; color: #2c3e50; padding: 10px 20px; border-radius: 6px; font-weight: bold; letter-spacing: 2px;">
                ${verificationCode}
              </span>
            </div>
            <p style="font-size: 16px; color: #333;">Enter this code in the app to verify your email address.</p>
            <p style="font-size: 16px; color: #333;">If you did not create an account, no further action is required.</p>
            <br>
            <p style="font-size: 16px; color: #333;">Best regards,</p>
            <p style="font-size: 16px; color: #333;"><strong>The ILTEZAM Team</strong></p>
            <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
            <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} ILTEZAM. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    try {
      await sgMail.send(msg);
      console.log('Verification email sent to:', email);
    } catch (error) {
      console.error('SendGrid Error:', error.response?.body || error.message);
      throw error;
    }
  }

  async sendResetPasswordEmail(
    email: string,
    resetCode: string,
  ): Promise<void> {
    const msg = {
      to: email,
      from: `ILTEZAM <${process.env.EMAIL}>`,
      subject: 'Reset Your ILTEZAM Password',
      html: `
        <div style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 30px;">
          <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
            <h2 style="text-align: center; color: #2c3e50;">Reset Your Password</h2>
            <p style="font-size: 16px; color: #333;">Hi there,</p>
            <p style="font-size: 16px; color: #333;">You recently requested to reset your password for your <strong>ILTEZAM</strong> account.</p>
            <p style="font-size: 16px; color: #333;">Here is your password reset code:</p>
            <div style="text-align: center; margin: 20px 0;">
              <span style="display: inline-block; font-size: 24px; background-color: #f0f0f0; color: #2c3e50; padding: 10px 20px; border-radius: 6px; font-weight: bold; letter-spacing: 2px;">
                ${resetCode}
              </span>
            </div>
            <p style="font-size: 16px; color: #333;">Enter this code in the app to reset your password.</p>
            <p style="font-size: 16px; color: #333;">If you didn't request a password reset, you can safely ignore this email.</p>
            <br>
            <p style="font-size: 16px; color: #333;">Best regards,</p>
            <p style="font-size: 16px; color: #333;"><strong>The ILTEZAM Team</strong></p>
            <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
            <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} ILTEZAM. All rights reserved.</p>
          </div>
        </div>
      `,
    };

    try {
      await sgMail.send(msg);
      console.log('Reset password email sent to:', email);
    } catch (error) {
      console.error('SendGrid Error:', error.response?.body || error.message);
      throw error;
    }
  }

  async sendSubscriptionEmail(email: string): Promise<void> {
    const msg = {
      to: email,
      from: `ILTEZAM <${process.env.EMAIL}>`,
      subject: 'Thank You for Subscribing to ILTEZAM',
      html: `
      <div style="font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 30px;">
        <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 8px; padding: 30px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);">
          <h2 style="text-align: center; color: #2c3e50;">Subscription Confirmed</h2>
          <p style="font-size: 16px; color: #333;">Hi there,</p>
          <p style="font-size: 16px; color: #333;">Thank you for subscribing to updates from <strong>ILTEZAM</strong>.</p>
          <p style="font-size: 16px; color: #333;">We're excited to keep you informed about our latest news, features, and special offers.</p>
          <p style="font-size: 16px; color: #333;">Stay tuned!</p>
          <br>
          <p style="font-size: 16px; color: #333;">Best regards,</p>
          <p style="font-size: 16px; color: #333;"><strong>The ILTEZAM Team</strong></p>
          <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;" />
          <p style="font-size: 12px; color: #999; text-align: center;">&copy; ${new Date().getFullYear()} ILTEZAM. All rights reserved.</p>
        </div>
      </div>
    `,
    };

    try {
      await sgMail.send(msg);
      console.log('Subscription email sent to:', email);
    } catch (error) {
      console.error('SendGrid Error:', error.response?.body || error.message);
      throw error;
    }
  }
}
