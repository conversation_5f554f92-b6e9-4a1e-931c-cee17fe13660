import { <PERSON>ti<PERSON>, Column, <PERSON>To<PERSON>ne, OneToMany, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'typeorm';
import { BaseEntityClass } from '../../auth/entities/base.entity';
import { User } from '../../auth/entities/user.entity';
import { Position } from './position.entity';
import { Organization } from 'src/organiser/entities/organiser.entities';
import { Donation } from './donation.entity'

export enum ProjectStatus {
  LIVE = 'live',
  CLOSED = 'closed',
}

export enum ProjectCategory {
  DONATION = 'donation',
  VOLUNTEER = 'volunteer',
  OTHER = 'other',
}

@Entity({ name: 'projects' })
export class Project extends BaseEntityClass {
  @Column()
  projectName: string;

  @Column({
    type: 'enum',
    enum: ProjectCategory,
  })
  projectCategory: ProjectCategory;

  @Column({
    type: 'enum',
    enum: ProjectStatus,
    default: ProjectStatus.LIVE,
  })
  status: ProjectStatus;

  @Column('simple-array', { nullable: true })
  images: string[];

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  fundRaisingGoal: number;

  @Column({ default: 0 })
  fundsCollected: number;

  @Column({ nullable: true })
  location: string;

  @Column({ type: 'timestamp' })
  startDate: Date;

  @Column({ type: 'timestamp' })
  finishDate: Date;

  @Column({ type: 'text' })
  projectDescription: string;

  @Column({ nullable: true })
  thumbnailImage: string;

  @Column({ default: 0 })
  totalClicks: number;

  @ManyToOne(() => User, { eager: true })
  @JoinColumn({ name: 'createdBy' })
  createdBy: User;

  @Column('simple-array', { nullable: true })
  totalFilledPositions: string[];

  @OneToMany(() => Position, (position) => position.project, {
    cascade: true,
    eager: true,
  })
  positions: Position[];

  @ManyToOne(() => Organization, (org) => org.projects, {
    eager: true,
    nullable: true,
  })
  @JoinColumn({ name: 'organization_id' })
  organization: Organization;
  donations: any;
}
