import { Repository } from 'typeorm';
import { Course } from './entities/course.entity';
import { CreateCourseDto, UpdateCourseDto } from './dtos/create.course.dto';
import { Quiz } from './entities/quiz.entity';
import { QuizAttempt } from './entities/quiz-attempt.entity';
import { CourseProgress } from './entities/course-progress.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { SavedCourse } from './entities/savedCourse.entity';
import { Enrollment } from './entities/enrollment.entity';
import { Chapter } from './entities/chapter.entity';
import { Topic } from './entities/topic.entity';
import { Question } from './entities/question.entity';
import { QuizAttemptDto } from './dtos/create.course.dto';
import { Certificate } from './entities/certificate.entity';
import { ChapterProgress } from './entities/chapter-progress.entity';
export declare class CourseService {
    private courseRepo;
    private topicRepo;
    private chapterRepo;
    private quizRepo;
    private questionRepo;
    private quizAttemptRepo;
    private courseProgressRepo;
    private chapterProgressRepo;
    private userRepo;
    private savedCourseRepo;
    private enrollmentRepo;
    private certificateRepo;
    constructor(courseRepo: Repository<Course>, topicRepo: Repository<Topic>, chapterRepo: Repository<Chapter>, quizRepo: Repository<Quiz>, questionRepo: Repository<Question>, quizAttemptRepo: Repository<QuizAttempt>, courseProgressRepo: Repository<CourseProgress>, chapterProgressRepo: Repository<ChapterProgress>, userRepo: Repository<User>, savedCourseRepo: Repository<SavedCourse>, enrollmentRepo: Repository<Enrollment>, certificateRepo: Repository<Certificate>);
    createCourse(dto: CreateCourseDto, creator: User): Promise<{
        message: string;
        course: any;
    }>;
    getSimpleCourseById(courseId: number): Promise<any>;
    updateCourseById(courseId: number, dto: UpdateCourseDto, creator: User): Promise<{
        message: string;
        course: any;
    }>;
    getCourseById(courseId: string, userId: number, watchedPercentages?: Record<number, number>): Promise<any>;
    updateChapterProgress(courseId: number, topicId: number, chapterId: number, userId: number, progressInSeconds: number, duration: number): Promise<any>;
    findAllWithCreatorInfo(params?: {
        userId?: number;
        filters?: {
            language?: string;
            tags?: string[];
            organizationName?: string;
            courseName?: string;
            searchQuery?: string;
            status?: 'active' | 'closed';
        };
    }): Promise<any[]>;
    getUserCourseProgress(userId: number): Promise<{
        courseId: number;
        courseName: string;
        language: string;
        tags: string[];
        topicstag: string[];
        isEnrolled: boolean;
        totalChapters: number;
        completedChapters: number;
        progressPercentage: number;
        lastWatchedChapter: {
            id: number;
            title: string;
        } | null;
        thumbnailUrl: string;
        whatYouLearn: string;
        createdBy: {
            id: number;
            profilePicture: string;
            organizationName: string | undefined;
        };
    }[]>;
    getAllCompletedCoursesByUser(userId: number): Promise<{
        courseId: number;
        courseTitle: string;
        thumbnailUrl: string;
        language: string;
        tags: string[];
        topicstag: string[];
        whatTheyLearn: string;
        userFullName: string;
        organizationName?: string;
        completionDate: Date;
        isEnrolled: boolean;
        totalChapters: number;
        completedChapters: number;
        progressPercentage: number;
        createdBy: {
            id: number;
            profilePicture?: string;
            organizationName?: string;
        };
    }[]>;
    getSavedCourses(userId: number): Promise<{
        user: {
            userId: number;
            fullName: string;
            email: string;
        };
        course: {
            id: number;
            courseName: string;
            language: string;
            whatTheyLearn: string;
            tags: string[];
            topicstag: string[];
            thumbnailUrl: string;
            status: "active" | "closed";
            estimatedDuration: string;
            previewVideoUrl: string;
            isEnrolled: boolean;
            createdBy: {
                id: number;
                organizationName: string | undefined;
                profilePicture: string;
            };
            topics: {
                topicId: number;
                topicTitle: string;
                chapters: {
                    chapterId: number;
                    chapterTitle: string;
                    description: string;
                    mediaUrl: string;
                }[];
            }[];
        };
    }[]>;
    findCoursesByType(params: {
        userId: number;
        type: 'all' | 'in-progress' | 'completed' | 'saved';
        filters?: {
            language?: string;
            tags?: string[];
            organizationName?: string;
            courseName?: string;
            searchQuery?: string;
        };
    }): Promise<any[]>;
    findByStatus(status: 'active' | 'closed', userId: number): Promise<any[]>;
    closeCourse(courseId: number, userId: number): Promise<string>;
    deleteCourseById(courseId: number): Promise<void>;
    attemptQuiz(body: QuizAttemptDto, user: User): Promise<{
        quizId: number;
        user: {
            id: number;
            name: string;
            email: string;
            designation: string;
        };
        questions: {
            questionText: string;
            options: string[];
            correctOptionIndex: number;
            userSelectedIndex: number;
            isCorrect: boolean;
        }[];
        score: number;
        total: number;
        attemptNumber: number;
    }>;
    getLatestQuizAttemptDetailed(userId: number, quizId: number): Promise<{
        quizId: number;
        user: {
            id: number;
            name: string;
            email: string;
        };
        questions: {
            questionText: string;
            options: string[];
            correctOptionIndex: number;
            userSelectedIndex: number;
            isCorrect: boolean;
        }[];
        score: number;
        total: number;
        attemptNumber: number;
        attemptedAt: Date;
    }>;
    getQuizById(quizId: number): Promise<any>;
    toggleSaveCourse(userId: number, courseId: number): Promise<{
        success: boolean;
        message: string;
        savedCourseId?: undefined;
    } | {
        success: boolean;
        message: string;
        savedCourseId: number;
    }>;
    enrollUser(userId: number, courseId: number): Promise<any>;
    getEnrolledUsers(courseId: number): Promise<{
        courseId: number;
        userId: number;
        fullName: string;
        email: string;
        enrolledAt: Date;
    }[]>;
    getEnrollmentCount(courseId: number): Promise<{
        courseId: number;
        enrollmentCount: number;
    }>;
    assignCertificateToUser(userId: number, courseId: number, pdfUrl: string): Promise<{
        message: string;
        certificate: Certificate;
    }>;
}
