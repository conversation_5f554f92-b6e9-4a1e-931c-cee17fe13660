import {
  Injectable,
  NotFoundException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UpdateProfileDto } from './dtos/create-profile.dto';
import { Profile } from './entities/profile.entity';
import { ProfileConnection } from './entities/profile.connection.entity';
import { User } from '../auth/entities/user.entity';
import { ChatRoom } from '../live-chat/entities/chat-room.entity';
import { OrganizationFollow } from 'src/organiser/entities/organization.follow.entities';

@Injectable()
export class ProfileService {
  constructor(
    @InjectRepository(Profile)
    private profileRepository: Repository<Profile>,

    @InjectRepository(ProfileConnection)
    private readonly connectionRepository: Repository<ProfileConnection>,

    @InjectRepository(User)
    private userRepository: Repository<User>,
     @InjectRepository(ChatRoom)
    private chatRoomRepository: Repository<ChatRoom>,

    @InjectRepository(OrganizationFollow)
    private orgFollowRepository: Repository<OrganizationFollow>,
  ) {}

  async getByUserId(userId: number, countsOnly = false): Promise<any> {
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });
    if (!profile) {
      throw new NotFoundException(`Profile for user ID ${userId} not found`);
    }
     // 1. Group chat rooms count
    const groupRoomsCount = await this.chatRoomRepository
  .createQueryBuilder('room')
  .where('room.isGroup = :isGroup', { isGroup: true })
  .andWhere(':userId = ANY(room.users)', { userId })
  .getCount();

    // 2. Organizations followed count
    const orgFollowCount = await this.orgFollowRepository.count({
      where: {
        follower: { id: userId },
        status: true, // Only accepted follows
      },
    });
    console.log('Group Rooms Count:', groupRoomsCount);
    console.log('Organization Follow Count:', orgFollowCount);
    
    
    if (countsOnly) {
      const connections = await this.getConnections(userId);
      const acceptedCount = connections.users.filter(
        (c) => c.status === 'accepted',
      ).length;
      const disconnectedCount = connections.users.filter(
        (c) => c.status === 'disconnected',
      ).length;
      return {
        profile,
         groupRoomsCount,
         orgFollowCount,
        connectionsCount: {
          accepted: acceptedCount,
          disconnected: disconnectedCount,
        },
      };
    } else {
      const connections = await this.getConnections(userId);
      return { profile, connections ,groupRoomsCount,
         orgFollowCount,};
    }
  }

  async getUserByUserId(userId: number): Promise<Profile> {
    console.log('Looking for profile of userId:', userId);
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });
    if (!profile) {
      throw new NotFoundException(`Profile for user ID ${userId} not found`);
    }
    return profile;
  }
  async updateByUserId(
    userId: number,
    updateProfileDto: UpdateProfileDto,
  ): Promise<Profile> {
    const existingProfile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });

    if (!existingProfile) {
      throw new NotFoundException(`Profile not found for user ID: ${userId}`);
    }
    for (const key in updateProfileDto) {
      if (key !== 'id' && key !== 'user') {
        existingProfile[key] = updateProfileDto[key];
      }
    }

    if (updateProfileDto.profilePicture !== undefined && existingProfile.user) {
      existingProfile.user.profilePicture = updateProfileDto.profilePicture;
      await this.userRepository.save(existingProfile.user);
    }

    return await this.profileRepository.save(existingProfile);
  }

  async findOne(id: number): Promise<Profile> {
    const profile = await this.profileRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!profile) {
      throw new NotFoundException(`Profile with ID ${id} not found`);
    }

    return profile;
  }

  async connectProfiles(requesterUserId: number, receiverUserId: number) {
    if (
      typeof requesterUserId !== 'number' ||
      typeof receiverUserId !== 'number'
    ) {
      throw new HttpException('Invalid input types', HttpStatus.BAD_REQUEST);
    }
    if (requesterUserId === receiverUserId) {
      throw new HttpException(
        'Cannot connect to yourself',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Find the profiles associated with these users
    const requesterProfile = await this.profileRepository.findOne({
      where: { user: { id: requesterUserId } },
    });
    const requesterUser = await this.userRepository.findOne({
      where: { id: requesterUserId },
    });
    if (!requesterUser) {
      throw new NotFoundException('Requester user not found');
    }
    const receiverUser = await this.userRepository.findOne({
      where: { id: receiverUserId },
    });
    if (!receiverUser) {
      throw new NotFoundException('Receiver user not found');
    }

    const receiverProfile = await this.profileRepository.findOne({
      where: { user: { id: receiverUserId } },
    });

    if (!receiverProfile) {
      throw new NotFoundException('Receiver profile not found');
    }
    if (!requesterProfile) {
      throw new NotFoundException('Requester profile not found');
    }
    console.log('req' + receiverProfile.id);
    console.log('rec' + requesterProfile.id);

    const existingConnection = await this.connectionRepository.findOne({
      where: [
        {
          requester: { id: requesterUserId },
          receiver: { id: receiverUserId },
        },
        {
          requester: { id: receiverUserId },
          receiver: { id: requesterUserId },
        },
      ],
    });
    console.log('here is the existing connection', existingConnection);

    if (existingConnection) {
      throw new HttpException(
        'Connection request already sent',
        HttpStatus.CONFLICT,
      );
    }

    // Create connection with Profile entities
    const connection = this.connectionRepository.create({
      requester: requesterUser,
      receiver: receiverUser,
      status: 'accepted',
    });

    return this.connectionRepository.save(connection);
  }
  async acceptConnection(connectionId: number) {
    const connection = await this.connectionRepository.findOne({
      where: { id: connectionId },
    });

    if (!connection) {
      throw new NotFoundException('Connection not found');
    }

    connection.status = 'accepted';
    return await this.connectionRepository.save(connection);
  }

  async rejectConnection(connectionId: number) {
    const connection = await this.connectionRepository.findOne({
      where: { id: connectionId },
    });

    if (!connection) {
      throw new NotFoundException('Connection not found');
    }

    return await this.connectionRepository.remove(connection);
  }

  async getConnections(userId: number) {
    const connections = await this.connectionRepository.find({
      where: [{ requester: { id: userId } }, { receiver: { id: userId } }],
      relations: ['requester', 'receiver'],
    });

    const result = connections.map((conn) => {
      const otherUser =
        conn.requester.id === userId ? conn.receiver : conn.requester;

      return {
        user: {
          id: otherUser.id,
          email: otherUser.email,
          FullName: otherUser.FullName,
          designation: otherUser.designation,
          profilePicture: otherUser.profilePicture,
          isOrganization: otherUser.isOrganization,
          organizationName: otherUser.organizationName,
          created_at: otherUser.created_at,
          updated_at: otherUser.updated_at,
        },
        connectionId: conn.id,
        status: conn.status,
        createdAt: conn.createdAt,
        updatedAt: conn.updated_at,
      };
    });

    return { users: result };
  }
}
