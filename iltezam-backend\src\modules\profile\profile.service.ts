import {
  Injectable,
  NotFoundException,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UpdateProfileDto } from './dtos/create-profile.dto';
import { Profile } from './entities/profile.entity';
import { ProfileConnection } from './entities/profile.connection.entity';
import { User } from '../auth/entities/user.entity';
import { ChatRoom } from '../live-chat/entities/chat-room.entity';
import { OrganizationFollow } from 'src/organiser/entities/organization.follow.entities';

@Injectable()
export class ProfileService {
  constructor(
    @InjectRepository(Profile)
    private profileRepository: Repository<Profile>,

    @InjectRepository(ProfileConnection)
    private readonly connectionRepository: Repository<ProfileConnection>,

    @InjectRepository(User)
    private userRepository: Repository<User>,
     @InjectRepository(ChatRoom)
    private chatRoomRepository: Repository<ChatRoom>,

    @InjectRepository(OrganizationFollow)
    private orgFollowRepository: Repository<OrganizationFollow>,
  ) {}

  async getByUserId(userId: number, countsOnly = false): Promise<any> {
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });
    if (!profile) {
      throw new NotFoundException(`Profile for user ID ${userId} not found`);
    }
     // 1. Group chat rooms count
    const groupRoomsCount = await this.chatRoomRepository
  .createQueryBuilder('room')
  .where('room.isGroup = :isGroup', { isGroup: true })
  .andWhere(':userId = ANY(room.users)', { userId })
  .getCount();

    // 2. Organizations followed count
    const orgFollowCount = await this.orgFollowRepository.count({
      where: {
        follower: { id: userId },
        status: true, // Only accepted follows
      },
    });
    console.log('Group Rooms Count:', groupRoomsCount);
    console.log('Organization Follow Count:', orgFollowCount);
    
    
    if (countsOnly) {
      const connections = await this.getConnections(userId);
      const acceptedCount = connections.users.filter(
        (c) => c.status === 'accepted',
      ).length;
      const disconnectedCount = connections.users.filter(
        (c) => c.status === 'disconnected',
      ).length;
      return {
        profile,
         groupRoomsCount,
         orgFollowCount,
        connectionsCount: {
          accepted: acceptedCount,
          disconnected: disconnectedCount,
        },
      };
    } else {
      let connections = await this.getConnections(userId);
      // Do not filter connections here to include all statuses
      let profileWithStatus = {
        status: connections.users.length > 0 ? connections.users[0].status : 'Not Connected',
        ...profile,
      };
      return { profile: profileWithStatus, connections ,groupRoomsCount,
         orgFollowCount,};
  }


}

  async getUserByUserId(userId: number): Promise<any> {
    console.log('Looking for profile of userId:', userId);
    const profile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });
    if (!profile) {
      throw new NotFoundException(`Profile for user ID ${userId} not found`);
    }
    let connections = await this.getConnections(userId);
    // Do not filter connections here to include all statuses
    let profileWithStatus = {
      status: connections.users.length > 0 ? connections.users[0].status : 'none',
      ...profile,
    };
    return { profile: profileWithStatus, connections };
  }
  async updateByUserId(
    userId: number,
    updateProfileDto: UpdateProfileDto,
  ): Promise<Profile> {
    const existingProfile = await this.profileRepository.findOne({
      where: { user: { id: userId } },
      relations: ['user'],
    });

    if (!existingProfile) {
      throw new NotFoundException(`Profile not found for user ID: ${userId}`);
    }
    for (const key in updateProfileDto) {
      if (key !== 'id' && key !== 'user') {
        existingProfile[key] = updateProfileDto[key];
      }
    }

    if (updateProfileDto.profilePicture !== undefined && existingProfile.user) {
      existingProfile.user.profilePicture = updateProfileDto.profilePicture;
      await this.userRepository.save(existingProfile.user);
    }

    return await this.profileRepository.save(existingProfile);
  }

  async findOne(id: number): Promise<Profile> {
    const profile = await this.profileRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (!profile) {
      throw new NotFoundException(`Profile with ID ${id} not found`);
    }

    return profile;
  }

async connectProfiles(requesterUserId: number, receiverUserId: number) {
  if (typeof requesterUserId !== 'number' || typeof receiverUserId !== 'number') {
    throw new HttpException('Invalid input types', HttpStatus.BAD_REQUEST);
  }
  if (requesterUserId === receiverUserId) {
    throw new HttpException('Cannot connect to yourself', HttpStatus.BAD_REQUEST);
  }

  const [requesterUser, receiverUser] = await Promise.all([
    this.userRepository.findOne({ where: { id: requesterUserId } }),
    this.userRepository.findOne({ where: { id: receiverUserId } }),
  ]);
  if (!requesterUser || !receiverUser) {
    throw new NotFoundException('User not found');
  }

  const existing = await this.connectionRepository.findOne({
    where: [
      { requester: { id: requesterUserId }, receiver: { id: receiverUserId } },
      { requester: { id: receiverUserId }, receiver: { id: requesterUserId } }
    ],
  });

  if (existing) {
    if (existing.status === 'disconnected' || existing.status === 'rejected') {
      existing.status = 'pending';
    } else {
      existing.status = (existing.status === 'pending' ? 'disconnected' : 'disconnected');
    }
    return this.connectionRepository.save(existing);
  }

  const connection = this.connectionRepository.create({
    requester: requesterUser,
    receiver: receiverUser,
    status: 'pending',
  });
  return this.connectionRepository.save(connection);
}


  

  async acceptConnection(connectionId: number) {
    const connection = await this.connectionRepository.findOne({
      where: { id: connectionId },
    });

    if (!connection) {
      throw new NotFoundException('Connection not found');
    }

    connection.status = 'accepted';
    return await this.connectionRepository.save(connection);
  }

  async rejectConnection(connectionId: number) {
    const connection = await this.connectionRepository.findOne({
      where: { id: connectionId },
    });

    if (!connection) {
      throw new NotFoundException('Connection not found');
    }

    return await this.connectionRepository.remove(connection);
  }

  async getConnections(userId: number) {
    const connections = await this.connectionRepository.find({
      where: [{ requester: { id: userId } }, { receiver: { id: userId } }],
      relations: ['requester', 'receiver'],
    });

    const result = connections.map((conn) => {
      const otherUser =
        conn.requester.id === userId ? conn.receiver : conn.requester;

      return {
        user: {
          id: otherUser.id,
          email: otherUser.email,
          FullName: otherUser.FullName,
          designation: otherUser.designation,
          profilePicture: otherUser.profilePicture,
          isOrganization: otherUser.isOrganization,
          organizationName: otherUser.organizationName,
          created_at: otherUser.created_at,
          updated_at: otherUser.updated_at,
        },
        connectionId: conn.id,
        status: conn.status,
        createdAt: conn.createdAt,
        updatedAt: conn.updated_at,
      };
    });

    return { users: result };
  }



  async getReceivedConnectionRequests(userId: number) {
    const connectionRequests = await this.connectionRepository
      .createQueryBuilder('connection')
      .leftJoinAndSelect('connection.requester', 'requester')
      .where('connection.status = :status', { status: 'pending' })
      .andWhere('connection.receiver = :userId', { userId })
      .getMany();

    const result = connectionRequests.map((conn) => {
      const requester = conn.requester;
      return {
        connectionId: conn.id,
        status: conn.status,
        createdAt: conn.createdAt,
        updatedAt: conn.updated_at,
        requester: {
          id: requester.id,
          email: requester.email,
          FullName: requester.FullName,
          designation: requester.designation,
          profilePicture: requester.profilePicture,
          isOrganization: requester.isOrganization,
          organizationName: requester.organizationName,
          created_at: requester.created_at,
          updated_at: requester.updated_at,
        },
      };
    });

    return { users: result };
  }
} 