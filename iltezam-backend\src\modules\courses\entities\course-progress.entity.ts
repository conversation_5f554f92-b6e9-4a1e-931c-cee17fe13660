import { Entity, Column, ManyToOne } from 'typeorm';
import { User } from 'src/modules/auth/entities/user.entity';
import { Course } from './course.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';

@Entity()
export class CourseProgress extends BaseEntityClass {
  @ManyToOne(() => User, (user) => user.courseProgress, { eager: false })
  user: User;

  @ManyToOne(() => Course, { eager: false })
  course: Course;

  @Column('int', { array: true, default: [] })
  completedChapterIds: number[];

  @Column('jsonb', { default: {} })
  videoProgress: { [chapterId: string]: number };

  @Column('int', { array: true, default: [] })
  completedQuizIds: number[];

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  lastUpdated: Date;

  @Column({ type: 'int', nullable: true })
  lastWatchedChapterId: number | null;

  @Column({ default: false })
  isCompleted: boolean;
}
