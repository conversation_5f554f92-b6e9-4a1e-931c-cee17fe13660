import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from 'src/modules/auth/entities/user.entity';
import { Organization } from './../organiser/entities/organiser.entities';
import { OrganizationFollow } from './../organiser/entities/organization.follow.entities';

import {
  Body,
  Controller,
  HttpException,
  Post,
  Put,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import { CreateOrganizationDto } from './dto/organiser.dto';
import { profile } from 'console';

@Injectable()
export class organiserService {
  projectRepo: any;
  constructor(
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,

    @InjectRepository(Organization)
    private readonly orgRepo: Repository<Organization>,

    @InjectRepository(OrganizationFollow)
    private readonly followRepo: Repository<OrganizationFollow>,
  ) {}

  async createOrganiser(
    createUserDto: CreateOrganizationDto,
    user: any,
  ): Promise<Organization> {
    try {
      const fullUser = await this.userRepo.findOne({ where: { id: user.id } });
      if (!fullUser) {
        throw new HttpException('User not found', HttpStatus.NOT_FOUND);
      }

      // 🔒 Check if organization already exists for this user
      const existingOrg = await this.orgRepo.findOne({
        where: { user: { id: user.id } },
      });
      if (existingOrg) {
        throw new HttpException(
          'Organization already exists for this user',
          HttpStatus.CONFLICT,
        );
      }

      fullUser.isFormFillUp = true;

      const newOrg = this.orgRepo.create({
        ...createUserDto,
        user: fullUser,
      });

      return await this.orgRepo.save(newOrg);
    } catch (e) {
      throw new HttpException(
        e.message,
        e.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrganiser(userId: number) {
    try {
      const organizations = await this.orgRepo.find({
        relations: ['user', 'followers', 'followers.follower'],
      });

      const data = organizations.map((organization) => {
        const isFollowing = organization.followers?.some(
          (follow) => follow.follower?.id === userId && follow.status === true,
        );

        return {
          id: organization.id,
          created_at: organization.created_at,
          updated_at: organization.updated_at,
          email: organization.email,
          organizationName: organization.organizationName,
          organizationNameArabic: organization.organizationNameArabic,
          country: organization.country,
          website: organization.website,
          licenseFilePath: organization.licenseFilePath,
          contactName: organization.contactName,
          contactPhone: organization.contactPhone,
          contactTitle: organization.contactTitle,
          contactEmail: organization.contactEmail,
          goodGovernance: organization.goodGovernance,
          transparencyReporting: organization.transparencyReporting,
          sustainableFunding: organization.sustainableFunding,
          impactMeasurement: organization.impactMeasurement,
          shortBio: organization.shortBio,
          organizationImage: organization.organizationImage,
          organizationTags: organization.organizationTags,
          status: isFollowing,
          user: {
            id: organization.user.id,
            created_at: organization.user.created_at,
            updated_at: organization.user.updated_at,
            email: organization.user.email,
            FullName: organization.user.FullName,
            designation: organization.user.designation,
            profilePicture: organization.user.profilePicture,
            isOrganization: organization.user.isOrganization,
            organizationName: organization.user.organizationName,
            isEmailVerified: organization.user.isEmailVerified,
          },
          followers:
            organization.followers?.map((f) => ({
              id: f.id,
              created_at: f.created_at,
              updated_at: f.updated_at,
              followedAt: f.followedAt,
              status: f.status,
              follower: {
                email: f.follower?.email,
                profilePicture: f.follower?.profilePicture,
                isOrganization: f.follower?.isOrganization,
                organizationName: f.follower?.organizationName,
              },
            })) || [],
        };
      });

      return data;
    } catch (e) {
      throw new HttpException(
        e.message,
        e.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrganiserById(id: number) {
    try {
      const org = await this.orgRepo.findOne({
        where: { id },
        relations: ['user'],
      });
      if (!org) {
        throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
      }
      return org;
    } catch (e) {
      throw new HttpException(
        e.message,
        e.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

 async followOrganization(orgId: number, user: any) {
  try {
    const organization = await this.orgRepo.findOne({ where: { id: orgId } });
    const fullUser = await this.userRepo.findOne({ where: { id: user.id } });

    if (!organization || !fullUser) {
      throw new HttpException('Organization or user not found', HttpStatus.NOT_FOUND);
    }

    const existingFollow = await this.followRepo.findOne({
      where: {
        organization: { id: orgId },
        follower: { id: user.id },
      },
    });

    if (existingFollow) {
      // Toggle the follow status
      existingFollow.status = !existingFollow.status;
      existingFollow.followedAt = new Date();
      const updatedFollow = await this.followRepo.save(existingFollow);

      return {
        statusCode: 200,
        message: updatedFollow.status
          ? 'Organization followed successfully'
          : 'Organization unfollowed successfully',
        data: {
          status: updatedFollow.status,
          follow: updatedFollow,
        },
      };
    }

    // First time follow
    const follow = this.followRepo.create({
      organization,
      follower: fullUser,
      status: true,
      followedAt: new Date(),
    });
    const savedFollow = await this.followRepo.save(follow);

    return {
      statusCode: 200,
      message: 'Organization followed successfully',
      data: {
        status: true,
        follow: savedFollow,
      },
    };
  } catch (e) {
    throw new HttpException(e.message, e.status || HttpStatus.INTERNAL_SERVER_ERROR);
  }
}

async getOrganizationProfile(orgId: number, userId: number) {
  try {
    const organization = await this.orgRepo.findOne({
      where: { id: orgId },
      relations: ['user', 'followers', 'followers.follower'],
    });

    if (!organization) {
      throw new HttpException('Organization not found', HttpStatus.NOT_FOUND);
    }

    const isFollowing = organization.followers?.some(
      (follow) => follow.follower?.id === userId && follow.status === true,
    );

    return {
      id: organization.id,
      email: organization.email,
      organizationName: organization.organizationName,
      organizationNameArabic: organization.organizationNameArabic,
      country: organization.country,
      website: organization.website,
      licenseFilePath: organization.licenseFilePath,
      contactName: organization.contactName,
      contactPhone: organization.contactPhone,
      contactTitle: organization.contactTitle,
      contactEmail: organization.contactEmail,
      goodGovernance: organization.goodGovernance,
      transparencyReporting: organization.transparencyReporting,
      sustainableFunding: organization.sustainableFunding,
      impactMeasurement: organization.impactMeasurement,
      shortBio: organization.shortBio,
      organizationImage: organization.organizationImage,
      organizationTags: organization.organizationTags,
      user: {
        id: organization.user.id,
        name: organization.user.organizationName,
        email: organization.user.email,
      },
      followers:
        organization.followers?.map((f) => ({
          id: f.follower?.id,
          email: f.follower?.email,
          fullName: f.follower?.FullName || null,
          status: f.status, // ✅ Added status here
        })) || [],
      status: isFollowing,
    };
  } catch (e) {
    throw new HttpException(
      e.message,
      e.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

async getProjectCategoryByOrgAndProject(orgId: number, projectId: number) {
  try {
    const project = await this.projectRepo.findOne({
      where: {
        id: projectId,
        organization: { id: orgId },
      },
      relations: ['organization', 'positions', 'createdBy'], // Include other relations if needed
    });

    if (!project) {
      throw new HttpException(
        'Project not found under this organization',
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      organizationId: orgId,
      projectId: project.id,
      projectName: project.projectName,
      projectCategory: project.projectCategory,
      status: project.status,
      images: project.images,
      fundRaisingGoal: project.fundRaisingGoal,
      fundsCollected: project.fundsCollected,
      location: project.location,
      startDate: project.startDate,
      finishDate: project.finishDate,
      projectDescription: project.projectDescription,
      thumbnailImage: project.thumbnailImage,
      totalClicks: project.totalClicks,
      totalFilledPositions: project.totalFilledPositions,
      positions: project.positions, // You can also map these if you need specific fields
      organization: {
        id: project.organization?.id,
        organizationName: project.organization?.organizationName,
      },
    };
  } catch (e) {
    throw new HttpException(
      e.message,
      e.status || HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}

}
