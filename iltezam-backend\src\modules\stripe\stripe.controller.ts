import { Body, Controller, Get, Post, Req, UseGuards ,Headers,Res} from '@nestjs/common';
import { StripeService } from './stripe.service';
import { CreatePaymentIntentDto } from './dtos/stripe-payment.dto';
import { SaveCardDetailsDto } from './dtos/save-card.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Public } from 'src/shared/decorators/publicRoute.decorator';
import { log } from 'console';

@Controller('stripe')
export class StripeController {
  constructor(private readonly stripeService: StripeService) {}

  @Public()
  @Post('create-payment-intent')
  async createPaymentIntent(@Body() createPaymentIntentDto: CreatePaymentIntentDto) {
    return this.stripeService.createPaymentIntent(createPaymentIntentDto);
  }

  @UseGuards(JwtAuthGuard)
  @Post('save-card')
  async saveCardDetails(
    @Body() saveCardDetailsDto: SaveCardDetailsDto,
    @Req() req,
  ) {
    const userId = req.user.id;
    return this.stripeService.saveCardDetails(saveCardDetailsDto, userId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('saved-cards')
  async getSavedCards(@Req() req) {
    const userId = req.user.id;
    return this.stripeService.getSavedCards(userId);
  }

@Public()
@Post('webhook')
async handleWebhook(
  @Req() req,
  @Res() res,
  @Headers('stripe-signature') signature: string,
) {
  try {
    console.log('🔔 Webhook received, verifying...');
    
    const event = this.stripeService.constructEvent(req.rawBody, signature); // ✅ Use rawBody
    const response = await this.stripeService.handleWebhook(event);
    
    return res.status(200).json(response);
  } catch (err) {
    console.error('❌ Webhook error:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }
}


}
