"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CourseService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const course_entity_1 = require("./entities/course.entity");
const quiz_entity_1 = require("./entities/quiz.entity");
const quiz_attempt_entity_1 = require("./entities/quiz-attempt.entity");
const course_progress_entity_1 = require("./entities/course-progress.entity");
const user_entity_1 = require("../auth/entities/user.entity");
const savedCourse_entity_1 = require("./entities/savedCourse.entity");
const enrollment_entity_1 = require("./entities/enrollment.entity");
const chapter_entity_1 = require("./entities/chapter.entity");
const topic_entity_1 = require("./entities/topic.entity");
const question_entity_1 = require("./entities/question.entity");
const certificate_entity_1 = require("./entities/certificate.entity");
const chapter_progress_entity_1 = require("./entities/chapter-progress.entity");
let CourseService = class CourseService {
    courseRepo;
    topicRepo;
    chapterRepo;
    quizRepo;
    questionRepo;
    quizAttemptRepo;
    courseProgressRepo;
    chapterProgressRepo;
    userRepo;
    savedCourseRepo;
    enrollmentRepo;
    certificateRepo;
    constructor(courseRepo, topicRepo, chapterRepo, quizRepo, questionRepo, quizAttemptRepo, courseProgressRepo, chapterProgressRepo, userRepo, savedCourseRepo, enrollmentRepo, certificateRepo) {
        this.courseRepo = courseRepo;
        this.topicRepo = topicRepo;
        this.chapterRepo = chapterRepo;
        this.quizRepo = quizRepo;
        this.questionRepo = questionRepo;
        this.quizAttemptRepo = quizAttemptRepo;
        this.courseProgressRepo = courseProgressRepo;
        this.chapterProgressRepo = chapterProgressRepo;
        this.userRepo = userRepo;
        this.savedCourseRepo = savedCourseRepo;
        this.enrollmentRepo = enrollmentRepo;
        this.certificateRepo = certificateRepo;
    }
    async createCourse(dto, creator) {
        const course = this.courseRepo.create({
            courseName: dto.courseName,
            language: dto.language,
            whatTheyLearn: dto.whatTheyLearn,
            tags: dto.tags,
            topicsTag: dto.topicsTag,
            thumbnailUrl: dto.thumbnailUrl,
            status: dto.status || 'active',
            estimatedDuration: dto.estimatedDuration,
            previewVideoUrl: dto.previewVideoUrl,
            createdBy: creator,
        });
        course.topics = [];
        for (const topicDto of dto.topics) {
            const topic = this.topicRepo.create({
                topicTitle: topicDto.topicTitle,
                numberOfChapters: topicDto.numberOfChapters,
            });
            topic.chapters = [];
            for (const chapterDto of topicDto.chapters) {
                const chapter = this.chapterRepo.create({
                    chapterTitle: chapterDto.chapterTitle,
                    description: chapterDto.description,
                    mediaUrl: chapterDto.mediaUrl,
                    duration: chapterDto.duration,
                    prerequisiteChapterIds: chapterDto.prerequisiteChapterIds || [],
                });
                if (chapterDto.quiz) {
                    const quiz = this.quizRepo.create({ createdBy: creator });
                    quiz.questions = chapterDto.quiz.questions.map((q) => this.questionRepo.create({
                        questionText: q.questionText,
                        options: q.options,
                        correctOption: q.correctOption,
                    }));
                    chapter.quizzes = [quiz];
                }
                topic.chapters.push(chapter);
            }
            course.topics.push(topic);
        }
        await this.courseRepo.save(course);
        const savedCourse = await this.courseRepo.findOne({
            where: { id: course.id },
            relations: {
                topics: {
                    chapters: {
                        quizzes: {
                            questions: true,
                        },
                    },
                },
            },
        });
        const { topics, ...courseData } = savedCourse;
        return {
            message: 'Course created successfully',
            course: {
                ...courseData,
                topics: topics.map((topic) => ({
                    topicTitle: topic.topicTitle,
                    numberOfChapters: topic.numberOfChapters,
                    chapters: topic.chapters.map((ch) => ({
                        chapterTitle: ch.chapterTitle,
                        description: ch.description,
                        mediaUrl: ch.mediaUrl,
                        duration: ch.duration,
                        quiz: ch.quizzes?.[0]
                            ? {
                                quizId: ch.quizzes[0].id,
                                questions: ch.quizzes[0].questions.map((q) => ({
                                    questionText: q.questionText,
                                    options: q.options,
                                    correctOption: q.correctOption,
                                })),
                            }
                            : undefined,
                    })),
                })),
            },
        };
    }
    async getSimpleCourseById(courseId) {
        const course = await this.courseRepo.findOne({
            where: { id: courseId },
            relations: [
                'topics',
                'topics.chapters',
                'topics.chapters.quizzes',
                'createdBy',
                'createdBy.organizationDetails',
            ],
        });
        if (!course) {
            return null;
        }
        const enrollmentCount = await this.enrollmentRepo.count({
            where: { course: { id: courseId }, isEnroll: true },
        });
        const totalDuration = course.topics
            .flatMap((topic) => topic.chapters)
            .reduce((sum, chapter) => sum + (Number(chapter.duration) || 0), 0);
        const topicsWithChapters = course.topics.map((topic) => ({
            topicId: topic.id,
            topicTitle: topic.topicTitle,
            chapters: topic.chapters.map((chapter) => ({
                id: chapter.id,
                title: chapter.chapterTitle,
                description: chapter.description,
                duration: Number(chapter.duration) || 0,
                mediaUrl: chapter.mediaUrl || null,
                quiz: (chapter.quizzes && chapter.quizzes.length > 0)
                    ? {
                        quizId: chapter.quizzes[0].id,
                        questions: chapter.quizzes[0].questions.map((q) => ({
                            questionText: q.questionText,
                            options: q.options,
                            correctOption: q.correctOption,
                        })),
                    }
                    : undefined,
            })),
        }));
        return {
            courseId: course.id,
            courseTitle: course.courseName,
            thumbnailUrl: course.thumbnailUrl,
            whatYouWillLearn: course.whatTheyLearn,
            tags: course.tags,
            topicstag: course.topicsTag,
            status: course.status,
            language: course.language,
            estimatedDuration: totalDuration,
            previewVideoUrl: course.previewVideoUrl,
            enrollmentCount,
            createdBy: {
                id: course.createdBy?.id,
                organizationName: course.createdBy?.organizationName,
                profilePicture: course.createdBy?.organizationDetails?.profilePicture,
            },
            topics: topicsWithChapters,
        };
    }
    async updateCourseById(courseId, dto, creator) {
        const existingCourse = await this.courseRepo.findOne({
            where: { id: courseId },
            relations: {
                topics: {
                    chapters: {
                        quizzes: {
                            questions: true,
                        },
                    },
                },
            },
        });
        if (!existingCourse) {
            throw new common_1.NotFoundException('Course not found');
        }
        if (dto.courseName !== undefined)
            existingCourse.courseName = dto.courseName;
        if (dto.language !== undefined)
            existingCourse.language = dto.language;
        if (dto.whatTheyLearn !== undefined)
            existingCourse.whatTheyLearn = dto.whatTheyLearn;
        if (dto.tags !== undefined)
            existingCourse.tags = dto.tags;
        if (dto.topicsTag !== undefined)
            existingCourse.topicsTag = dto.topicsTag;
        if (dto.thumbnailUrl !== undefined)
            existingCourse.thumbnailUrl = dto.thumbnailUrl;
        if (dto.status !== undefined)
            existingCourse.status = dto.status;
        if (dto.previewVideoUrl !== undefined)
            existingCourse.previewVideoUrl = dto.previewVideoUrl;
        if (dto.estimatedDuration !== undefined)
            existingCourse.estimatedDuration = (dto.estimatedDuration);
        if (dto.topics) {
            for (const topic of existingCourse.topics) {
                for (const chapter of topic.chapters) {
                    for (const quiz of chapter.quizzes || []) {
                        if (quiz.questions?.length) {
                            await this.questionRepo.remove(quiz.questions);
                        }
                        await this.quizRepo.remove(quiz);
                    }
                    await this.chapterRepo.remove(chapter);
                }
                await this.topicRepo.remove(topic);
            }
            existingCourse.topics = [];
            const topics = [];
            for (const topicDto of dto.topics) {
                const topic = this.topicRepo.create({
                    topicTitle: topicDto.topicTitle,
                    numberOfChapters: topicDto.numberOfChapters,
                });
                topic.courses = existingCourse;
                const chapters = [];
                for (const chapterDto of topicDto.chapters) {
                    const chapter = this.chapterRepo.create({
                        chapterTitle: chapterDto.chapterTitle,
                        description: chapterDto.description,
                        mediaUrl: chapterDto.mediaUrl,
                        duration: chapterDto.duration,
                        prerequisiteChapterIds: chapterDto.prerequisiteChapterIds || [],
                    });
                    chapter.topics = topic;
                    if (chapterDto.quiz) {
                        const quiz = this.quizRepo.create({ createdBy: creator });
                        quiz.chapter = chapter;
                        quiz.questions = chapterDto.quiz.questions.map((q) => this.questionRepo.create({
                            questionText: q.questionText,
                            options: q.options,
                            correctOption: q.correctOption,
                        }));
                        chapter.quizzes = [quiz];
                    }
                    chapters.push(chapter);
                }
                topic.chapters = chapters;
                topics.push(topic);
            }
            existingCourse.topics = topics;
        }
        await this.courseRepo.save(existingCourse);
        const updated = await this.courseRepo.findOne({
            where: { id: courseId },
            relations: {
                topics: {
                    chapters: {
                        quizzes: {
                            questions: true,
                        },
                    },
                },
            },
        });
        const { topics, ...courseData } = updated;
        return {
            message: 'Course updated successfully',
            course: {
                ...courseData,
                topics: topics.map((topic) => ({
                    topicTitle: topic.topicTitle,
                    numberOfChapters: topic.numberOfChapters,
                    chapters: topic.chapters.map((ch) => ({
                        chapterTitle: ch.chapterTitle,
                        description: ch.description,
                        mediaUrl: ch.mediaUrl,
                        duration: ch.duration,
                        quiz: ch.quizzes?.[0]
                            ? {
                                quizId: ch.quizzes[0].id,
                                questions: ch.quizzes[0].questions.map((q) => ({
                                    questionText: q.questionText,
                                    options: q.options,
                                    correctOption: q.correctOption,
                                })),
                            }
                            : undefined,
                    })),
                })),
            },
        };
    }
    async getCourseById(courseId, userId, watchedPercentages = {}) {
        const course = await this.courseRepo.findOne({
            where: { id: Number(courseId) },
            relations: [
                'topics',
                'topics.chapters',
                'topics.chapters.quizzes',
                'createdBy',
                'createdBy.organizationDetails',
            ],
        });
        if (!course)
            return null;
        const enrollment = await this.enrollmentRepo.findOne({
            where: {
                user: { id: userId },
                course: { id: Number(courseId) },
                isEnroll: true,
            },
            relations: ['user', 'course'],
        });
        const isEnrolled = !!enrollment;
        const enrollmentCount = await this.enrollmentRepo.count({
            where: { course: { id: Number(courseId) }, isEnroll: true },
        });
        const saved = await this.savedCourseRepo.findOne({
            where: {
                user: { id: userId },
                course: { id: Number(courseId) },
            },
        });
        const isSaved = !!saved;
        let progress = await this.courseProgressRepo.findOne({
            where: { user: { id: userId }, course: { id: Number(courseId) } },
        });
        if (!progress) {
            progress = this.courseProgressRepo.create({
                user: { id: userId },
                course: { id: Number(courseId) },
                completedChapterIds: [],
                videoProgress: {},
                completedQuizIds: [],
                lastWatchedChapterId: null,
            });
        }
        let updated = false;
        for (const [chapterIdStr, percentWatched] of Object.entries(watchedPercentages)) {
            const chapterId = Number(chapterIdStr);
            if (percentWatched >= 90 &&
                !progress.completedChapterIds.includes(chapterId)) {
                progress.completedChapterIds.push(chapterId);
                progress.lastWatchedChapterId = chapterId;
                updated = true;
            }
            progress.videoProgress[chapterIdStr] = percentWatched;
        }
        if (updated) {
            await this.courseProgressRepo.save(progress);
        }
        const completedIds = progress.completedChapterIds;
        const totalDuration = course.topics
            .flatMap((topic) => topic.chapters)
            .reduce((sum, chapter) => sum + (Number(chapter.duration) || 0), 0);
        const topicsWithChapters = course.topics.map((topic) => {
            const sortedChapters = topic.chapters.sort((a, b) => a.id - b.id);
            return {
                topicId: topic.id,
                topicTitle: topic.topicTitle,
                chapters: sortedChapters.map((chapter, index) => {
                    const isCompleted = completedIds.includes(chapter.id);
                    let isUnlocked = false;
                    if (index === 0) {
                        isUnlocked = true;
                    }
                    else {
                        const prevChapterId = sortedChapters[index - 1].id;
                        isUnlocked = completedIds.includes(prevChapterId);
                    }
                    return {
                        id: chapter.id,
                        title: chapter.chapterTitle,
                        description: chapter.description,
                        duration: Number(chapter.duration) || 0,
                        mediaUrl: isUnlocked ? chapter.mediaUrl : null,
                        isLocked: !isUnlocked,
                        isCompleted,
                        quizIds: chapter.quizzes?.map((quiz) => quiz.id) || [],
                    };
                }),
            };
        });
        return {
            isEnrolled,
            isSaved,
            enrollmentCount,
            courseId: course.id,
            courseTitle: course.courseName,
            thumbnailUrl: course.thumbnailUrl,
            whatYouWillLearn: course.whatTheyLearn,
            tags: course.tags,
            topicstag: course.topicsTag,
            language: course.language,
            estimatedDuration: totalDuration,
            previewVideoUrl: course.previewVideoUrl,
            lastWatchedChapterId: progress.lastWatchedChapterId,
            createdBy: {
                id: course.createdBy?.id,
                organizationName: course.createdBy?.organizationName,
                profilePicture: course.createdBy?.organizationDetails?.profilePicture,
            },
            topics: topicsWithChapters,
        };
    }
    async updateChapterProgress(courseId, topicId, chapterId, userId, progressInSeconds, duration) {
        const course = await this.courseRepo.findOne({
            where: { id: courseId },
            relations: [
                'topics',
                'topics.chapters',
                'topics.chapters.quizzes',
                'createdBy',
                'createdBy.organizationDetails',
            ],
        });
        if (!course)
            throw new common_1.NotFoundException('Course not found');
        const topic = course.topics.find((t) => t.id === topicId);
        if (!topic)
            throw new common_1.NotFoundException('Topic not found in course');
        const chapters = topic.chapters.sort((a, b) => a.id - b.id);
        const chapterIndex = chapters.findIndex((c) => c.id === chapterId);
        const chapter = chapters[chapterIndex];
        if (!chapter)
            throw new common_1.NotFoundException('Chapter not found in topic');
        const watchedPercentage = duration
            ? Math.floor((progressInSeconds / duration) * 100)
            : 0;
        let courseProgress = await this.courseProgressRepo.findOne({
            where: { user: { id: userId }, course: { id: courseId } },
        });
        if (!courseProgress) {
            courseProgress = this.courseProgressRepo.create({
                user: { id: userId },
                course: { id: courseId },
                completedChapterIds: [],
                videoProgress: {},
                completedQuizIds: [],
                lastWatchedChapterId: null,
            });
        }
        courseProgress.videoProgress[chapterId] = watchedPercentage;
        if (watchedPercentage >= 90 &&
            !courseProgress.completedChapterIds.includes(chapterId)) {
            courseProgress.completedChapterIds.push(chapterId);
            courseProgress.lastWatchedChapterId = chapterId;
        }
        await this.courseProgressRepo.save(courseProgress);
        let chapterProgress = await this.chapterProgressRepo.findOne({
            where: {
                user: { id: userId },
                chapter: { id: chapterId },
            },
        });
        if (!chapterProgress) {
            chapterProgress = this.chapterProgressRepo.create({
                user: { id: userId },
                chapter: { id: chapterId },
                isLocked: false,
            });
        }
        chapterProgress.isCompleted = watchedPercentage >= 90;
        chapterProgress.progressInSeconds = progressInSeconds;
        await this.chapterProgressRepo.save(chapterProgress);
        const nextChapter = chapters[chapterIndex + 1];
        if (nextChapter) {
            let nextChapterProgress = await this.chapterProgressRepo.findOne({
                where: {
                    user: { id: userId },
                    chapter: { id: nextChapter.id },
                },
            });
            if (!nextChapterProgress) {
                nextChapterProgress = this.chapterProgressRepo.create({
                    user: { id: userId },
                    chapter: { id: nextChapter.id },
                    isLocked: false,
                });
            }
            else {
                nextChapterProgress.isLocked = false;
            }
            await this.chapterProgressRepo.save(nextChapterProgress);
        }
        const allChapterProgress = await this.chapterProgressRepo.find({
            where: { user: { id: userId } },
            relations: ['chapter'],
        });
        const progressMap = new Map();
        allChapterProgress.forEach((p) => {
            if (p.chapter) {
                progressMap.set(p.chapter.id, p);
            }
        });
        const enrollment = await this.enrollmentRepo.findOne({
            where: {
                user: { id: userId },
                course: { id: courseId },
                isEnroll: true,
            },
        });
        const isEnrolled = !!enrollment;
        const enrollmentCount = await this.enrollmentRepo.count({
            where: { course: { id: courseId }, isEnroll: true },
        });
        const totalDuration = course.topics
            .flatMap((t) => t.chapters)
            .reduce((sum, ch) => sum + (Number(ch.duration) || 0), 0);
        const topicsWithChapters = course.topics.map((t) => {
            const sortedChapters = t.chapters.sort((a, b) => a.id - b.id);
            const chapters = sortedChapters.map((ch, index) => {
                const chProgress = progressMap.get(ch.id);
                const isCompleted = chProgress?.isCompleted ?? false;
                const watched = courseProgress.videoProgress[ch.id] || 0;
                let isLocked;
                if (chProgress && typeof chProgress.isLocked === 'boolean') {
                    isLocked = chProgress.isLocked;
                }
                else if (index === 0) {
                    isLocked = false;
                }
                else {
                    const prevChapter = sortedChapters[index - 1];
                    const prevProgress = progressMap.get(prevChapter.id);
                    const prevCompleted = prevProgress?.isCompleted ?? false;
                    isLocked = !prevCompleted;
                }
                return {
                    id: ch.id,
                    title: ch.chapterTitle,
                    description: ch.description,
                    duration: Number(ch.duration) || 0,
                    mediaUrl: !isLocked ? ch.mediaUrl : null,
                    isLocked,
                    isCompleted,
                    watchedPercentage: Math.floor(watched),
                    quizIds: ch.quizzes?.map((quiz) => quiz.id) || [],
                };
            });
            return {
                topicId: t.id,
                topicTitle: t.topicTitle,
                chapters,
            };
        });
        return {
            isEnrolled,
            enrollmentCount,
            courseId: course.id,
            courseTitle: course.courseName,
            thumbnailUrl: course.thumbnailUrl,
            whatYouWillLearn: course.whatTheyLearn,
            tags: course.tags,
            language: course.language,
            estimatedDuration: totalDuration,
            previewVideoUrl: course.previewVideoUrl,
            lastWatchedChapterId: courseProgress.lastWatchedChapterId,
            createdBy: {
                id: course.createdBy?.id,
                organizationName: course.createdBy?.organizationName,
                profilePicture: course.createdBy?.organizationDetails?.profilePicture,
            },
            topics: topicsWithChapters,
        };
    }
    async findAllWithCreatorInfo(params) {
        const { userId, filters } = params || {};
        let courses = await this.courseRepo.find({
            relations: ['topics', 'topics.chapters', 'createdBy.organizationDetails'],
        });
        if (filters) {
            courses = courses.filter((course) => {
                let matches = true;
                if ((filters.status || 'active') !== course.status) {
                    matches = false;
                }
                if (filters.searchQuery) {
                    const searchTerm = filters.searchQuery.toLowerCase();
                    const courseNameMatch = course.courseName
                        .toLowerCase()
                        .includes(searchTerm);
                    const tagsMatch = course.tags?.some((tag) => tag.toLowerCase().includes(searchTerm));
                    if (!courseNameMatch && !tagsMatch) {
                        matches = false;
                    }
                }
                if (filters.language) {
                    const hasMatchingLanguage = course.language
                        .toLowerCase()
                        .includes(filters.language.toLowerCase());
                    if (!hasMatchingLanguage) {
                        matches = false;
                    }
                }
                if (filters.tags && filters.tags.length > 0) {
                    const courseTags = course.tags || [];
                    const hasMatchingTag = filters.tags.some((tag) => courseTags.includes(tag));
                    if (!hasMatchingTag) {
                        matches = false;
                    }
                }
                if (filters.organizationName &&
                    course.createdBy?.organizationName?.toLowerCase() !==
                        filters.organizationName.toLowerCase()) {
                    matches = false;
                }
                if (filters.courseName &&
                    !course.courseName
                        .toLowerCase()
                        .includes(filters.courseName.toLowerCase())) {
                    matches = false;
                }
                return matches;
            });
        }
        return await Promise.all(courses.map(async (course) => {
            let isSaved = false;
            if (userId) {
                const saved = await this.savedCourseRepo.findOne({
                    where: {
                        user: { id: userId },
                        course: { id: course.id },
                    },
                });
                isSaved = !!saved;
            }
            return {
                id: course.id,
                courseName: course.courseName,
                language: course.language,
                whatTheyLearn: course.whatTheyLearn,
                tags: course.tags,
                topicstag: course.topicsTag,
                status: course.status,
                estimatedDuration: course.estimatedDuration,
                thumbnailUrl: course.thumbnailUrl,
                isSaved,
                createdBy: {
                    id: course.createdBy?.id,
                    organizationName: course.createdBy?.organizationName,
                    profilePicture: course.createdBy?.organizationDetails?.profilePicture,
                },
                topics: course.topics?.map((topic) => ({
                    id: topic.id,
                    title: topic.topicTitle,
                    numberOfChapters: topic.numberOfChapters,
                    chapters: topic.chapters?.map((chapter) => ({
                        id: chapter.id,
                        title: chapter.chapterTitle,
                        description: chapter.description,
                        mediaUrl: chapter.mediaUrl,
                    })) || [],
                })) || [],
            };
        }));
    }
    async getUserCourseProgress(userId) {
        const enrollments = await this.enrollmentRepo.find({
            where: {
                user: { id: userId },
                isEnroll: true,
            },
            relations: [
                'course',
                'course.topics',
                'course.topics.chapters',
                'course.createdBy',
                'course.createdBy.organizationDetails',
            ],
        });
        const progressRecords = await this.courseProgressRepo.find({
            where: { user: { id: userId } },
            relations: ['course'],
        });
        return enrollments
            .map(({ course }) => {
            const allChapters = course.topics.flatMap((topic) => topic.chapters);
            const totalChapters = allChapters.length;
            const progress = progressRecords.find((p) => p.course && p.course.id === course.id);
            const completedChapterIds = progress?.completedChapterIds || [];
            const lastWatchedChapterId = progress?.lastWatchedChapterId || null;
            const completedChapters = allChapters.filter((ch) => completedChapterIds.includes(ch.id)).length;
            const percentage = totalChapters === 0
                ? 0
                : Math.round((completedChapters / totalChapters) * 100);
            const lastWatchedChapter = allChapters.find((ch) => ch.id === lastWatchedChapterId);
            return {
                courseId: course.id,
                courseName: course.courseName,
                language: course.language,
                tags: course.tags,
                topicstag: course.topicsTag,
                isEnrolled: true,
                totalChapters,
                completedChapters,
                progressPercentage: percentage,
                lastWatchedChapter: lastWatchedChapter
                    ? {
                        id: lastWatchedChapter.id,
                        title: lastWatchedChapter.chapterTitle,
                    }
                    : null,
                thumbnailUrl: course.thumbnailUrl,
                whatYouLearn: course.whatTheyLearn,
                createdBy: {
                    id: course.createdBy?.id,
                    profilePicture: course.createdBy?.organizationDetails?.profilePicture,
                    organizationName: course.createdBy?.organizationName,
                },
            };
        })
            .filter((course) => course.progressPercentage < 100);
    }
    async getAllCompletedCoursesByUser(userId) {
        const user = await this.userRepo.findOne({
            where: { id: userId },
            select: ['FullName', 'organizationName'],
        });
        if (!user)
            throw new common_1.NotFoundException('User not found');
        const enrollments = await this.enrollmentRepo.find({
            where: {
                user: { id: userId },
                isEnroll: true,
            },
            relations: ['course'],
        });
        const enrolledCourseIds = enrollments.map((e) => e.course.id);
        const progressRecords = await this.courseProgressRepo.find({
            where: { user: { id: userId } },
            relations: [
                'course',
                'course.topics',
                'course.topics.chapters',
                'course.createdBy',
                'course.createdBy.organizationDetails',
            ],
        });
        const completedCourses = [];
        for (const progress of progressRecords) {
            const course = progress.course;
            if (!course || !course.topics)
                continue;
            const chapters = course.topics?.flatMap((topic) => topic.chapters) || [];
            const allChaptersCompleted = chapters.length > 0 &&
                chapters.every((chapter) => progress.completedChapterIds.includes(chapter.id));
            const allQuizzesCompleted = chapters.every((chapter) => {
                if (chapter.quizId) {
                    return progress.completedQuizIds?.includes(chapter.quizId);
                }
                return true;
            });
            const totalChapters = chapters.length;
            const completedChapters = chapters.filter((chapter) => progress.completedChapterIds.includes(chapter.id)).length;
            const progressPercentage = totalChapters === 0
                ? 0
                : Math.round((completedChapters / totalChapters) * 100);
            if (allChaptersCompleted && allQuizzesCompleted) {
                completedCourses.push({
                    courseId: course.id,
                    language: course.language,
                    tags: course.tags,
                    topicstag: course.topicsTag,
                    courseTitle: course.courseName,
                    thumbnailUrl: course.thumbnailUrl,
                    whatTheyLearn: course.whatTheyLearn,
                    userFullName: user.FullName,
                    createdBy: {
                        id: course.createdBy?.id,
                        profilePicture: course.createdBy?.organizationDetails?.profilePicture,
                        organizationName: course.createdBy?.organizationName,
                    },
                    completionDate: progress.lastUpdated,
                    isEnrolled: enrolledCourseIds.includes(course.id),
                    totalChapters,
                    completedChapters,
                    progressPercentage,
                });
            }
        }
        return completedCourses;
    }
    async getSavedCourses(userId) {
        const savedCourses = await this.savedCourseRepo.find({
            where: { user: { id: userId } },
            relations: [
                'user',
                'course',
                'course.topics',
                'course.topics.chapters',
                'course.createdBy',
                'course.createdBy.organizationDetails',
            ],
        });
        const enrollments = await this.enrollmentRepo.find({
            where: {
                user: { id: userId },
                isEnroll: true,
            },
            relations: ['course'],
        });
        const enrolledCourseIds = enrollments.map((e) => e.course.id);
        return savedCourses.map(({ user, course }) => ({
            user: {
                userId: user.id,
                fullName: user.FullName,
                email: user.email,
            },
            course: {
                id: course.id,
                courseName: course.courseName,
                language: course.language,
                whatTheyLearn: course.whatTheyLearn,
                tags: course.tags,
                topicstag: course.topicsTag,
                thumbnailUrl: course.thumbnailUrl,
                status: course.status,
                estimatedDuration: course.estimatedDuration,
                previewVideoUrl: course.previewVideoUrl,
                isEnrolled: enrolledCourseIds.includes(course.id),
                createdBy: {
                    id: course.createdBy?.id,
                    organizationName: course.createdBy?.organizationName,
                    profilePicture: course.createdBy?.organizationDetails?.profilePicture,
                },
                topics: course.topics.map((topic) => ({
                    topicId: topic.id,
                    topicTitle: topic.topicTitle,
                    chapters: topic.chapters.map((chapter) => ({
                        chapterId: chapter.id,
                        chapterTitle: chapter.chapterTitle,
                        description: chapter.description,
                        mediaUrl: chapter.mediaUrl,
                    })),
                })),
            },
        }));
    }
    async findCoursesByType(params) {
        const { userId, type, filters } = params;
        let courses = [];
        if (type === 'in-progress') {
            const progressCourses = await this.getUserCourseProgress(userId);
            courses = progressCourses.map((course) => ({
                ...course,
                type: 'in-progress',
            }));
        }
        if (type === 'completed') {
            const completedCourses = await this.getAllCompletedCoursesByUser(userId);
            courses = completedCourses.map((course) => ({
                ...course,
                type: 'completed',
            }));
        }
        if (type === 'saved') {
            const savedCourses = await this.getSavedCourses(userId);
            courses = savedCourses.map((entry) => ({
                ...entry.course,
                type: 'saved',
            }));
        }
        if (type === 'all') {
            const allCourses = await this.courseRepo.find({
                where: { status: 'active' },
                relations: ['topics', 'topics.chapters', 'createdBy.organizationDetails'],
            });
            courses = await Promise.all(allCourses.map(async (course) => {
                const saved = await this.savedCourseRepo.findOne({
                    where: {
                        user: { id: userId },
                        course: { id: course.id },
                    },
                });
                return {
                    id: course.id,
                    courseName: course.courseName,
                    language: course.language,
                    whatTheyLearn: course.whatTheyLearn,
                    tags: course.tags,
                    topicstag: course.topicsTag,
                    status: course.status,
                    estimatedDuration: course.estimatedDuration,
                    thumbnailUrl: course.thumbnailUrl,
                    isSaved: !!saved,
                    type: 'all',
                    createdBy: {
                        id: course.createdBy?.id,
                        organizationName: course.createdBy?.organizationName,
                        profilePicture: course.createdBy?.organizationDetails?.profilePicture,
                    },
                    topics: course.topics?.map((topic) => ({
                        id: topic.id,
                        title: topic.topicTitle,
                        numberOfChapters: topic.numberOfChapters,
                        chapters: topic.chapters?.map((chapter) => ({
                            id: chapter.id,
                            title: chapter.chapterTitle,
                            description: chapter.description,
                            mediaUrl: chapter.mediaUrl,
                        })) || [],
                    })) || [],
                };
            }));
        }
        if (filters) {
            courses = courses.filter((course) => {
                const search = filters.searchQuery?.toLowerCase();
                const matchName = !filters.courseName || course.courseName?.toLowerCase().includes(filters.courseName.toLowerCase());
                const matchSearch = !search || course.courseName?.toLowerCase().includes(search) || course.tags?.some((tag) => tag.toLowerCase().includes(search));
                const matchLang = !filters.language || course.language?.toLowerCase().includes(filters.language.toLowerCase());
                const matchTags = !filters.tags || filters.tags.some((tag) => course.tags?.includes(tag));
                const matchOrg = !filters.organizationName || course.createdBy?.organizationName?.toLowerCase() === filters.organizationName.toLowerCase();
                return matchName && matchSearch && matchLang && matchTags && matchOrg;
            });
        }
        return courses;
    }
    async findByStatus(status, userId) {
        const courses = await this.courseRepo.find({
            where: {
                status,
                createdBy: { id: userId },
            },
            relations: [
                'topics',
                'topics.chapters',
                'createdBy',
                'createdBy.organizationDetails',
            ],
        });
        return await Promise.all(courses.map(async (course) => {
            const enrollmentCount = await this.enrollmentRepo.count({
                where: { course: { id: Number(course.id) } },
            });
            return {
                id: course.id,
                courseName: course.courseName,
                thumbnailUrl: course.thumbnailUrl,
                status: course.status,
                whatTheyLearn: course.whatTheyLearn,
                language: course.language,
                enrollmentCount,
                createdBy: {
                    id: course.createdBy?.id,
                    organizationName: course.createdBy?.organizationName,
                    profilePicture: course.createdBy?.organizationDetails?.profilePicture,
                },
            };
        }));
    }
    async closeCourse(courseId, userId) {
        const course = await this.courseRepo.findOne({
            where: { id: courseId, createdBy: { id: userId } },
        });
        if (!course)
            throw new common_1.NotFoundException('Course not found');
        course.status = 'closed';
        await this.courseRepo.save(course);
        return 'Course closed successfully';
    }
    async deleteCourseById(courseId) {
        const course = await this.courseRepo.findOne({
            where: { id: courseId },
            relations: [
                'topics',
                'topics.chapters',
                'topics.chapters.quizzes',
                'topics.chapters.quizzes.attempts',
            ],
        });
        if (!course) {
            throw new common_1.NotFoundException('Course not found');
        }
        const chapterIds = [];
        const quizIds = [];
        for (const topic of course.topics) {
            for (const chapter of topic.chapters) {
                chapterIds.push(chapter.id);
                for (const quiz of chapter.quizzes) {
                    quizIds.push(quiz.id);
                    await this.quizAttemptRepo.delete({ quiz: { id: quiz.id } });
                }
            }
        }
        if (chapterIds.length > 0) {
            await this.chapterProgressRepo
                .createQueryBuilder()
                .delete()
                .where('chapterId IN (:...chapterIds)', { chapterIds })
                .execute();
        }
        await this.courseProgressRepo.delete({ course: { id: courseId } });
        await this.enrollmentRepo.delete({ course: { id: courseId } });
        await this.savedCourseRepo.delete({ course: { id: courseId } });
        for (const quizId of quizIds) {
            await this.quizRepo.delete(quizId);
        }
        for (const topic of course.topics) {
            for (const chapter of topic.chapters) {
                await this.chapterRepo.delete(chapter.id);
            }
            await this.topicRepo.delete(topic.id);
        }
        await this.courseRepo.delete(course.id);
    }
    async attemptQuiz(body, user) {
        const { quizId, answers } = body;
        const quiz = await this.quizRepo.findOne({
            where: { id: quizId },
            relations: ['questions'],
        });
        if (!quiz)
            throw new Error('Quiz not found');
        if (!quiz.questions || quiz.questions.length === 0)
            throw new Error('No questions in quiz');
        const latestAttempt = await this.quizAttemptRepo.findOne({
            where: {
                user: { id: user.id },
                quiz: { id: quizId },
            },
            order: {
                created_at: 'DESC',
            },
        });
        const nextAttemptNumber = latestAttempt
            ? latestAttempt.attemptNumber + 1
            : 1;
        let score = 0;
        const questionsResult = quiz.questions.map((q, idx) => {
            const userSelectedIndex = answers[idx];
            const correctOptionIndex = q.options.findIndex((opt) => opt === q.correctOption);
            const isCorrect = userSelectedIndex === correctOptionIndex;
            if (isCorrect)
                score++;
            return {
                questionText: q.questionText,
                options: q.options,
                correctOptionIndex,
                userSelectedIndex,
                isCorrect,
            };
        });
        await this.quizAttemptRepo.save({
            user: { id: user.id },
            quiz: { id: quizId },
            score,
            attemptNumber: nextAttemptNumber,
            answers,
        });
        return {
            quizId,
            user: {
                id: user.id,
                name: user.FullName,
                email: user.email,
                designation: user.designation,
            },
            questions: questionsResult,
            score,
            total: quiz.questions.length,
            attemptNumber: nextAttemptNumber,
        };
    }
    async getLatestQuizAttemptDetailed(userId, quizId) {
        const attempt = await this.quizAttemptRepo.findOne({
            where: {
                user: { id: userId },
                quiz: { id: quizId },
            },
            order: {
                attemptedAt: 'DESC',
            },
            relations: ['user'],
        });
        if (!attempt)
            throw new Error('No attempt found');
        const quiz = await this.quizRepo.findOne({
            where: { id: quizId },
            relations: ['questions'],
        });
        if (!quiz)
            throw new Error('Quiz not found');
        const user = attempt.user;
        const answers = attempt.answers || [];
        let score = 0;
        const questionsResult = quiz.questions.map((q, idx) => {
            const userSelectedIndex = answers[idx];
            const correctOptionIndex = q.options.findIndex((opt) => opt === q.correctOption);
            const isCorrect = userSelectedIndex === correctOptionIndex;
            if (isCorrect)
                score++;
            return {
                questionText: q.questionText,
                options: q.options,
                correctOptionIndex,
                userSelectedIndex,
                isCorrect,
            };
        });
        return {
            quizId,
            user: {
                id: user.id,
                name: user.FullName,
                email: user.email,
            },
            questions: questionsResult,
            score,
            total: quiz.questions.length,
            attemptNumber: attempt.attemptNumber,
            attemptedAt: attempt.attemptedAt,
        };
    }
    async getQuizById(quizId) {
        const quiz = await this.quizRepo.findOne({
            where: { id: quizId },
            relations: ['chapter', 'questions', 'createdBy'],
        });
        if (!quiz)
            throw new common_1.NotFoundException('Quiz not found');
        return {
            id: quiz.id,
            chapter: {
                id: quiz.chapter.id,
                title: quiz.chapter.chapterTitle,
            },
            createdBy: {
                id: quiz.createdBy.id,
                email: quiz.createdBy.email,
                organizationName: quiz.createdBy.organizationName,
            },
            questions: quiz.questions.map((q) => ({
                id: q.id,
                questionText: q.questionText,
                options: q.options,
                correctOptionIndex: q.correctOption,
            })),
        };
    }
    async toggleSaveCourse(userId, courseId) {
        const user = await this.userRepo.findOneByOrFail({ id: userId });
        const course = await this.courseRepo.findOneByOrFail({ id: courseId });
        const existing = await this.savedCourseRepo.findOne({
            where: {
                user: { id: userId },
                course: { id: courseId },
            },
        });
        if (existing) {
            await this.savedCourseRepo.remove(existing);
            return { success: true, message: 'Course unsaved' };
        }
        else {
            const saved = this.savedCourseRepo.create({ user, course });
            await this.savedCourseRepo.save(saved);
            return {
                success: true,
                message: 'Course saved',
                savedCourseId: saved.id,
            };
        }
    }
    async enrollUser(userId, courseId) {
        const user = await this.userRepo.findOne({ where: { id: userId } });
        const course = await this.courseRepo.findOne({ where: { id: courseId } });
        if (!user || !course)
            throw new common_1.NotFoundException('User or Course not found');
        let enrollment = await this.enrollmentRepo.findOne({
            where: { user: { id: userId }, course: { id: courseId } },
        });
        if (enrollment?.isEnroll) {
            return { message: 'User already enrolled' };
        }
        if (enrollment) {
            enrollment.isEnroll = true;
            enrollment.enrolledAt = new Date();
        }
        else {
            enrollment = this.enrollmentRepo.create({
                user,
                course,
                isEnroll: true,
                enrolledAt: new Date(),
            });
        }
        await this.enrollmentRepo.save(enrollment);
        return { message: 'Enrolled successfully', enrollment };
    }
    async getEnrolledUsers(courseId) {
        const enrollments = await this.enrollmentRepo.find({
            where: { course: { id: courseId } },
            relations: ['user', 'course'],
        });
        return enrollments.map((enrollment) => ({
            courseId: enrollment.course.id,
            userId: enrollment.user.id,
            fullName: enrollment.user.FullName,
            email: enrollment.user.email,
            enrolledAt: enrollment.enrolledAt,
        }));
    }
    async getEnrollmentCount(courseId) {
        const count = await this.enrollmentRepo.count({
            where: { course: { id: courseId } },
        });
        return { courseId, enrollmentCount: count };
    }
    async assignCertificateToUser(userId, courseId, pdfUrl) {
        const progress = await this.courseProgressRepo.findOne({
            where: { user: { id: userId }, course: { id: courseId } },
            relations: ['course', 'course.topics', 'course.topics.chapters'],
        });
        if (!progress) {
            throw new common_1.NotFoundException('Progress record not found');
        }
        const chapters = progress.course.topics.flatMap((t) => t.chapters);
        const allChaptersCompleted = chapters.length > 0 &&
            progress.completedChapterIds.length === chapters.length;
        const allQuizzesCompleted = chapters.every((chapter) => {
            if (chapter.quizId) {
                return progress.completedQuizIds?.includes(chapter.quizId);
            }
            return true;
        });
        if (!allChaptersCompleted || !allQuizzesCompleted) {
            throw new common_1.BadRequestException('User has not completed the course');
        }
        const existing = await this.certificateRepo.findOne({
            where: { user: { id: userId }, course: { id: courseId } },
        });
        if (existing) {
            throw new common_1.BadRequestException('Certificate already exists');
        }
        const certificate = this.certificateRepo.create({
            user: { id: userId },
            course: { id: courseId },
            pdfUrl,
        });
        await this.certificateRepo.save(certificate);
        return {
            message: 'Certificate assign successfully',
            certificate,
        };
    }
};
exports.CourseService = CourseService;
exports.CourseService = CourseService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(course_entity_1.Course)),
    __param(1, (0, typeorm_1.InjectRepository)(topic_entity_1.Topic)),
    __param(2, (0, typeorm_1.InjectRepository)(chapter_entity_1.Chapter)),
    __param(3, (0, typeorm_1.InjectRepository)(quiz_entity_1.Quiz)),
    __param(4, (0, typeorm_1.InjectRepository)(question_entity_1.Question)),
    __param(5, (0, typeorm_1.InjectRepository)(quiz_attempt_entity_1.QuizAttempt)),
    __param(6, (0, typeorm_1.InjectRepository)(course_progress_entity_1.CourseProgress)),
    __param(7, (0, typeorm_1.InjectRepository)(chapter_progress_entity_1.ChapterProgress)),
    __param(8, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __param(9, (0, typeorm_1.InjectRepository)(savedCourse_entity_1.SavedCourse)),
    __param(10, (0, typeorm_1.InjectRepository)(enrollment_entity_1.Enrollment)),
    __param(11, (0, typeorm_1.InjectRepository)(certificate_entity_1.Certificate)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository])
], CourseService);
//# sourceMappingURL=course.service.js.map