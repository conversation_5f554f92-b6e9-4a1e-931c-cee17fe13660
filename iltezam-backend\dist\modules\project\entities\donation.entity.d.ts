import { BaseEntityClass } from '../../auth/entities/base.entity';
import { User } from '../../auth/entities/user.entity';
import { Project } from './project.entity';
export declare enum PaymentMethod {
    STRIPE = "stripe",
    CRYPTO = "crypto"
}
export declare enum PaymentFrequency {
    ONE_TIME = "one_time",
    MONTHLY = "monthly"
}
export declare enum PaymentStatus {
    SUCCESS = "success",
    FAILED = "failed",
    PENDING = "pending"
}
export declare class Donation extends BaseEntityClass {
    donor: User;
    project: Project;
    paymentMethod: PaymentMethod;
    frequency: PaymentFrequency;
    amount: number;
    status: PaymentStatus;
    transactionId: string;
    paymentDate: Date;
    notes: string;
}
