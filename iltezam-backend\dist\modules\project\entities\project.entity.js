"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Project = exports.ProjectCategory = exports.ProjectStatus = void 0;
const typeorm_1 = require("typeorm");
const base_entity_1 = require("../../auth/entities/base.entity");
const user_entity_1 = require("../../auth/entities/user.entity");
const position_entity_1 = require("./position.entity");
const organiser_entities_1 = require("../../../organiser/entities/organiser.entities");
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["LIVE"] = "live";
    ProjectStatus["CLOSED"] = "closed";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
var ProjectCategory;
(function (ProjectCategory) {
    ProjectCategory["DONATION"] = "donation";
    ProjectCategory["VOLUNTEER"] = "volunteer";
    ProjectCategory["OTHER"] = "other";
})(ProjectCategory || (exports.ProjectCategory = ProjectCategory = {}));
let Project = class Project extends base_entity_1.BaseEntityClass {
    projectName;
    projectCategory;
    status;
    images;
    fundRaisingGoal;
    fundsCollected;
    location;
    startDate;
    finishDate;
    projectDescription;
    thumbnailImage;
    totalClicks;
    createdBy;
    totalFilledPositions;
    positions;
    organization;
    donations;
};
exports.Project = Project;
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], Project.prototype, "projectName", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ProjectCategory,
    }),
    __metadata("design:type", String)
], Project.prototype, "projectCategory", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ProjectStatus,
        default: ProjectStatus.LIVE,
    }),
    __metadata("design:type", String)
], Project.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)('simple-array', { nullable: true }),
    __metadata("design:type", Array)
], Project.prototype, "images", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "fundRaisingGoal", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "fundsCollected", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Project.prototype, "location", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], Project.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'timestamp' }),
    __metadata("design:type", Date)
], Project.prototype, "finishDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], Project.prototype, "projectDescription", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Project.prototype, "thumbnailImage", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "totalClicks", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { eager: true }),
    (0, typeorm_1.JoinColumn)({ name: 'createdBy' }),
    __metadata("design:type", user_entity_1.User)
], Project.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)('simple-array', { nullable: true }),
    __metadata("design:type", Array)
], Project.prototype, "totalFilledPositions", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => position_entity_1.Position, (position) => position.project, {
        cascade: true,
        eager: true,
    }),
    __metadata("design:type", Array)
], Project.prototype, "positions", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => organiser_entities_1.Organization, (org) => org.projects, {
        eager: true,
        nullable: true,
    }),
    (0, typeorm_1.JoinColumn)({ name: 'organization_id' }),
    __metadata("design:type", organiser_entities_1.Organization)
], Project.prototype, "organization", void 0);
exports.Project = Project = __decorate([
    (0, typeorm_1.Entity)({ name: 'projects' })
], Project);
//# sourceMappingURL=project.entity.js.map