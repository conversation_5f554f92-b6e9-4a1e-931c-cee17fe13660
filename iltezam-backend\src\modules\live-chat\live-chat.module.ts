import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LiveChatService } from './live-chat.service';
import { LiveChatController } from './live-chat.controller';
import { ChatRoom } from './entities/chat-room.entity';
import { Message } from './entities/message.entity';
import { User } from '../auth/entities/user.entity';

@Module({
  imports: [TypeOrmModule.forFeature([ChatRoom, Message, User])],
  providers: [LiveChatService],
  controllers: [LiveChatController],
  exports: [LiveChatService],
})
export class LiveChatModule {}
