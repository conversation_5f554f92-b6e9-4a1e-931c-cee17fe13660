import { NotificationService } from './notification.service';
interface AuthenticatedRequest extends Request {
    user: {
        id: number;
    };
}
export declare class NotificationController {
    private readonly notificationService;
    constructor(notificationService: NotificationService);
    getUserNotifications(req: AuthenticatedRequest): Promise<{
        notifications: import("./entities/notification.entity").Notification[];
    }>;
    getNotificationById(id: number, req: AuthenticatedRequest): Promise<{
        notification: import("./entities/notification.entity").Notification | null;
    }>;
    markAsRead(id: number, req: AuthenticatedRequest): Promise<{
        notification: import("./entities/notification.entity").Notification;
        message: string;
    }>;
    notifyUser(notificationData: {
        title: string;
        content: string;
    }, req: AuthenticatedRequest): Promise<{
        notification: import("./entities/notification.entity").Notification;
        message: string;
    }>;
}
export {};
