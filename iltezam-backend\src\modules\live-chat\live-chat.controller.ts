import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common';
import { LiveChatService } from './live-chat.service';
import { AuthGuard } from '@nestjs/passport';

@Controller('live-chat')
export class LiveChatController {
  constructor(private readonly liveChatService: LiveChatService) {}

  @Post('create-room')
  @UseGuards(AuthGuard('jwt'))
  async createRoom(@Req() req, @Body() body) {
    const sender = req.user.id;
    const { receiver } = body;
    return this.liveChatService.createRoom(sender, receiver);
  }

  @Post('send-message')
  @UseGuards(AuthGuard('jwt'))
  async sendMessage(@Req() req, @Body() body) {
    const sender = req.user.id;
    const { roomId, receiver, message } = body;
    console.log('sender', sender);
    console.log('roomId', roomId);
    console.log('receiver', receiver);
    console.log('message', message);

    return this.liveChatService.sendMessage(sender, roomId, receiver, message);
  }

  @Get('messages/:roomId')
  @UseGuards(AuthGuard('jwt'))
  async getMessages(@Param('roomId') roomId: string, @Req() req) {
    const userId = req.user.id;
    return this.liveChatService.getMessages(roomId, userId);
  }

  @Get('inbox')
  @UseGuards(AuthGuard('jwt'))
  async getInbox(@Req() req) {
    const userId = req.user.id;
    return this.liveChatService.getInbox(userId);
  }

  @Post('room/:id')
  @UseGuards(AuthGuard('jwt'))
  async getRoom(@Req() req, @Param('id') partnerId: string) {
    const userId = req.user.id;
    return this.liveChatService.getRoom(userId, Number(partnerId));
  }

  @Post('create-group')
  @UseGuards(AuthGuard('jwt'))
  async createGroupRoom(@Req() req, @Body() body) {
    const creator = req.user.id;
    const { users, groupName, groupPicture } = body;
    return this.liveChatService.createGroupRoom(
      creator,
      users,
      groupName,
      groupPicture,
    );
  }

  @Post('send-group-message')
  @UseGuards(AuthGuard('jwt'))
  async sendGroupMessage(@Req() req, @Body() body) {
    const sender = req.user.id;
    const { roomId, message } = body;
    return this.liveChatService.sendGroupMessage(sender, roomId, message);
  }
  // In your live-chat.controller.ts

@Get('groups')
@UseGuards(AuthGuard('jwt'))
async getAllGroups(@Req() req) {
  const userId = req.user.id;
  return this.liveChatService.getAllGroups(userId);
}
@Post('groups/:roomId/join')
@UseGuards(AuthGuard('jwt'))
async joinGroup(@Param('roomId') roomId: string, @Req() req) {
  const userId = req.user.id;
  return this.liveChatService.joinGroup(roomId, userId);
}

}
