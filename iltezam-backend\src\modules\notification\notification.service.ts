import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Notification } from './entities/notification.entity';

@Injectable()
export class NotificationService {
  constructor(
    @InjectRepository(Notification)
    private notificationRepository: Repository<Notification>,
  ) {}

  /**
   * Get all notifications for a user
   * @param userId The user ID
   * @returns Array of notifications
   */
  async getUserNotifications(userId: number) {
    return this.notificationRepository.find({
      where: { userId },
      order: { created_at: 'DESC' },
    });
  }

  /**
   * Get a specific notification by ID and user ID
   * @param id The notification ID
   * @param userId The user ID
   * @returns The notification if found
   */
  async getNotificationById(id: number, userId: number) {
    return this.notificationRepository.findOne({
      where: { id, userId },
    });
  }

  /**
   * Mark a notification as read
   * @param id The notification ID
   * @param userId The user ID
   * @returns The updated notification
   */
  async markAsRead(id: number, userId: number) {
    const notification = await this.notificationRepository.findOne({
      where: { id, userId },
    });

    if (!notification) {
      throw new Error('Notification not found');
    }

    notification.isRead = true;
    return this.notificationRepository.save(notification);
  }

  /**
   * Create a notification for a user
   * @param data The notification data
   * @returns The created notification
   */
  async notifyUser(data: {
    userId: number;
    title: string;
    content: string;
  }) {
    const notification = this.notificationRepository.create({
      userId: data.userId,
      title: data.title,
      content: data.content,
      isRead: false,
    });

    return this.notificationRepository.save(notification);
  }
}
