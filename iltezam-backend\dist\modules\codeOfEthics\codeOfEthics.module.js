"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeOfEthicsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const codeOfEthics_controller_1 = require("./codeOfEthics.controller");
const codeOfEthics_services_1 = require("./codeOfEthics.services");
const codeOfEthics_entity_1 = require("./Entity/codeOfEthics.entity");
let CodeOfEthicsModule = class CodeOfEthicsModule {
};
exports.CodeOfEthicsModule = CodeOfEthicsModule;
exports.CodeOfEthicsModule = CodeOfEthicsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([codeOfEthics_entity_1.CodeOfEthics])],
        controllers: [codeOfEthics_controller_1.CodeOfEthicsController],
        providers: [codeOfEthics_services_1.CodeOfEthicsService],
        exports: [codeOfEthics_services_1.CodeOfEthicsService],
    })
], CodeOfEthicsModule);
//# sourceMappingURL=codeOfEthics.module.js.map