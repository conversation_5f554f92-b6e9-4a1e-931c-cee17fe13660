"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuizAttemptDto = exports.UpdateCourseDto = exports.CreateQuizDto = exports.CreateChapterDto = exports.CreateTopicDto = exports.CreateCourseDto = void 0;
class CreateCourseDto {
    courseName;
    language;
    whatTheyLearn;
    tags;
    topicsTag;
    thumbnailUrl;
    status;
    topics;
    estimatedDuration;
    previewVideoUrl;
}
exports.CreateCourseDto = CreateCourseDto;
class CreateTopicDto {
    topicTitle;
    numberOfChapters;
    chapters;
}
exports.CreateTopicDto = CreateTopicDto;
class CreateChapterDto {
    chapterTitle;
    description;
    mediaUrl;
    duration;
    prerequisiteChapterIds;
    quiz;
}
exports.CreateChapterDto = CreateChapterDto;
class CreateQuizDto {
    chapterId;
    questions;
}
exports.CreateQuizDto = CreateQuizDto;
class UpdateCourseDto {
    courseName;
    language;
    whatTheyLearn;
    tags;
    topicsTag;
    thumbnailUrl;
    status;
    topics;
}
exports.UpdateCourseDto = UpdateCourseDto;
class QuizAttemptDto {
    userId;
    quizId;
    answers;
}
exports.QuizAttemptDto = QuizAttemptDto;
//# sourceMappingURL=create.course.dto.js.map