import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import * as multer from 'multer';
import { extname } from 'path';

@Injectable()
export class UploadMiddleware implements NestMiddleware {
    private upload = multer({
        storage: multer.diskStorage({
            destination: './uploads/universities',
            filename: (req, file, callback) => {
                const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
                const ext = extname(file.originalname);
                callback(null, `${uniqueSuffix}${ext}`);
            }
        })
    }).single('logo_url'); // Ensure 'logo_url' matches the frontend form field name

    use(req: Request, res: Response, next: NextFunction) {
        this.upload(req, res, (err) => {
            if (err) {
                return res.status(400).json({ message: err.message });
            }
            next();
        });
    }
}
