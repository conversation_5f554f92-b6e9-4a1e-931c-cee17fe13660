import {
  ConflictException,
  HttpException,
  HttpStatus,
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Course } from './entities/course.entity';
import { CreateCourseDto, UpdateCourseDto } from './dtos/create.course.dto';
import { Quiz } from './entities/quiz.entity';
import { QuizAttempt } from './entities/quiz-attempt.entity';
import { CourseProgress } from './entities/course-progress.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { SavedCourse } from './entities/savedCourse.entity';
import { Enrollment } from './entities/enrollment.entity';
import { Chapter } from './entities/chapter.entity';
import { Topic } from './entities/topic.entity';
import { CreateQuizDto } from './dtos/create.course.dto';
import { Question } from './entities/question.entity';
import { QuizAttemptDto } from './dtos/create.course.dto';
import { Certificate } from './entities/certificate.entity';
import { ChapterProgress } from './entities/chapter-progress.entity';

@Injectable()
export class CourseService {
  constructor(
    @InjectRepository(Course)
    private courseRepo: Repository<Course>,
    @InjectRepository(Topic) private topicRepo: Repository<Topic>,
    @InjectRepository(Chapter) private chapterRepo: Repository<Chapter>,
    @InjectRepository(Quiz)
    private quizRepo: Repository<Quiz>,
    @InjectRepository(Question)
    private questionRepo: Repository<Question>,
    @InjectRepository(QuizAttempt)
    private quizAttemptRepo: Repository<QuizAttempt>,
    @InjectRepository(CourseProgress)
    private courseProgressRepo: Repository<CourseProgress>,
    @InjectRepository(ChapterProgress)
    private chapterProgressRepo: Repository<ChapterProgress>,
    @InjectRepository(User)
    private userRepo: Repository<User>,
    @InjectRepository(SavedCourse)
    private savedCourseRepo: Repository<SavedCourse>,
    @InjectRepository(Enrollment)
    private enrollmentRepo: Repository<Enrollment>,
    @InjectRepository(Certificate)
    private certificateRepo: Repository<Certificate>,
  ) {}

  // async createCourse(
  //   createCourseDto: CreateCourseDto,
  //   user: User,
  // ): Promise<any> {
  //   try {
  //     const newCourse = this.courseRepo.create({
  //       ...createCourseDto,
  //       createdBy: user,
  //     });

  //     const savedCourse = await this.courseRepo.save(newCourse);

  //     return {
  //       ...savedCourse,
  //       createdBy: {
  //         id: user.id,
  //         organizationName: user.organizationName,
  //         email: user.email,
  //       },
  //     };
  //   } catch (e) {
  //     console.error(e);
  //     throw new HttpException(
  //       e.message || 'Internal server error',
  //       e.status || HttpStatus.INTERNAL_SERVER_ERROR,
  //     );
  //   }
  // }

  async createCourse(
    dto: CreateCourseDto,
    creator: User,
  ): Promise<{ message: string; course: any }> {
    const course = this.courseRepo.create({
      courseName: dto.courseName,
      language: dto.language,
      whatTheyLearn: dto.whatTheyLearn,
      tags: dto.tags,
      topicsTag: dto.topicsTag,
      thumbnailUrl: dto.thumbnailUrl,
      status: dto.status || 'active',
      estimatedDuration: dto.estimatedDuration,
      previewVideoUrl: dto.previewVideoUrl,
      createdBy: creator,
    });

    course.topics = [];

    for (const topicDto of dto.topics) {
      const topic = this.topicRepo.create({
        topicTitle: topicDto.topicTitle,
        numberOfChapters: topicDto.numberOfChapters,
      });

      topic.chapters = [];

      for (const chapterDto of topicDto.chapters) {
        const chapter = this.chapterRepo.create({
          chapterTitle: chapterDto.chapterTitle,
          description: chapterDto.description,
          mediaUrl: chapterDto.mediaUrl,
          duration: chapterDto.duration,
          prerequisiteChapterIds: chapterDto.prerequisiteChapterIds || [],
        });

        if (chapterDto.quiz) {
          const quiz = this.quizRepo.create({ createdBy: creator });
          quiz.questions = chapterDto.quiz.questions.map((q) =>
            this.questionRepo.create({
              questionText: q.questionText,
              options: q.options,
              correctOption: q.correctOption,
            }),
          );
          chapter.quizzes = [quiz];
        }

        topic.chapters.push(chapter);
      }

      course.topics.push(topic);
    }

    await this.courseRepo.save(course);

    const savedCourse = await this.courseRepo.findOne({
      where: { id: course.id },
      relations: {
        topics: {
          chapters: {
            quizzes: {
              questions: true,
            },
          },
        },
      },
    });

    const { topics, ...courseData } = savedCourse!;

    return {
      message: 'Course created successfully',
      course: {
        ...courseData,
        topics: topics.map((topic) => ({
          topicTitle: topic.topicTitle,
          numberOfChapters: topic.numberOfChapters,
          chapters: topic.chapters.map((ch) => ({
            chapterTitle: ch.chapterTitle,
            description: ch.description,
            mediaUrl: ch.mediaUrl,
            duration: ch.duration,
            quiz: ch.quizzes?.[0]
              ? {
                  quizId: ch.quizzes[0].id,
                  questions: ch.quizzes[0].questions.map((q) => ({
                    questionText: q.questionText,
                    options: q.options,
                    correctOption: q.correctOption,
                  })),
                }
              : undefined,
          })),
        })),
      },
    };
  }

  async getSimpleCourseById(courseId: number): Promise<any> {
    const course = await this.courseRepo.findOne({
      where: { id: courseId },
      relations: [
        'topics',
        'topics.chapters',
        'createdBy',
        'createdBy.organizationDetails',
      ],
    });

    if (!course) {
      return null;
    }

    const enrollmentCount = await this.enrollmentRepo.count({
      where: { course: { id: courseId }, isEnroll: true },
    });

    const totalDuration = course.topics
      .flatMap((topic) => topic.chapters)
      .reduce((sum, chapter) => sum + (Number(chapter.duration) || 0), 0);

    const topicsWithChapters = course.topics.map((topic) => ({
      topicId: topic.id,
      topicTitle: topic.topicTitle,
      chapters: topic.chapters.map((chapter) => ({
        id: chapter.id,
        title: chapter.chapterTitle,
        description: chapter.description,
        duration: Number(chapter.duration) || 0,
        mediaUrl: chapter.mediaUrl || null,
      })),
    }));

    return {
      courseId: course.id,
      courseTitle: course.courseName,
      thumbnailUrl: course.thumbnailUrl,
      whatYouWillLearn: course.whatTheyLearn,
      tags: course.tags,
      topicstag: course.topicsTag,
      language: course.language,
      estimatedDuration: totalDuration,
      previewVideoUrl: course.previewVideoUrl,
      enrollmentCount,
      createdBy: {
        id: course.createdBy?.id,
        organizationName: course.createdBy?.organizationName,
        profilePicture: course.createdBy?.organizationDetails?.profilePicture,
      },
      topics: topicsWithChapters,
    };
  }

  //find all courses
  async findAllWithCreatorInfo(params?: {
    userId?: number;
    filters?: {
      language?: string;
      tags?: string[];
      organizationName?: string;
      courseName?: string;
      searchQuery?: string;
    };
  }): Promise<any[]> {
    const { userId, filters } = params || {};

    let courses = await this.courseRepo.find({
      relations: ['topics', 'topics.chapters', 'createdBy.organizationDetails'],
    });

    // Apply filters
    if (filters) {
      courses = courses.filter((course) => {
        let matches = true;

        // Search in course name and tags
        if (filters.searchQuery) {
          const searchTerm = filters.searchQuery.toLowerCase();
          const courseNameMatch = course.courseName
            .toLowerCase()
            .includes(searchTerm);
          const tagsMatch = course.tags?.some((tag) =>
            tag.toLowerCase().includes(searchTerm),
          );
          if (!courseNameMatch && !tagsMatch) {
            matches = false;
          }
        }

        // Filter by language
        if (filters.language) {
          const hasMatchingLanguage = course.language
            .toLowerCase()
            .includes(filters.language.toLowerCase());
          if (!hasMatchingLanguage) {
            matches = false;
          }
        }

        // Filter by tags
        if (filters.tags && filters.tags.length > 0) {
          const courseTags = course.tags || [];
          const hasMatchingTag = filters.tags.some((tag) =>
            courseTags.includes(tag),
          );
          if (!hasMatchingTag) {
            matches = false;
          }
        }

        // Filter by organization name
        if (
          filters.organizationName &&
          course.createdBy?.organizationName?.toLowerCase() !==
            filters.organizationName.toLowerCase()
        ) {
          matches = false;
        }

        // Filter by course name
        if (
          filters.courseName &&
          !course.courseName
            .toLowerCase()
            .includes(filters.courseName.toLowerCase())
        ) {
          matches = false;
        }

        return matches;
      });
    }

    // Map and include isSaved
    return await Promise.all(
      courses.map(async (course) => {
        let isSaved = false;

        if (userId) {
          const saved = await this.savedCourseRepo.findOne({
            where: {
              user: { id: userId },
              course: { id: course.id },
            },
          });
          isSaved = !!saved;
        }

        return {
          id: course.id,
          courseName: course.courseName,
          language: course.language,
          whatTheyLearn: course.whatTheyLearn,
          tags: course.tags,
          topicstag: course.topicsTag,
          status: course.status,
          estimatedDuration: course.estimatedDuration,
          thumbnailUrl: course.thumbnailUrl,
          isSaved,
          createdBy: {
            id: course.createdBy?.id,
            organizationName: course.createdBy?.organizationName,
            profilePicture:
              course.createdBy?.organizationDetails?.profilePicture,
          },
          topics:
            course.topics?.map((topic) => ({
              id: topic.id,
              title: topic.topicTitle,
              numberOfChapters: topic.numberOfChapters,
              chapters:
                topic.chapters?.map((chapter) => ({
                  id: chapter.id,
                  title: chapter.chapterTitle,
                  description: chapter.description,
                  mediaUrl: chapter.mediaUrl,
                })) || [],
            })) || [],
        };
      }),
    );
  }

  async getCourseById(
    courseId: string,
    userId: number,
    watchedPercentages: Record<number, number> = {},
  ): Promise<any> {
    const course = await this.courseRepo.findOne({
      where: { id: Number(courseId) },
      relations: [
        'topics',
        'topics.chapters',
        'topics.chapters.quizzes',
        'createdBy',
        'createdBy.organizationDetails',
      ],
    });

    if (!course) return null;

    const enrollment = await this.enrollmentRepo.findOne({
      where: {
        user: { id: userId },
        course: { id: Number(courseId) },
        isEnroll: true,
      },
      relations: ['user', 'course'],
    });

    const isEnrolled = !!enrollment;

    const enrollmentCount = await this.enrollmentRepo.count({
      where: { course: { id: Number(courseId) }, isEnroll: true },
    });

    const saved = await this.savedCourseRepo.findOne({
      where: {
        user: { id: userId },
        course: { id: Number(courseId) },
      },
    });

    const isSaved = !!saved;

    let progress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: Number(courseId) } },
    });

    if (!progress) {
      progress = this.courseProgressRepo.create({
        user: { id: userId } as any,
        course: { id: Number(courseId) } as any,
        completedChapterIds: [],
        videoProgress: {},
        completedQuizIds: [],
        lastWatchedChapterId: null,
      });
    }

    let updated = false;
    for (const [chapterIdStr, percentWatched] of Object.entries(
      watchedPercentages,
    )) {
      const chapterId = Number(chapterIdStr);
      if (
        percentWatched >= 90 &&
        !progress.completedChapterIds.includes(chapterId)
      ) {
        progress.completedChapterIds.push(chapterId);
        progress.lastWatchedChapterId = chapterId;
        updated = true;
      }

      progress.videoProgress[chapterIdStr] = percentWatched;
    }

    if (updated) {
      await this.courseProgressRepo.save(progress);
    }

    const completedIds = progress.completedChapterIds;

    const totalDuration = course.topics
      .flatMap((topic) => topic.chapters)
      .reduce((sum, chapter) => sum + (Number(chapter.duration) || 0), 0);

    const topicsWithChapters = course.topics.map((topic) => {
      const sortedChapters = topic.chapters.sort((a, b) => a.id - b.id);

      return {
        topicId: topic.id,
        topicTitle: topic.topicTitle,
        chapters: sortedChapters.map((chapter, index) => {
          const isCompleted = completedIds.includes(chapter.id);

          let isUnlocked = false;
          if (index === 0) {
            isUnlocked = true;
          } else {
            const prevChapterId = sortedChapters[index - 1].id;
            isUnlocked = completedIds.includes(prevChapterId);
          }

          return {
            id: chapter.id,
            title: chapter.chapterTitle,
            description: chapter.description,
            duration: Number(chapter.duration) || 0,
            mediaUrl: isUnlocked ? chapter.mediaUrl : null,
            isLocked: !isUnlocked,
            isCompleted,
            quizIds: chapter.quizzes?.map((quiz) => quiz.id) || [],
          };
        }),
      };
    });

    return {
      isEnrolled,
      isSaved,
      enrollmentCount,
      courseId: course.id,
      courseTitle: course.courseName,
      thumbnailUrl: course.thumbnailUrl,
      whatYouWillLearn: course.whatTheyLearn,
      tags: course.tags,
      topicstag: course.topicsTag,
      language: course.language,
      estimatedDuration: totalDuration,
      previewVideoUrl: course.previewVideoUrl,
      lastWatchedChapterId: progress.lastWatchedChapterId,
      createdBy: {
        id: course.createdBy?.id,
        organizationName: course.createdBy?.organizationName,
        profilePicture: course.createdBy?.organizationDetails?.profilePicture,
      },
      topics: topicsWithChapters,
    };
  }

  async updateChapterProgress(
    courseId: number,
    topicId: number,
    chapterId: number,
    userId: number,
    progressInSeconds: number,
    duration: number,
  ): Promise<any> {
    const course = await this.courseRepo.findOne({
      where: { id: courseId },
      relations: [
        'topics',
        'topics.chapters',
        'topics.chapters.quizzes',
        'createdBy',
        'createdBy.organizationDetails',
      ],
    });

    if (!course) throw new NotFoundException('Course not found');

    const topic = course.topics.find((t) => t.id === topicId);
    if (!topic) throw new NotFoundException('Topic not found in course');

    const chapters = topic.chapters.sort((a, b) => a.id - b.id);
    const chapterIndex = chapters.findIndex((c) => c.id === chapterId);
    const chapter = chapters[chapterIndex];
    if (!chapter) throw new NotFoundException('Chapter not found in topic');

    const watchedPercentage = duration
      ? Math.floor((progressInSeconds / duration) * 100)
      : 0;

    // CourseProgress
    let courseProgress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } },
    });

    if (!courseProgress) {
      courseProgress = this.courseProgressRepo.create({
        user: { id: userId } as any,
        course: { id: courseId } as any,
        completedChapterIds: [],
        videoProgress: {},
        completedQuizIds: [],
        lastWatchedChapterId: null,
      });
    }

    courseProgress.videoProgress[chapterId] = watchedPercentage;

    if (
      watchedPercentage >= 90 &&
      !courseProgress.completedChapterIds.includes(chapterId)
    ) {
      courseProgress.completedChapterIds.push(chapterId);
      courseProgress.lastWatchedChapterId = chapterId;
    }

    await this.courseProgressRepo.save(courseProgress);

    // ChapterProgress
    let chapterProgress = await this.chapterProgressRepo.findOne({
      where: {
        user: { id: userId },
        chapter: { id: chapterId },
      },
    });

    if (!chapterProgress) {
      chapterProgress = this.chapterProgressRepo.create({
        user: { id: userId } as any,
        chapter: { id: chapterId } as any,
        isLocked: false,
      });
    }

    chapterProgress.isCompleted = watchedPercentage >= 90;
    chapterProgress.progressInSeconds = progressInSeconds;
    await this.chapterProgressRepo.save(chapterProgress);

    // Unlock next chapter
    const nextChapter = chapters[chapterIndex + 1];
    if (nextChapter) {
      let nextChapterProgress = await this.chapterProgressRepo.findOne({
        where: {
          user: { id: userId },
          chapter: { id: nextChapter.id },
        },
      });

      if (!nextChapterProgress) {
        nextChapterProgress = this.chapterProgressRepo.create({
          user: { id: userId } as any,
          chapter: { id: nextChapter.id } as any,
          isLocked: false,
        });
      } else {
        nextChapterProgress.isLocked = false;
      }

      await this.chapterProgressRepo.save(nextChapterProgress);
    }

    // Fetch all chapter progress
    const allChapterProgress = await this.chapterProgressRepo.find({
      where: { user: { id: userId } },
      relations: ['chapter'],
    });

    const progressMap = new Map<number, any>();
    allChapterProgress.forEach((p) => {
      if (p.chapter) {
        progressMap.set(p.chapter.id, p);
      }
    });

    const enrollment = await this.enrollmentRepo.findOne({
      where: {
        user: { id: userId },
        course: { id: courseId },
        isEnroll: true,
      },
    });

    const isEnrolled = !!enrollment;

    const enrollmentCount = await this.enrollmentRepo.count({
      where: { course: { id: courseId }, isEnroll: true },
    });

    const totalDuration = course.topics
      .flatMap((t) => t.chapters)
      .reduce((sum, ch) => sum + (Number(ch.duration) || 0), 0);

    const topicsWithChapters = course.topics.map((t) => {
      const sortedChapters = t.chapters.sort((a, b) => a.id - b.id);

      const chapters = sortedChapters.map((ch, index) => {
        const chProgress = progressMap.get(ch.id);
        const isCompleted = chProgress?.isCompleted ?? false;
        const watched = courseProgress.videoProgress[ch.id] || 0;

        let isLocked: boolean;
        if (chProgress && typeof chProgress.isLocked === 'boolean') {
          isLocked = chProgress.isLocked;
        } else if (index === 0) {
          isLocked = false;
        } else {
          const prevChapter = sortedChapters[index - 1];
          const prevProgress = progressMap.get(prevChapter.id);
          const prevCompleted = prevProgress?.isCompleted ?? false;
          isLocked = !prevCompleted;
        }

        return {
          id: ch.id,
          title: ch.chapterTitle,
          description: ch.description,
          duration: Number(ch.duration) || 0,
          mediaUrl: !isLocked ? ch.mediaUrl : null,
          isLocked,
          isCompleted,
          watchedPercentage: Math.floor(watched),
          quizIds: ch.quizzes?.map((quiz) => quiz.id) || [],
        };
      });

      return {
        topicId: t.id,
        topicTitle: t.topicTitle,
        chapters,
      };
    });

    return {
      isEnrolled,
      enrollmentCount,
      courseId: course.id,
      courseTitle: course.courseName,
      thumbnailUrl: course.thumbnailUrl,
      whatYouWillLearn: course.whatTheyLearn,
      tags: course.tags,
      language: course.language,
      estimatedDuration: totalDuration,
      previewVideoUrl: course.previewVideoUrl,
      lastWatchedChapterId: courseProgress.lastWatchedChapterId,
      createdBy: {
        id: course.createdBy?.id,
        organizationName: course.createdBy?.organizationName,
        profilePicture: course.createdBy?.organizationDetails?.profilePicture,
      },
      topics: topicsWithChapters,
    };
  }

  async getUserCourseProgress(userId: number) {
    const enrollments = await this.enrollmentRepo.find({
      where: {
        user: { id: userId },
        isEnroll: true,
      },
      relations: [
        'course',
        'course.topics',
        'course.topics.chapters',
        'course.createdBy',
        'course.createdBy.organizationDetails',
      ],
    });

    const progressRecords = await this.courseProgressRepo.find({
      where: { user: { id: userId } },
      relations: ['course'],
    });

    return enrollments.map(({ course }) => {
      const allChapters = course.topics.flatMap((topic) => topic.chapters);
      const totalChapters = allChapters.length;

      const progress = progressRecords.find(
        (p) => p.course && p.course.id === course.id,
      );

      const completedChapterIds = progress?.completedChapterIds || [];
      const lastWatchedChapterId = progress?.lastWatchedChapterId || null;

      const completedChapters = allChapters.filter((ch) =>
        completedChapterIds.includes(ch.id),
      ).length;

      const percentage =
        totalChapters === 0
          ? 0
          : Math.round((completedChapters / totalChapters) * 100);

      const lastWatchedChapter = allChapters.find(
        (ch) => ch.id === lastWatchedChapterId,
      );

      return {
        courseId: course.id,
        courseName: course.courseName,
        isEnrolled: true,
        totalChapters,
        completedChapters,
        progressPercentage: percentage,
        lastWatchedChapter: lastWatchedChapter
          ? {
              id: lastWatchedChapter.id,
              title: lastWatchedChapter.chapterTitle,
            }
          : null,
        thumbnailUrl: course.thumbnailUrl,
        whatYouLearn: course.whatTheyLearn,
        createdBy: {
          id: course.createdBy?.id,
          profilePicture: course.createdBy?.organizationDetails?.profilePicture,
          organizationName: course.createdBy?.organizationName,
        },
      };
    });
  }

  //get completed courses by users
  async getAllCompletedCoursesByUser(userId: number) {
    const user = await this.userRepo.findOne({
      where: { id: userId },
      select: ['FullName', 'organizationName'],
    });

    if (!user) throw new NotFoundException('User not found');

    // 🔍 Get all enrollments for the user
    const enrollments = await this.enrollmentRepo.find({
      where: {
        user: { id: userId },
        isEnroll: true,
      },
      relations: ['course'],
    });

    const enrolledCourseIds = enrollments.map((e) => e.course.id);

    const progressRecords = await this.courseProgressRepo.find({
      where: { user: { id: userId } },
      relations: [
        'course',
        'course.topics',
        'course.topics.chapters',
        'course.createdBy',
        'course.createdBy.organizationDetails',
      ],
    });

    const completedCourses: {
      courseId: number;
      courseTitle: string;
      thumbnailUrl: string;
      whatTheyLearn: string;
      userFullName: string;
      organizationName?: string;
      completionDate: Date;
      isEnrolled: boolean;
      totalChapters: number;
      completedChapters: number;
      progressPercentage: number;
      createdBy: {
        id: number;
        profilePicture?: string;
        organizationName?: string;
      };
    }[] = [];

    for (const progress of progressRecords) {
      const course = progress.course;

      if (!course || !course.topics) continue;

      const chapters = course.topics?.flatMap((topic) => topic.chapters) || [];

      const allChaptersCompleted =
        chapters.length > 0 &&
        chapters.every((chapter) =>
          progress.completedChapterIds.includes(chapter.id),
        );

      const allQuizzesCompleted = chapters.every((chapter: any) => {
        if (chapter.quizId) {
          return progress.completedQuizIds?.includes(chapter.quizId);
        }
        return true;
      });

      const totalChapters = chapters.length;
      const completedChapters = chapters.filter((chapter) =>
        progress.completedChapterIds.includes(chapter.id),
      ).length;

      const progressPercentage =
        totalChapters === 0
          ? 0
          : Math.round((completedChapters / totalChapters) * 100);

      if (allChaptersCompleted && allQuizzesCompleted) {
        completedCourses.push({
          courseId: course.id,
          courseTitle: course.courseName,
          thumbnailUrl: course.thumbnailUrl,
          whatTheyLearn: course.whatTheyLearn,
          userFullName: user.FullName,
          createdBy: {
            id: course.createdBy?.id,
            profilePicture:
              course.createdBy?.organizationDetails?.profilePicture,
            organizationName: course.createdBy?.organizationName,
          },
          completionDate: progress.lastUpdated,
          isEnrolled: enrolledCourseIds.includes(course.id),
          totalChapters,
          completedChapters,
          progressPercentage,
        });
      }
    }

    return completedCourses;
  }

  //update course
  async updateCourseFields(id: number, data: UpdateCourseDto): Promise<Course> {
    const course = await this.courseRepo.findOneBy({ id });
    if (!course) throw new Error('Course not found');

    // Only update scalar fields
    course.courseName = data.courseName ?? course.courseName;
    course.language = data.language ?? course.language;
    course.whatTheyLearn = data.whatTheyLearn ?? course.whatTheyLearn;
    course.tags = data.tags ?? course.tags;
    course.thumbnailUrl = data.thumbnailUrl ?? course.thumbnailUrl;
    course.status = data.status ?? course.status;

    return this.courseRepo.save(course);
  }

  //update topic
  async updateTopicFields(
    id: number,
    data: { topicTitle?: string; numberOfChapters?: number },
  ) {
    const topic = await this.topicRepo.findOneBy({ id });
    if (!topic) throw new Error('Topic not found');

    topic.topicTitle = data.topicTitle ?? topic.topicTitle;
    topic.numberOfChapters = data.numberOfChapters ?? topic.numberOfChapters;

    return this.topicRepo.save(topic);
  }

  //update chapter
  async updateChapterFields(
    id: number,
    data: {
      chapterTitle?: string;
      description?: string;
      mediaUrl?: string;
      prerequisiteChapterIds?: number[];
    },
  ) {
    const chapter = await this.chapterRepo.findOneBy({ id });
    if (!chapter) throw new Error('Chapter not found');

    chapter.chapterTitle = data.chapterTitle ?? chapter.chapterTitle;
    chapter.description = data.description ?? chapter.description;
    chapter.mediaUrl = data.mediaUrl ?? chapter.mediaUrl;
    chapter.prerequisiteChapterIds = (
      data.prerequisiteChapterIds ?? chapter.prerequisiteChapterIds
    )?.map(String);

    return this.chapterRepo.save(chapter);
  }

  //find course by status
  async findByStatus(
    status: 'active' | 'closed',
    userId: number,
  ): Promise<any[]> {
    const courses = await this.courseRepo.find({
      where: {
        status,
        createdBy: { id: userId },
      },
      relations: [
        'topics',
        'topics.chapters',
        'createdBy',
        'createdBy.organizationDetails',
      ],
    });

    return await Promise.all(
      courses.map(async (course) => {
        const enrollmentCount = await this.enrollmentRepo.count({
          where: { course: { id: Number(course.id) } },
        });

        return {
          id: course.id,
          courseName: course.courseName,
          thumbnailUrl: course.thumbnailUrl,
          status: course.status,
          whatTheyLearn: course.whatTheyLearn,
          language: course.language,
          enrollmentCount,
          createdBy: {
            id: course.createdBy?.id,
            organizationName: course.createdBy?.organizationName,
            profilePicture:
              course.createdBy?.organizationDetails?.profilePicture,
          },
        };
      }),
    );
  }

  //create quiz
  // async createQuiz(
  //   data: CreateQuizDto,
  //   chapter: Chapter,
  //   user: User,
  // ): Promise<any> {
  //   const quiz = this.quizRepo.create({
  //     chapter,
  //     createdBy: user,
  //     questions: data.questions.map((q) =>
  //       this.questionRepo.create({
  //         questionText: q.questionText,
  //         options: q.options,
  //         correctOptionIndex: q.correctOptionIndex,
  //       }),
  //     ),
  //   });

  //   const saved = await this.quizRepo.save(quiz);

  //   return {
  //     questions: saved.questions,
  //     id: saved.id,
  //     chapter: { id: chapter.id, title: chapter.chapterTitle },
  //     createdBy: {
  //       id: user.id,
  //       email: user.email,
  //       organizationName: user.organizationName,
  //     },
  //   };
  // }

  //attempt quiz
  async attemptQuiz(body: QuizAttemptDto, user: User) {
    const { quizId, answers } = body;

    // Validate quiz
    const quiz = await this.quizRepo.findOne({
      where: { id: quizId },
      relations: ['questions'],
    });

    if (!quiz) throw new Error('Quiz not found');
    if (!quiz.questions || quiz.questions.length === 0)
      throw new Error('No questions in quiz');

    // Get latest attempt number
    const latestAttempt = await this.quizAttemptRepo.findOne({
      where: {
        user: { id: user.id },
        quiz: { id: quizId },
      },
      order: {
        created_at: 'DESC',
      },
    });

    const nextAttemptNumber = latestAttempt
      ? latestAttempt.attemptNumber + 1
      : 1;

    // Calculate score
    let score = 0;
    const questionsResult = quiz.questions.map((q, idx) => {
      const userSelectedIndex = answers[idx];

      // Get correct option index from the text stored in `correctOption`
      const correctOptionIndex = q.options.findIndex(
        (opt) => opt === q.correctOption,
      );

      const isCorrect = userSelectedIndex === correctOptionIndex;
      if (isCorrect) score++;

      return {
        questionText: q.questionText,
        options: q.options,
        correctOptionIndex,
        userSelectedIndex,
        isCorrect,
      };
    });

    // Save the attempt
    await this.quizAttemptRepo.save({
      user: { id: user.id } as any,
      quiz: { id: quizId } as any,
      score,
      attemptNumber: nextAttemptNumber,
      answers,
    });

    return {
      quizId,
      user: {
        id: user.id,
        name: user.FullName,
        email: user.email,
        designation: user.designation,
      },
      questions: questionsResult,
      score,
      total: quiz.questions.length,
      attemptNumber: nextAttemptNumber,
    };
  }

  //get latest quiz attempt
  async getLatestQuizAttemptDetailed(userId: number, quizId: number) {
    const attempt = await this.quizAttemptRepo.findOne({
      where: {
        user: { id: userId },
        quiz: { id: quizId },
      },
      order: {
        attemptedAt: 'DESC',
      },
      relations: ['user'],
    });

    if (!attempt) throw new Error('No attempt found');

    const quiz = await this.quizRepo.findOne({
      where: { id: quizId },
      relations: ['questions'],
    });

    if (!quiz) throw new Error('Quiz not found');

    const user = attempt.user;

    const answers = attempt.answers || [];
    let score = 0;
    const questionsResult = quiz.questions.map((q, idx) => {
      const userSelectedIndex = answers[idx];
      const correctOptionIndex = q.options.findIndex(
        (opt) => opt === q.correctOption,
      );
      const isCorrect = userSelectedIndex === correctOptionIndex;
      if (isCorrect) score++;
      return {
        questionText: q.questionText,
        options: q.options,
        correctOptionIndex,
        userSelectedIndex,
        isCorrect,
      };
    });

    return {
      quizId,
      user: {
        id: user.id,
        name: user.FullName,
        email: user.email,
      },
      questions: questionsResult,
      score,
      total: quiz.questions.length,
      attemptNumber: attempt.attemptNumber,
      attemptedAt: attempt.attemptedAt,
    };
  }

  //get quiz by id
  async getQuizById(quizId: number): Promise<any> {
    const quiz = await this.quizRepo.findOne({
      where: { id: quizId },
      relations: ['chapter', 'questions', 'createdBy'],
    });

    if (!quiz) throw new NotFoundException('Quiz not found');

    return {
      id: quiz.id,
      chapter: {
        id: quiz.chapter.id,
        title: quiz.chapter.chapterTitle,
      },
      createdBy: {
        id: quiz.createdBy.id,
        email: quiz.createdBy.email,
        organizationName: quiz.createdBy.organizationName,
      },
      questions: quiz.questions.map((q) => ({
        id: q.id,
        questionText: q.questionText,
        options: q.options,
        correctOptionIndex: q.correctOption,
      })),
    };
  }

  // Toggle save/unsave course
  async toggleSaveCourse(userId: number, courseId: number) {
    const user = await this.userRepo.findOneByOrFail({ id: userId });
    const course = await this.courseRepo.findOneByOrFail({ id: courseId });

    const existing = await this.savedCourseRepo.findOne({
      where: {
        user: { id: userId },
        course: { id: courseId },
      },
    });

    if (existing) {
      await this.savedCourseRepo.remove(existing);
      return { success: true, message: 'Course unsaved' };
    } else {
      const saved = this.savedCourseRepo.create({ user, course });
      await this.savedCourseRepo.save(saved);
      return { success: true, message: 'Course saved' };
    }
  }

  // Get all saved courses for a user
  async getSavedCourses(userId: number) {
    const savedCourses = await this.savedCourseRepo.find({
      where: { user: { id: userId } },
      relations: [
        'user',
        'course',
        'course.topics',
        'course.topics.chapters',
        'course.createdBy',
        'course.createdBy.organizationDetails',
      ],
    });

    // 🔍 Fetch enrolled course IDs
    const enrollments = await this.enrollmentRepo.find({
      where: {
        user: { id: userId },
        isEnroll: true,
      },
      relations: ['course'],
    });

    const enrolledCourseIds = enrollments.map((e) => e.course.id);

    return savedCourses.map(({ user, course }) => ({
      user: {
        userId: user.id,
        fullName: user.FullName,
        email: user.email,
      },
      course: {
        id: course.id,
        courseName: course.courseName,
        language: course.language,
        whatTheyLearn: course.whatTheyLearn,
        tags: course.tags,
        thumbnailUrl: course.thumbnailUrl,
        status: course.status,
        estimatedDuration: course.estimatedDuration,
        previewVideoUrl: course.previewVideoUrl,
        isEnrolled: enrolledCourseIds.includes(course.id),
        createdBy: {
          id: course.createdBy?.id,
          organizationName: course.createdBy?.organizationName,
          profilePicture: course.createdBy?.organizationDetails?.profilePicture,
        },
        topics: course.topics.map((topic) => ({
          topicId: topic.id,
          topicTitle: topic.topicTitle,
          chapters: topic.chapters.map((chapter) => ({
            chapterId: chapter.id,
            chapterTitle: chapter.chapterTitle,
            description: chapter.description,
            mediaUrl: chapter.mediaUrl,
          })),
        })),
      },
    }));
  }

  //enroll users in courses
  async enrollUser(userId: number, courseId: number): Promise<any> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    const course = await this.courseRepo.findOne({ where: { id: courseId } });

    if (!user || !course)
      throw new NotFoundException('User or Course not found');

    let enrollment = await this.enrollmentRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } },
    });

    if (enrollment?.isEnroll) {
      return { message: 'User already enrolled' };
    }

    if (enrollment) {
      enrollment.isEnroll = true;
      enrollment.enrolledAt = new Date();
    } else {
      enrollment = this.enrollmentRepo.create({
        user,
        course,
        isEnroll: true,
        enrolledAt: new Date(),
      });
    }

    await this.enrollmentRepo.save(enrollment);
    return { message: 'Enrolled successfully', enrollment };
  }

  // Get list of enrolled users with full user details
  async getEnrolledUsers(courseId: number) {
    const enrollments = await this.enrollmentRepo.find({
      where: { course: { id: courseId } },
      relations: ['user', 'course'],
    });

    return enrollments.map((enrollment) => ({
      courseId: enrollment.course.id,
      userId: enrollment.user.id,
      fullName: enrollment.user.FullName,
      email: enrollment.user.email,
      enrolledAt: enrollment.enrolledAt,
    }));
  }

  // Get total enrollment count for a course
  async getEnrollmentCount(courseId: number) {
    const count = await this.enrollmentRepo.count({
      where: { course: { id: courseId } },
    });

    return { courseId, enrollmentCount: count };
  }

  // //get course with lock status
  // async getCourseContent(courseId: number, userId: number) {
  //   const course = await this.courseRepo.findOne({
  //     where: { id: courseId },
  //     relations: ['topics', 'topics.chapters'],
  //   });

  //   const progress = await this.courseProgressRepo.findOne({
  //     where: { user: { id: userId }, course: { id: courseId } },
  //   });

  //   const completedIds = progress?.completedChapterIds || [];

  //   const totalDuration = course?.topics
  //     .flatMap((topic) => topic.chapters)
  //     .reduce((sum, chapter) => sum + (Number(chapter.duration) || 0), 0);

  //   const chaptersWithLock = course?.topics.map((topic) => {
  //     let unlockNext = true;

  //     const updatedChapters = topic.chapters.map((chapter) => {
  //       const isCompleted = completedIds.includes(chapter.id);
  //       const isUnlocked = unlockNext;

  //       if (!isCompleted) unlockNext = false;

  //       return {
  //         id: chapter.id,
  //         title: chapter.chapterTitle,
  //         duration: chapter.duration,
  //         mediaUrl: isUnlocked ? chapter.mediaUrl : null,
  //         isLocked: !isUnlocked,
  //         isCompleted,
  //       };
  //     });

  //     return {
  //       topicId: topic.id,
  //       topicTitle: topic.topicTitle,
  //       chapters: updatedChapters,
  //     };
  //   });

  //   return {
  //     courseId: course?.id,
  //     courseTitle: course?.courseName,
  //     thumbnailUrl: course?.thumbnailUrl,
  //     whatYouWillLearn: course?.whatTheyLearn,
  //     tags: course?.tags,
  //     language: course?.language,
  //     estimatedDuration: totalDuration,
  //     previewVideoUrl: course?.previewVideoUrl,
  //     topics: chaptersWithLock,
  //   };
  // }

  // //update course progress
  // async updateChapterProgress(
  //   userId: number,
  //   courseId: number,
  //   chapterId: number,
  //   progress: number,
  // ) {
  //   const user = await this.userRepo.findOne({ where: { id: userId } });
  //   if (!user) throw new NotFoundException('User not found');

  //   const course = await this.courseRepo.findOne({
  //     where: { id: courseId },
  //     relations: ['topics', 'topics.chapters', 'createdBy'],
  //   });
  //   if (!course) throw new NotFoundException('Course not found');

  //   const chapter = await this.chapterRepo.findOne({
  //     where: { id: chapterId },
  //   });
  //   if (!chapter) throw new NotFoundException('Chapter not found');

  //   let courseProgress = await this.courseProgressRepo.findOne({
  //     where: { user: { id: userId }, course: { id: courseId } },
  //   });

  //   if (!courseProgress) {
  //     courseProgress = this.courseProgressRepo.create({
  //       user,
  //       course,
  //       completedChapterIds: [],
  //       videoProgress: {},
  //       lastWatchedChapterId: chapterId,
  //     });
  //   }

  //   courseProgress.videoProgress = courseProgress.videoProgress || {};
  //   const chapterKey = chapterId.toString();
  //   const existingProgress = courseProgress.videoProgress[chapterKey] || 0;
  //   courseProgress.videoProgress[chapterKey] = Math.max(
  //     existingProgress,
  //     progress,
  //   );

  //   if (
  //     progress >= 90 &&
  //     !courseProgress.completedChapterIds.includes(chapterId)
  //   ) {
  //     courseProgress.completedChapterIds.push(chapterId);
  //   }

  //   courseProgress.lastWatchedChapterId = chapterId;
  //   courseProgress.lastUpdated = new Date();

  //   await this.courseProgressRepo.save(courseProgress);

  //   const allChapters = course.topics.flatMap((topic) => topic.chapters);
  //   const totalChapters = allChapters.length;
  //   const completedChapters = allChapters.filter((ch) =>
  //     courseProgress.completedChapterIds.includes(ch.id),
  //   ).length;

  //   const progressPercentage =
  //     totalChapters === 0
  //       ? 0
  //       : Math.round((completedChapters / totalChapters) * 100);

  //   let currentTopicTitle = '';
  //   for (const topic of course.topics) {
  //     if (topic.chapters.some((ch) => ch.id === chapterId)) {
  //       currentTopicTitle = topic.topicTitle;
  //       break;
  //     }
  //   }

  //   // === Prepare Chapters with Lock Status ===
  //   const chapterLockMap: Record<number, boolean> = {};
  //   let unlockNext = true;

  //   const topicsWithChapters = course.topics.map((topic) => {
  //     const sortedChapters = topic.chapters.sort(
  //       (a, b) => a.created_at.getTime() - b.created_at.getTime(),
  //     );

  //     return {
  //       topicTitle: topic.topicTitle,
  //       chapters: sortedChapters.map((ch) => {
  //         let isLocked =
  //           !courseProgress.completedChapterIds.includes(ch.id) && !unlockNext;
  //         if (
  //           !isLocked &&
  //           !courseProgress.completedChapterIds.includes(ch.id)
  //         ) {
  //           unlockNext = false; // Only unlock the next one after last completed
  //         }

  //         return {
  //           id: ch.id,
  //           chapterTitle: ch.chapterTitle,
  //           description: ch.description,
  //           mediaUrl: ch.mediaUrl,
  //           isLocked,
  //         };
  //       }),
  //     };
  //   });

  //   return {
  //     message: 'Progress updated successfully',
  //     courseId: course.id,
  //     courseName: course.courseName,
  //     whatTheyLearn: course.whatTheyLearn,
  //     progressPercentage,
  //     lastWatchedChapterId: courseProgress.lastWatchedChapterId,
  //     completedChapters,
  //     totalChapters,
  //     topicTitle: currentTopicTitle,
  //     currentChapter: {
  //       id: chapter.id,
  //       chapterTitle: chapter.chapterTitle,
  //       description: chapter.description,
  //       mediaUrl: chapter.mediaUrl,
  //     },
  //     topics: topicsWithChapters,
  //   };
  // }

  //assign certificate to user
  async assignCertificateToUser(
    userId: number,
    courseId: number,
    pdfUrl: string,
  ) {
    const progress = await this.courseProgressRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } },
      relations: ['course', 'course.topics', 'course.topics.chapters'],
    });

    if (!progress) {
      throw new NotFoundException('Progress record not found');
    }

    const chapters = progress.course.topics.flatMap((t) => t.chapters);
    const allChaptersCompleted =
      chapters.length > 0 &&
      progress.completedChapterIds.length === chapters.length;

    const allQuizzesCompleted = chapters.every((chapter) => {
      if (chapter.quizId) {
        return progress.completedQuizIds?.includes(chapter.quizId);
      }
      return true;
    });

    if (!allChaptersCompleted || !allQuizzesCompleted) {
      throw new BadRequestException('User has not completed the course');
    }

    const existing = await this.certificateRepo.findOne({
      where: { user: { id: userId }, course: { id: courseId } },
    });

    if (existing) {
      throw new BadRequestException('Certificate already exists');
    }

    const certificate = this.certificateRepo.create({
      user: { id: userId },
      course: { id: courseId },
      pdfUrl,
    });

    await this.certificateRepo.save(certificate);

    return {
      message: 'Certificate assign successfully',
      certificate,
    };
  }

  // async getCourseWithUserProgress(userId: number, courseId: number) {
  //   // 1. Fetch course with relations
  //   const course = await this.courseRepo.findOne({
  //     where: { id: courseId },
  //     relations: [
  //       'createdBy',
  //       'createdBy.organizationDetails',
  //       'topics',
  //       'topics.chapters',
  //     ],
  //   });
  //   if (!course) throw new NotFoundException('Course not found');

  //   // 2. Check if user enrolled
  //   const enrollment = await this.enrollmentRepo.findOne({
  //     where: { user: { id: userId }, course: { id: courseId } },
  //   });
  //   const isEnrolled = !!enrollment;

  //   // 3. Get enrollment count
  //   const enrollmentCount = await this.enrollmentRepo.count({
  //     where: { course: { id: courseId } },
  //   });

  //   // 4. Fetch user progress on course
  //   let progress = await this.courseProgressRepo.findOne({
  //     where: { user: { id: userId }, course: { id: courseId } },
  //   });

  //   // If no progress, initialize empty
  //   const user = await this.userRepo.findOne({ where: { id: userId } });
  //   const courseEntity = await this.courseRepo.findOne({
  //     where: { id: courseId },
  //   });

  //   if (!user || !courseEntity)
  //     throw new NotFoundException('User or course not found');

  //   progress = this.courseProgressRepo.create({
  //     user,
  //     course: courseEntity,
  //     completedChapterIds: [],
  //     videoProgress: {},
  //     lastWatchedChapterId: null,
  //   });

  //   // 5. Flatten all chapters for easier processing
  //   const allChapters = course.topics.flatMap((topic) => topic.chapters);

  //   // 6. Calculate total duration (sum of chapter durations)
  //   // Ensure duration is number (cast if needed)
  //   const totalDuration = allChapters.reduce((sum, chapter) => {
  //     const dur =
  //       typeof chapter.duration === 'string'
  //         ? parseInt(chapter.duration, 10)
  //         : chapter.duration || 0;
  //     return sum + dur;
  //   }, 0);

  //   // 7. Compose topics and chapters with locking and progress info
  //   const topicsWithChapters = course.topics.map((topic) => {
  //     const chapters = topic.chapters.map((chapter, index) => {
  //       const isCompleted = progress.completedChapterIds.includes(chapter.id);

  //       // Unlock logic: chapter is unlocked if
  //       // - user enrolled
  //       // - AND it's the first chapter
  //       // - OR previous chapter is completed
  //       // - OR chapter itself is completed

  //       let isUnlocked = false;
  //       if (!isEnrolled) {
  //         isUnlocked = false; // not enrolled → locked
  //       } else if (index === 0) {
  //         isUnlocked = true; // first chapter unlocked
  //       } else {
  //         const prevChapterId = topic.chapters[index - 1].id;
  //         isUnlocked =
  //           progress.completedChapterIds.includes(prevChapterId) || isCompleted;
  //       }

  //       return {
  //         chapterId: chapter.id,
  //         chapterTitle: chapter.chapterTitle,
  //         description: chapter.description,
  //         mediaUrl: chapter.mediaUrl,
  //         duration:
  //           typeof chapter.duration === 'string'
  //             ? parseInt(chapter.duration, 10)
  //             : chapter.duration || 0,
  //         isCompleted,
  //         isUnlocked,
  //       };
  //     });

  //     return {
  //       topicId: topic.id,
  //       topicTitle: topic.topicTitle,
  //       chapters,
  //     };
  //   });

  //   // 8. Calculate progress percentage
  //   const completedCount = progress.completedChapterIds.length;
  //   const totalCount = allChapters.length;
  //   const progressPercentage =
  //     totalCount === 0 ? 0 : Math.round((completedCount / totalCount) * 100);

  //   // 9. Return the unified data
  //   return {
  //     courseId: course.id,
  //     courseName: course.courseName,
  //     whatTheyLearn: course.whatTheyLearn,
  //     thumbnailUrl: course.thumbnailUrl,
  //     createdBy: {
  //       id: course.createdBy?.id,
  //       organizationName: course.createdBy?.organizationName,
  //       profilePicture: course.createdBy?.organizationDetails?.profilePicture,
  //     },
  //     enrollmentCount,
  //     isEnrolled,
  //     lastWatchedChapterId: progress.lastWatchedChapterId || null,
  //     progressPercentage,
  //     estimatedDuration: totalDuration, // in seconds or minutes based on your data
  //     topics: topicsWithChapters,
  //   };
  // }
}
