import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsOptional,
  IsDateString,
  IsEnum,
  IsArray,
  ValidateNested,
  ValidateIf,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { ProjectStatus, ProjectCategory } from '../entities/project.entity';

export class CreateProjectDto {
  @ApiProperty({ description: 'Project name' })
  @IsNotEmpty()
  @IsString()
  projectName: string;

  @ApiProperty({
    description: 'Project category',
    enum: ProjectCategory,
  })
  @IsNotEmpty()
  @IsEnum(ProjectCategory)
  projectCategory: ProjectCategory;

  @ApiProperty({
    description: 'Project status',
    enum: ProjectStatus,
    default: ProjectStatus.LIVE,
  })
  @IsOptional()
  @IsEnum(ProjectStatus)
  status?: ProjectStatus = ProjectStatus.LIVE;

  @ApiProperty({
    description: 'Project images',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  images?: string[];

  @IsString()
  thumbnailImage?: string;

  @ApiProperty({ description: 'Fund raising goal', required: false })
  @ValidateIf((o) => o.projectCategory === ProjectCategory.DONATION)
  @IsNotEmpty()
  @IsNumber()
  fundRaisingGoal?: number;

  @ApiProperty({ description: 'Project location', required: false })
  @IsNumber()
  fundsCollected?: number;

  @ApiProperty({ description: 'Project location', required: false })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({ description: 'Project start date' })
  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  @ApiProperty({ description: 'Project finish date' })
  @IsNotEmpty()
  @IsDateString()
  finishDate: string;

  @ApiProperty({ description: 'Project description' })
  @IsNotEmpty()
  @IsString()
  projectDescription: string;

  @ApiProperty({
    description: 'Number of required volunteers',
    default: 1,
    required: false,
  })
  @ValidateIf((o) => o.projectCategory === ProjectCategory.VOLUNTEER)
  @IsOptional()
  @IsNumber()
  requiredVolunteers?: number;

  @ApiProperty({
    description: 'Total filled volunteer positions',
    required: false,
  })
  @ValidateIf((o) => o.projectCategory === ProjectCategory.VOLUNTEER)
  @IsOptional()
  @IsArray()
  totalFilledPositions?: string[];

  @ApiProperty({
    description: 'Project positions',
    type: [Object],
    required: false,
  })
  @ValidateIf((o) => o.projectCategory === ProjectCategory.VOLUNTEER)
  @IsArray()
  @ApiProperty({
    description: 'Project positions (only allowed for volunteer category)',
    type: [Object],
    required: false,
  })
  positions?: CreatePositionDto[];
}

export class PositionDto {
  @ApiProperty({ description: 'Position title' })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({ description: 'Position description' })
  @IsNotEmpty()
  @IsString()
  description: string;

  @ApiProperty({ description: 'Required skills', required: false })
  @IsOptional()
  @IsString()
  requiredSkills?: string;

  @ApiProperty({ description: 'Number of volunteers needed', required: false })
  @IsOptional()
  @IsNumber()
  volunteersNeeded?: number;
}

export class UpdateProjectDto {
  @ApiProperty({ description: 'Project name', required: false })
  @IsOptional()
  @IsString()
  projectName?: string;

  @ApiProperty({
    description: 'Project category',
    enum: ProjectCategory,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProjectCategory)
  projectCategory?: ProjectCategory;

  @ApiProperty({
    description: 'Project status',
    enum: ProjectStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProjectStatus)
  status?: ProjectStatus;

  @ApiProperty({
    description: 'Project images',
    type: [String],
    required: false,
  })
  @IsOptional()
  @IsArray()
  images?: string[];

  @ApiProperty({ description: 'Fund raising goal', required: false })
  @IsOptional()
  @IsNumber()
  fundRaisingGoal?: number;

  @ApiProperty({ description: 'Project location', required: false })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiProperty({ description: 'Project start date', required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ description: 'Project finish date', required: false })
  @IsOptional()
  @IsDateString()
  finishDate?: string;

  @ApiProperty({ description: 'Project description', required: false })
  @IsOptional()
  @IsString()
  projectDescription?: string;

  @ApiProperty({
    description: 'Number of required volunteers',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  requiredVolunteers?: number;

  @ApiProperty({
    description: 'Project positions',
    type: [PositionDto],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionDto)
  positions?: PositionDto[];

  @ApiProperty({
    description: 'Project status',
    enum: ProjectStatus,
    default: ProjectStatus.LIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(ProjectStatus)
  ProjectStatus = ProjectStatus.LIVE;
}

export class CreatePositionDto {
  @ApiProperty({ description: 'Position title' })
  @IsNotEmpty()
  @IsString()
  positionName: string;

  @ApiProperty({ description: 'Required number of volunteers' })
  @IsNumber()
  requiredVolunteers: number;

  @ApiProperty({ description: 'Position description' })
  @IsString()
  positionDescription: string;
}
