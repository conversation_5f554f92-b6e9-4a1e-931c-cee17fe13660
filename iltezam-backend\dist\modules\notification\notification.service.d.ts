import { Repository } from 'typeorm';
import { Notification } from './entities/notification.entity';
export declare class NotificationService {
    private notificationRepository;
    constructor(notificationRepository: Repository<Notification>);
    getUserNotifications(userId: number): Promise<Notification[]>;
    getNotificationById(id: number, userId: number): Promise<Notification | null>;
    markAsRead(id: number, userId: number): Promise<Notification>;
    notifyUser(data: {
        userId: number;
        title: string;
        content: string;
    }): Promise<Notification>;
}
