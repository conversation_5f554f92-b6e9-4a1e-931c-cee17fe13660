import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm'; // Add the Not import here
import { Server } from 'socket.io';
import { ChatRoom } from './entities/chat-room.entity';
import { Message } from './entities/message.entity';
import { User } from '../auth/entities/user.entity';
import { group } from 'console';

@Injectable()
export class LiveChatService {
  private io: Server;

  constructor(
    @InjectRepository(ChatRoom)
    private readonly chatRoomRepository: Repository<ChatRoom>,
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  setIo(io: Server) {
    this.io = io;
    this.setupSocketEvents();
  }

  async createRoom(senderId: number, receiverId: number) {
    if (!senderId || !receiverId) {
      throw new Error('Sender and Receiver required');
    }

    const userIds = [senderId, receiverId];

    // Force consistent order: smaller ID first
    userIds.sort((a, b) => a - b);

    const room = await this.chatRoomRepository
      .createQueryBuilder('room')
      .where('room.users = :userIds', { userIds })
      .getOne();

    if (room) {
      return {
        roomId: room.roomId,
        senderId: senderId,
        receiverId: receiverId,
      };
    }

    const newRoom = this.chatRoomRepository.create({
      roomId: uuidv4(),
      users: userIds,
    });

    const savedRoom = await this.chatRoomRepository.save(newRoom);

    return {
      roomId: savedRoom.roomId,
      senderId: senderId,
      receiverId: receiverId,
    };
  }

  async sendMessage(
    senderId: number,
    roomId: string,
    receiverId: number,
    messageText: string,
  ) {
    if (!roomId || !senderId || !receiverId || !messageText) {
      throw new Error(
        'All fields (roomId, sender, receiver, message) are required',
      );
    }

    const sender = await this.userRepository.findOne({
      where: { id: senderId },
    });
    const receiver = await this.userRepository.findOne({
      where: { id: receiverId },
    });

    if (!sender || !receiver) {
      throw new Error('Sender or Receiver not found');
    }

    const msg = new Message();
    msg.roomId = roomId;
    msg.sender = sender;
    msg.receiver = receiver;
    msg.message = messageText;

    const savedMessage = await this.messageRepository.save(msg);

    console.log('saved message', savedMessage);

    if (this.io) {
      this.io.to(roomId).emit('receiveMessage', msg);

      // console.log('yes fine');
    }

    return { success: true, message: 'Message sent' };
  }

  async createGroupRoom(
    creatorId: number,
    userIds: number[],
    groupName: string,
    groupPicture: string,
  ) {
    if (!creatorId || !userIds || userIds.length === 0 || !groupName) {
      throw new Error('Creator and at least one user required');
    }

    // Make sure creator is included in the users array
    if (!userIds.includes(creatorId)) {
      userIds.push(creatorId);
    }

    const newRoom = this.chatRoomRepository.create({
      roomId: uuidv4(),
      users: userIds,
      isGroup: true,
      groupName: groupName || 'New Group',
      groupPicture: groupPicture,
    });

    const savedRoom = await this.chatRoomRepository.save(newRoom);

    const creatorUser = await this.userRepository.findOne({
      where: { id: creatorId },
    });

    if (creatorUser) {
      const systemMessage = new Message();
      systemMessage.roomId = savedRoom.roomId;
      systemMessage.sender = creatorUser;
      systemMessage.message = `Group "${groupName}" created by ${creatorUser.FullName || creatorUser.email}`;
      await this.messageRepository.save(systemMessage);
    }

    return { roomId: savedRoom.roomId, groupName: savedRoom.groupName,groupImage: savedRoom.groupPicture };
  }
  // ek aur push krde

  async sendGroupMessage(
    senderId: number,
    roomId: string,
    messageText: string,
  ) {
    if (!roomId || !senderId || !messageText) {
      throw new Error('All fields (roomId, sender, message) are required');
    }

    const room = await this.chatRoomRepository.findOne({
      where: { roomId },
    });

    if (!room) {
      throw new Error('Room not found');
    }

    if (!room.users.includes(senderId)) {
      throw new Error('Sender is not a member of this group');
    }

    const sender = await this.userRepository.findOne({
      where: { id: senderId },
    });

    if (!sender) {
      throw new Error('Sender not found');
    }

    const msg = new Message();
    msg.roomId = roomId;
    msg.sender = sender;
    msg.message = messageText;
    msg.readUsers = [senderId]; 

    const savedMessage = await this.messageRepository.save(msg);

    if (this.io) {
      this.io.to(roomId).emit('receiveMessage', msg);
    }

    return { success: true, message: 'Message sent' };
  }

  async getMessages(roomId: string, userId: number) {
  if (!roomId) {
    throw new Error('Room ID is required');
  }

  const room = await this.chatRoomRepository.findOne({
    where: { roomId },
  });

  if (!room) {
    throw new Error('Room not found');
  }

  if (room.isGroup) {
    const messages = await this.messageRepository.find({
      where: { roomId },
      relations: ['sender'],
      order: { timestamp: 'ASC' },
    });

    for (const msg of messages) {
      // Agar readUsers array nahi hai toh initialize karo
      if (!Array.isArray(msg.readUsers)) msg.readUsers = [];

      // Agar user id readUsers me nahi hai toh add karo
      if (!msg.readUsers.includes(userId)) {
        msg.readUsers.push(userId);
      }

      // Agar sab users ne parh liya toh isRead true karo
      const allRead = room.users.every((uid) => msg.readUsers.includes(uid));
      if (allRead && !msg.isRead) {
        msg.isRead = true;
      }

      // Save karo agar koi update hua ho
      await this.messageRepository.save(msg);
    }

    return messages;
  } else {
    // Direct chat ka purana logic
    await this.messageRepository.update(
      { roomId: room.roomId, receiver: { id: userId } },
      { isRead: true },
    );

    return await this.messageRepository.find({
      where: { roomId },
      relations: ['sender', 'receiver'],
      order: { timestamp: 'ASC' },
    });
  }
}

  async getInbox(userId: number) {
    if (!userId) {
      throw new Error('User ID is required');
    }

    // Get all rooms the user is part of
    const rooms = await this.chatRoomRepository
      .createQueryBuilder('room')
      .where(':userId = ANY(room.users)', { userId })
      .getMany();

    const roomIds = rooms.map((room) => room.roomId);

    // Get the latest message from each room
    const latestMessages: any[] = []; // Define as any[] to fix type error
    const unreadCounts = {};

    for (const room of rooms) {
      // Get latest message in this room
      const latestMessage = await this.messageRepository.findOne({
        where: { roomId: room.roomId },
        relations: ['sender'],
        order: { timestamp: 'DESC' },
      });

      if (latestMessage) {
        // Count unread messages
        const unreadCount = await this.messageRepository.count({
          where: {
            roomId: room.roomId,
            isRead: false,
            sender: { id: Not(userId) }, // Messages not from current user
          },
        });

        unreadCounts[room.roomId] = unreadCount;

        if (room.isGroup) {
          // console.log('Group Room:', room.groupPicture);
       let unreadCount = 0;
           // Get all messages for this group room (only need readUsers)
      const groupMessages = await this.messageRepository.find({
        where: { roomId: room.roomId },
        select: ['readUsers'],
      });

      // Count messages where userId is NOT in readUsers
      unreadCount = groupMessages.reduce((count, msg) => {
        if (!Array.isArray(msg.readUsers) || !msg.readUsers.includes(userId)) {
          return count + 1;
        }
        return count;
      }, 0);

      unreadCounts[room.roomId] = unreadCount;
          
          latestMessages.push({
            roomId: room.roomId,
            isGroup: true,
            groupName: room.groupName,
            groupImage:room.groupPicture,
            message: latestMessage.message,
            senderName:
              latestMessage.sender.FullName || latestMessage.sender.email,
            timestamp: latestMessage.timestamp,
            profilePicture: latestMessage.sender.profilePicture,
            unreadMessages: unreadCount,
          });
        } else {
          // For direct messages, find the other user
          const otherUserId = room.users.find((id) => id !== userId);
          const otherUser = await this.userRepository.findOne({
            where: { id: otherUserId },
          });
 console.log( 'Other User:', otherUser?.id);

          latestMessages.push({
            roomId: room.roomId,
            userId:otherUser?.id,
            isGroup: false,
            senderId: otherUserId,
            senderName: otherUser?.FullName || otherUser?.email || 'Unknown',
            profilePicture: otherUser?.profilePicture || null,
            message:
              latestMessage.sender.id === userId
                ? `You: ${latestMessage.message}`
                : latestMessage.message,
            timestamp: latestMessage.timestamp,
            unreadMessages: unreadCount,
          });
        }
      }
    }

    // Sort by timestamp, newest first
    return latestMessages.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );
  }

  async getRoom(userId: number, partnerId: number) {
    if (!userId) {
      throw new Error('User ID is required');
    }

    const room = await this.chatRoomRepository
      .createQueryBuilder('room')
      .innerJoin('room.users', 'user1')
      .innerJoin('room.users', 'user2')
      .where('user1.id = :userId', { userId })
      .andWhere('user2.id = :partnerId', { partnerId })
      .getOne();

    if (!room) {
      throw new Error('No rooms found');
    }

    await this.messageRepository.update(
      { roomId: room.roomId, receiver: { id: userId } },
      { isRead: true },
    );

    return { roomId: room.roomId };
  }

  async getAllGroups(userId: number): Promise<any[]> {
  // Sare groups nikal lo
  const groups = await this.chatRoomRepository.find({
    where: { isGroup: true },
    select: ['roomId', 'groupName', 'groupPicture', 'users'],
  });

  
  const grps= groups.map((group) => ({
    ...group,
    joined: group.users.includes(userId),
  }));

  const sortedGroups = grps.sort((a, b) => {
  // Pehle joined groups, phir baqi
  if (a.joined && !b.joined) return -1;
  if (!a.joined && b.joined) return 1;
  return 0;
});
  return sortedGroups;

}

  private async getInboxData(userId: number) {
    const messages = await this.messageRepository.find({
      where: { receiver: { id: userId } },
      relations: ['sender'],
      order: { timestamp: 'DESC' },
    });

    const latestMessages = {};
    const senderIds = new Set();
    const unreadCounts = {};

    messages.forEach((msg) => {
      const senderId = msg.sender.id;
      if (!unreadCounts[senderId]) unreadCounts[senderId] = 0;
      if (!msg.isRead) unreadCounts[senderId] += 1;

      if (
        !latestMessages[senderId] ||
        new Date(msg.timestamp) > new Date(latestMessages[senderId].timestamp)
      ) {
        latestMessages[senderId] = {
          roomId: msg.roomId,
          senderId: senderId,
          message: msg.message,
          isRead: msg.isRead,
          timestamp: msg.timestamp,
        };
      }

      senderIds.add(senderId);
    });

    // const senderProfiles = await this.profileRepository.find({
    //   where: { user: { id: Array.from(senderIds) } },
    //   relations: ['user'],
    // });

    // const profileMap = {};
    // senderProfiles.forEach((profile) => {
    //   profileMap[profile.user.id] = {
    //     senderName: profile.displayName || 'Unknown',
    //     senderProfileImage: profile.profilePicture || null,
    //   };
    // });

    return Object.values(latestMessages).map((msg: any) => ({
      ...msg,
      // senderName: profileMap[msg.senderId]?.senderName || 'Unknown',
      // senderProfileImage: profileMap[msg.senderId]?.senderProfileImage || null,
      unreadMessages: unreadCounts[msg.senderId] || 0,
    }));
  }
  async joinGroup(roomId: string, userId: number) {
  const room = await this.chatRoomRepository.findOne({ where: { roomId } });
  if (!room) throw new Error('Group not found');
  if (!room.isGroup) throw new Error('Not a group room');

  if (!room.users.includes(userId)) {
    room.users.push(userId);
    await this.chatRoomRepository.save(room);
  }

  return { success: true, message: 'Joined group', roomId };
}

  private setupSocketEvents() {
    this.io.on('connection', (socket) => {
      console.log(`User connected: ${socket.id}`);

      socket.on('joinRoom', async ({ roomId }) => {
        socket.join(roomId);
        console.log(`User joined room: ${roomId}`);
      });

      socket.on(
        'sendMessage',
        async ({ roomId, sender, receiver, message, mediaUrl }) => {
          console.log(mediaUrl);
          try {
            const room = await this.chatRoomRepository.findOne({
              where: { roomId },
            });

            if (!room) {
              console.log('Room not found');
              return;
            }

            const senderUser = await this.userRepository.findOne({
              where: { id: sender },
            });

            if (!senderUser) {
              console.log('Sender not found');
              return;
            }

            const newMessage = new Message();
            newMessage.roomId = roomId;
            newMessage.sender = senderUser;

            if (!room.isGroup && receiver) {
              const receiverUser = await this.userRepository.findOne({
                where: { id: receiver },
              });

              if (!receiverUser) {
                console.log('Receiver not found');
                return;
              }

              newMessage.receiver = receiverUser;
            }                                     
          console.log("i am new message", newMessage);
          

            if (room.isGroup) {
            newMessage.readUsers = [sender];
            }


            newMessage.message = mediaUrl ? mediaUrl : message;

            await this.messageRepository.save(newMessage);

            const senderName =
              senderUser.FullName || senderUser.email || 'Unknown';
            const title = `New Message from ${senderName}`;
            const content = message
              ? message
              : mediaUrl
                ? 'Sent an Attachment'
                : 'No content';

            this.io.to(roomId).emit('receiveMessage', newMessage);

            // For group chats, update inbox for all members
            if (room.isGroup) {
              for (const userId of room.users) {
                if (userId !== sender) {
                  // Don't update sender's inbox
                  const latestMessages = await this.getInboxData(userId);
                  this.io
                    .to(userId.toString())
                    .emit('updateInbox', latestMessages);
                }
              }
            } else if (receiver) {
              // For direct messages, update receiver's inbox
              const latestMessages = await this.getInboxData(receiver);
              this.io
                .to(receiver.toString())
                .emit('updateInbox', latestMessages);
            }
          } catch (error) {
            console.error('Error in sendMessage:', error);
          }
        },
      );
      socket.on('getOnlineUsers', ({ userIds }, callback) => {
      // userIds: array of user IDs to check
      const onlineStatus = {};
      console.log('userIds from client:', userIds);
      
      for (const id of userIds) {
        console.log("looping this id", id);
        
        onlineStatus[id] = global.onlineUsers.has(id);
        // console.log("Global data is ", global.onlineUsers);
        
      // console.log(`User ${id} online status: ${onlineStatus[id]}`);
      // console.log(`Global onlineUsers:`, global.onlineUsers);
      
      }
      // Send result back to client
      if (callback) {
        callback(onlineStatus);
      } else {
        socket.emit('onlineUsersStatus', onlineStatus);
      }
    });

    socket.on("getInbox", async (userId, callback) => {
  try {
    const inbox = await this.getInbox(userId);
    console.log("get inbox event");
    if (callback) {
      callback({ success: true, data: inbox });
    } else {
      socket.emit("inbox", { success: true, data: inbox });
    }
  } catch (error) {
    if (callback) {
      callback({ success: false, message: error.message });
    } else {
      socket.emit("inbox", { success: false, message: error.message });
    }
  }
});

      socket.on('joinInbox', ({ userId }) => {
        socket.join(userId.toString());
      });

      socket.on('disconnect', () => {
        console.log(`User disconnected: ${socket.id}`);
      });
    });
  }
}
