import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { Chapter } from './chapter.entity';
import { Question } from './question.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import { QuizAttempt } from './quiz-attempt.entity';
export declare class Quiz extends BaseEntityClass {
    chapter: Chapter;
    questions: Question[];
    createdBy: User;
    attempts: QuizAttempt[];
}
