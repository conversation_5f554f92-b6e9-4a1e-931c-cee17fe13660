import {
  IsS<PERSON>,
  <PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON>al,
  IsNotEmpty,
  IsArray,
} from 'class-validator';

export class CreateProfileDto {
  @IsString()
  fullName: string;

  @IsString()
  organizationName: string;

  @IsEmail()
  email: string;

  @IsOptional()
  phone?: string;

  @IsOptional()
  bio?: string;
}

export class UpdateProfileDto {
  @IsOptional()
  @IsString()
  fullName?: string;

  @IsOptional()
  @IsString()
  organizationName?: string;

  @IsOptional()
  @IsString()
  phone?: string;

  @IsOptional()
  @IsString()
  bio?: string;

  @IsOptional()
  @IsString()
  profilePicture?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  designation?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skills?: string[];
}
