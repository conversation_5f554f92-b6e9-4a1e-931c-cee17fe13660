import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  statusCode: number;
  message: string;
  data: T;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    return next.handle().pipe(
      map(response => {
        const message = response?.message || 'success'; // Use response.message if available, else 'success'

        // If response contains 'message', exclude it from the `data` object
        const { message: _, ...dataWithoutMessage } = response;

        return {
          statusCode: 200,
          message,
          data: dataWithoutMessage.data !== undefined ? dataWithoutMessage.data : dataWithoutMessage,
        };
      })
    );
  }
}