import { Injectable, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  S3Client,
  DeleteObjectCommand,
  PutObjectCommand,
} from '@aws-sdk/client-s3';
import { v4 as uuid4 } from 'uuid';
@Injectable()
export class UploaddService {
  private s3Client: S3Client;
  private bucketName: string;
  constructor(private configService: ConfigService) {
    const bucketName = this.configService.get<string>('aws.bucketName');
    const region = this.configService.get<string>('aws.region');
    const accessKeyId = this.configService.get<string>('aws.accessKeyId');
    const secretAccessKey = this.configService.get<string>(
      'aws.secretAccessKey',
    );

    if (!bucketName || !region || !accessKeyId || !secretAccessKey) {
      throw new Error('AWS configuration is incomplete');
    }

    this.bucketName = bucketName;
    this.s3Client = new S3Client({
      region: region,
      credentials: {
        accessKeyId: accessKeyId,
        secretAccessKey: secretAccessKey,
      },
    });
  }
  async uploadFile(
    file: Express.Multer.File,
    folder: string = 'uploads',
  ): Promise<string> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'video/mp4',
      'video/webm',
      'video/ogg',
      'video/x-matroska',
      'video/quicktime',
      'video/3gpp',
    ];
    if (!allowedMimeTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        'Only images and videos  files are allowed',
      );
    }
    const fileExtension = file.originalname.split('.').pop();
    const fileName = `${folder}/${uuid4()}.${fileExtension}`;
    const uploadParams = {
      Bucket: this.bucketName,
      Key: fileName,
      Body: file.buffer,
      ContentType: file.mimetype,
    };
    try {
      await this.s3Client.send(new PutObjectCommand(uploadParams));
      return `https://${this.bucketName}.s3.${this.configService.get('aws.region')}.amazonaws.com/${fileName}`;
    } catch (error) {
      throw new BadRequestException(`Upload failed: ${error.message}`);
    }
  }
  async uploadFiles(
    files: Express.Multer.File[],
    folder = 'uploads',
  ): Promise<string[]> {
    return Promise.all(files.map((file) => this.uploadFile(file, folder)));
  }
  async deleteFile(fileUrl: string): Promise<boolean> {
    try {
      const key = fileUrl.split('amazonaws.com/')[1];
      const deleteParams = {
        Bucket: this.bucketName,
        Key: key,
      };
      await this.s3Client.send(new DeleteObjectCommand(deleteParams));
      return true;
    } catch (error) {
      throw new BadRequestException(`Delete failed: ${error.message}`);
    }
  }
}
