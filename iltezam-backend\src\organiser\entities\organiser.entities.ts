import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { User } from 'src/modules/auth/entities/user.entity';
import {
  Entity,
  Column,
  ManyToOne,
  JoinColumn,
  OneToOne,
  OneToMany,
} from 'typeorm';
import { OrganizationFollow } from './organization.follow.entities';
import { Project } from 'src/modules/project/entities/project.entity';

@Entity()
export class Organization extends BaseEntityClass {
  @Column()
  email: string;

  @Column()
  organizationName: string;

  @Column({ nullable: true })
  profilePicture: string;

  @Column({ nullable: true })
  organizationNameArabic: string;

  @Column()
  country: string;

  @Column({ nullable: true })
  website: string;

  @Column({ nullable: true })
  licenseFilePath: string;

  // Contact person
  @Column()
  contactName: string;

  @Column()
  contactPhone: string;

  @Column()
  contactTitle: string;

  @Column()
  contactEmail: string;

  // Governance
  @Column({ default: false })
  goodGovernance: boolean;

  @Column({ default: false })
  transparencyReporting: boolean;

  @Column({ default: false })
  sustainableFunding: boolean;

  @Column({ nullable: true })
  impactMeasurement: string;

  @Column()
  shortBio: string;

  @Column()
  organizationImage: string;

  @Column('text', { array: true, nullable: true })
  organizationTags: string[];

  @OneToOne(() => User, { nullable: false, eager: true })
  @JoinColumn({ name: 'user_id' })
  user: User;

  @OneToMany(() => OrganizationFollow, (follow) => follow.organization)
  followers: OrganizationFollow[];

  @OneToMany(() => Project, (project) => project.organization)
  projects: Project[];
}
