import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { BaseEntityClass } from 'src/modules/auth/entities/base.entity';
import { ProfileConnection } from './profile.connection.entity';

@Entity('profiles')
export class Profile extends BaseEntityClass {
  @Column({ nullable: true })
  fullName: string;

  @Column({ nullable: true })
  organizationName: string;

  @Column()
  email: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true, type: 'text' })
  bio: string;

  @Column({ nullable: true })
  profilePicture: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  designation: string;

  @Column({ nullable: true, type: 'simple-array' })
  skills: string[];

  @OneToOne(() => User, { cascade: true })
  @JoinColumn()
  user: User;

  @OneToMany(() => ProfileConnection, (conn) => conn.requester)
  sentConnections: ProfileConnection[];

  @OneToMany(() => ProfileConnection, (conn) => conn.receiver)
  receivedConnections: ProfileConnection[];
}
