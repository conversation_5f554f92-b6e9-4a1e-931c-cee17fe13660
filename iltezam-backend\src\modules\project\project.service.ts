import {
  Injectable,
  HttpException,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Project, ProjectCategory } from './entities/project.entity';
import { Position } from './entities/position.entity';
import { Donation, PaymentStatus } from './entities/donation.entity';
import {
  VolunteerApplication,
  ApplicationStatus,
} from './entities/volunteer-application.entity';
import { User } from '../auth/entities/user.entity';
import { CreateProjectDto, UpdateProjectDto } from './dtos/project.dto';
import { CreateDonationDto } from './dtos/donation.dto';
import { CreateVolunteerApplicationDto } from './dtos/volunteer-app.dto';
import { ProjectStatus } from './entities/project.entity';
import { Profile } from '../profile/entities/profile.entity';
import { Organization } from 'src/organiser/entities/organiser.entities';

@Injectable()
export class ProjectService {
  constructor(
    @InjectRepository(Project)
    private readonly projectRepo: Repository<Project>,
    @InjectRepository(Organization)
    private readonly organizationRepo: Repository<Organization>,
    @InjectRepository(Position)
    private readonly positionRepo: Repository<Position>,
    @InjectRepository(Donation)
    private readonly donationRepo: Repository<Donation>,
    @InjectRepository(VolunteerApplication)
    private readonly applicationRepo: Repository<VolunteerApplication>,
    @InjectRepository(User)
    private readonly userRepo: Repository<User>,
  ) {}

  async create(
    createProjectDto: CreateProjectDto,
    userId: number,
  ): Promise<Project> {
    try {
      const user = await this.userRepo.findOne({
        where: { id: Number(userId) },
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      if (!user.isOrganization) {
        throw new HttpException(
          'Only organizations can create projects',
          HttpStatus.FORBIDDEN,
        );
      }

      if (
        createProjectDto.projectCategory !== ProjectCategory.VOLUNTEER &&
        createProjectDto.positions &&
        createProjectDto.positions.length > 0
      ) {
        throw new HttpException(
          'Positions can only be added to volunteer projects',
          HttpStatus.BAD_REQUEST,
        );
      }
      const organization = await this.organizationRepo.findOne({
        where: { user: { id: userId } }, // find organization by user
      });

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }
      const project = this.projectRepo.create({
        ...createProjectDto,
        createdBy: user,
        fundsCollected: createProjectDto.fundsCollected ?? 0,
        organization,
      });

      const savedProject = await this.projectRepo.save(project);

      return savedProject;
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAll(category?: string, location?: string): Promise<any[]> {
    try {
      const where: any = {};

      if (category) {
        where.projectCategory = category;
      }

      if (location) {
        where.location = location;
      }

      const projects = await this.projectRepo.find({
        where,
        relations: ['positions', 'createdBy'],
        select: {
          positions: {
            id: true,
            positionName: true,
            requiredVolunteers: true,
            positionDescription: true,
          },
        },
        order: {
          totalClicks: 'DESC',
          fundsCollected: 'DESC',
        },
      });

      return projects.map((project) => {
        const { email, organizationName, profilePicture } =
          project.createdBy || {};
        const { organizationTags, organizationImage } =
          project.organization || {};
        return {
          ...project,
          createdBy: {
            email,
            organizationName,
            organizationTags,
            organizationImage,
          },
        };
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrganizationProjects(userId: number): Promise<Project[]> {
    const projects = await this.projectRepo.find({
      where: {
        createdBy: { id: userId },
      },
      relations: ['positions', 'createdBy', 'organization'],
    });

    return projects;
  }

  async findOne(id: string): Promise<any> {
    try {
      const project = await this.projectRepo.findOne({
        where: { id: Number(id) },
        relations: ['positions', 'createdBy', 'organization'],
      });

      if (!project) {
        throw new NotFoundException('Project not found');
      }
      project.totalClicks += 1;
      await this.projectRepo.save(project);
      const { id: userId, email, organizationName } = project.createdBy;

      const organizationTags = project.organization?.organizationTags || [];

      return {
        ...project,
        createdBy: {
          id: userId,
          email,
          organizationName,
          organizationTags,
        },
      };
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async update(
    id: string,
    updateProjectDto: UpdateProjectDto,
    userId: string,
  ): Promise<any> {
    try {
      const project = await this.projectRepo.findOne({
        where: { id: Number(id) },
        relations: ['createdBy'],
      });

      if (!project) {
        throw new NotFoundException('Project not found');
      }

      if (project.createdBy.id !== Number(userId)) {
        throw new HttpException(
          'You are not authorized to update this project',
          HttpStatus.FORBIDDEN,
        );
      }

      Object.assign(project, updateProjectDto);
      await this.projectRepo.save(project);

      if (updateProjectDto.positions && updateProjectDto.positions.length > 0) {
        await this.positionRepo.delete({ project: { id: Number(id) } });

        const positions = updateProjectDto.positions.map((pos) =>
          this.positionRepo.create({
            ...pos,
            project,
          }),
        );
        await this.positionRepo.save(positions);
      }

      const { email, organizationName } = project.createdBy;

      return {
        ...project,
        createdBy: {
          email,
          organizationName,
        },
      };
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async remove(id: string, userId: string): Promise<void> {
    try {
      const project = await this.projectRepo.findOne({
        where: { id: Number(id) },
        relations: ['createdBy'],
      });
      if (!project) {
        throw new NotFoundException('Project not found');
      }

      if (project.createdBy.id !== Number(userId)) {
        throw new HttpException(
          'You are not authorized to delete this project',
          HttpStatus.FORBIDDEN,
        );
      }

      await this.projectRepo.remove(project);
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getProjectsByOrganization(organizationId: string): Promise<any[]> {
    try {
      const projects = await this.projectRepo.find({
        where: {
          organization: { id: Number(organizationId) },
        },
        relations: ['positions', 'createdBy', 'organization'],
      });

      return projects.map((project) => {
        const { email, organizationName, profilePicture } =
          project.createdBy || {};
        return {
          ...project,
          createdBy: {
            email,
            organizationName,
            profilePicture,
          },
        };
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async closeProject(projectId: string, userId: string): Promise<Project> {
    try {
      const project = await this.projectRepo.findOne({
        where: { id: Number(projectId) },
        relations: ['createdBy'],
      });

      if (!project) {
        throw new NotFoundException('Project not found');
      }

      //  only the creator can close the project
      if (project.createdBy.id !== Number(userId)) {
        throw new HttpException(
          'You are not authorized to close this project',
          HttpStatus.FORBIDDEN,
        );
      }

      if (project.status === ProjectStatus.CLOSED) {
        throw new HttpException(
          'Project is already closed',
          HttpStatus.BAD_REQUEST,
        );
      }

      project.status = ProjectStatus.CLOSED;
      return await this.projectRepo.save(project);
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createDonation(
    createDonationDto: CreateDonationDto,
    userId: string,
  ): Promise<any> {
    try {
      const user = await this.userRepo.findOne({
        where: { id: Number(userId) },
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const project = await this.projectRepo.findOne({
        where: { id: Number(createDonationDto.projectId) },
      });

      if (!project) {
        throw new NotFoundException('Project not found');
      }

      if (project.projectCategory !== ProjectCategory.DONATION) {
        throw new HttpException(
          'This project does not accept donations',
          HttpStatus.BAD_REQUEST,
        );
      }

      const newTotal = (project.fundsCollected || 0) + createDonationDto.amount;
      if (newTotal > project.fundRaisingGoal) {
        throw new HttpException(
          'Donation exceeds fundraising goal',
          HttpStatus.BAD_REQUEST,
        );
      }

      const paymentSuccessful = true;
      const transactionId = `tx_${Math.random().toString(36).substring(2, 15)}`;

      const donation = this.donationRepo.create({
        donor: user,
        project: project,
        paymentMethod: createDonationDto.paymentMethod,
        frequency: createDonationDto.frequency,
        amount: createDonationDto.amount,
        status: paymentSuccessful
          ? PaymentStatus.SUCCESS
          : PaymentStatus.FAILED,
        transactionId: transactionId,
        paymentDate: new Date(),
        notes: createDonationDto.notes,
      });

      const savedDonation = await this.donationRepo.save(donation);

      if (paymentSuccessful) {
        project.fundsCollected = newTotal;
        await this.projectRepo.save(project);
      }

      return {
        id: savedDonation.id,
        amount: savedDonation.amount,
        status: savedDonation.status,
        paymentMethod: savedDonation.paymentMethod,
        frequency: savedDonation.frequency,
        transactionId: savedDonation.transactionId,
        paymentDate: savedDonation.paymentDate,
        notes: savedDonation.notes,
        donor: {
          email: user.email,
          FullName: user.FullName,
          profilePicture: user.profilePicture,
        },
        project: {
          id: project.id,
          projectName: project.projectName,
          projectCategory: project.projectCategory,
          thumbnailImage: project.thumbnailImage,
          fundRaisingGoal: project.fundRaisingGoal,
          fundsCollected: project.fundsCollected,
        },
      };
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getDonationsByUser(userId: string): Promise<any[]> {
    try {
      const donations = await this.donationRepo.find({
        where: { donor: { id: Number(userId) } },
        relations: ['project', 'donor'],
      });

      return donations.map((donation) => ({
        id: donation.id,
        amount: donation.amount,
        status: donation.status,
        paymentMethod: donation.paymentMethod,
        frequency: donation.frequency,
        transactionId: donation.transactionId,
        paymentDate: donation.paymentDate,

        donor: {
          FullName: donation.donor.FullName,
          email: donation.donor.email,
          profilePicture: donation.donor.profilePicture,
        },
        project: {
          id: donation.project.id,
          projectName: donation.project.projectName,
          projectCategory: donation.project.projectCategory,
          thumbnailImage: donation.project.thumbnailImage,
          fundRaisingGoal: donation.project.fundRaisingGoal,
          fundsCollected: donation.project.fundsCollected,
        },
      }));
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getDonationsByProject(projectId: string): Promise<any[]> {
    try {
      const donations = await this.donationRepo.find({
        where: { project: { id: Number(projectId) } },
        relations: ['donor', 'donor.profile'],
        order: { created_at: 'DESC' },
      });

      return donations.map((donation) => ({
        id: donation.id,
        amount: donation.amount,
        createdAt: donation.created_at,
        donor: {
          id: donation.donor.id,
          name:
            donation.donor.profile?.fullName ||
            donation.donor.profile?.organizationName ||
            donation.donor.email,
          email: donation.donor.email,
          profilePicture: donation.donor.profile?.profilePicture || null,
        },
      }));
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getDonationsById(id: string): Promise<Donation | null> {
    try {
      const donation = await this.donationRepo.findOne({
        where: { id: Number(id) },
        relations: ['project', 'donor', 'donor.profile'],
      });

      if (!donation) {
        throw new NotFoundException('Donation not found');
      }

      return donation;
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getVolunteerProjectWithPositions(): Promise<Project[]> {
    try {
      return await this.projectRepo.find({
        where: { projectCategory: ProjectCategory.VOLUNTEER },
        relations: ['positions'],
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getVolunteerProjectPositionsById(
    projectId: string,
    positionId: string,
  ): Promise<any> {
    try {
      const position = await this.positionRepo.findOne({
        where: {
          id: Number(positionId),
          project: { id: Number(projectId) },
        },
        relations: ['project'],
      });

      if (!position) {
        throw new NotFoundException('Position not found in this project');
      }

      if (position.project.projectCategory !== ProjectCategory.VOLUNTEER) {
        throw new HttpException(
          'This position is not from a volunteer project',
          HttpStatus.BAD_REQUEST,
        );
      }

      const response = {
        position: {
          id: position.id,
          positionName: position.positionName,
          requiredVolunteers: position.requiredVolunteers,
          positionDescription: position.positionDescription,
          positionApplications: position.totalApplications,
        },
        project: {
          id: position.project.id,
          projectName: position.project.projectName,
          projectCategory: position.project.projectCategory,
          status: position.project.status,
          location: position.project.location,
          startDate: position.project.startDate,
          finishDate: position.project.finishDate,
          projectDescription: position.project.projectDescription,
          images: position.project.images,
        },
      };

      return response;
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createApplication(
    projectId: string,
    positionId: string,
    createApplicationDto: CreateVolunteerApplicationDto,
    userId: string,
  ): Promise<VolunteerApplication> {
    try {
      const user = await this.userRepo.findOne({
        where: { id: Number(userId) },
      });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      const project = await this.projectRepo.findOne({
        where: { id: Number(projectId) },
      });
      if (!project) {
        throw new NotFoundException('Project not found');
      }

      const position = await this.positionRepo.findOne({
        where: { id: Number(positionId) },
        relations: ['project'],
      });
      if (!position || position.project.id !== project.id) {
        throw new NotFoundException('Position not found for this project');
      }

      const duplicateCheck = await this.applicationRepo.findOne({
        where: {
          applicant: { id: user.id },
          position: { id: position.id },
        },
      });

      if (duplicateCheck) {
        throw new HttpException(
          'You have already applied for this position',
          HttpStatus.BAD_REQUEST,
        );
      }

      if (!createApplicationDto.CV || createApplicationDto.CV.length === 0) {
        throw new HttpException(
          'At least one document is required',
          HttpStatus.BAD_REQUEST,
        );
      }

      const application = this.applicationRepo.create({
        applicant: user,
        project: project,
        position: position,
        CV: createApplicationDto.CV,
        status: ApplicationStatus.PENDING,
      });

      const savedApplication = await this.applicationRepo.save(application);
      position.totalApplications += 1;
      await this.positionRepo.save(position);

      return savedApplication;
    } catch (error) {
      throw new HttpException(
        error.message || 'Internal server error',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllApplicationAgainstProject(projectId: string): Promise<any[]> {
    try {
      const applications = await this.applicationRepo.find({
        where: { project: { id: Number(projectId) } },
        relations: ['applicant', 'applicant.profile', 'position'],
        order: { created_at: 'DESC' },
      });

      return applications.map((application) => ({
        id: application.id,
        status: application.status,
        createdAt: application.created_at,
        CV: application.CV,
        position: {
          id: application.position.id,
          title: application.position.positionName,
        },
        applicant: {
          id: application.applicant.id,
          email: application.applicant.email,
          fullName: application.applicant.profile?.fullName || null,
          profilePicture: application.applicant.profile?.profilePicture || null,
        },
      }));
    } catch (error) {
      throw new HttpException(
        error.message || 'Something went wrong',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getApplicationsByUser(userId: string): Promise<VolunteerApplication[]> {
    try {
      return await this.applicationRepo.find({
        where: { applicant: { id: Number(userId) } },
        relations: ['project', 'position'],
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getApplicationsByProject(
    projectId: string,
  ): Promise<VolunteerApplication[]> {
    try {
      return await this.applicationRepo.find({
        where: { project: { id: Number(projectId) } },
        relations: ['applicant', 'position'],
      });
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateApplicationStatus(
    applicationId: string,
    status: ApplicationStatus,
    organizationId: string,
  ): Promise<VolunteerApplication> {
    try {
      const application = await this.applicationRepo.findOne({
        where: { id: Number(applicationId) },
        relations: ['project', 'project.createdBy'],
      });

      if (!application) {
        throw new NotFoundException('Application not found');
      }

      if (application.project.createdBy.id !== Number(organizationId)) {
        throw new HttpException(
          'You are not authorized to update this application',
          HttpStatus.FORBIDDEN,
        );
      }

      application.status = status;
      return await this.applicationRepo.save(application);
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getOrganizationDonations(userId: string): Promise<any[]> {
    try {
      const organization = await this.organizationRepo.findOne({
        where: { user: { id: Number(userId) } },
      });
      console.log('Organization found:', organization);
      if (!organization) {
        throw new NotFoundException('Organization not found');
      }
      const projects = await this.projectRepo.find({
        where: { organization: { id: organization.id } },
        relations: ['donations', 'donations.donor', 'donations.donor.profile'],
      });
      console.log('Projects found:', projects.length);
      const donations = projects.flatMap((project) => {
        console.log(
          `Project ${project.id} donations count:`,
          project.donations.length,
        );
        return project.donations.map((donation) => ({
          id: donation.id,
          amount: donation.amount,
          status: donation.status,
          paymentMethod: donation.paymentMethod,
          frequency: donation.frequency,
          transactionId: donation.transactionId,
          paymentDate: donation.paymentDate,
          createdAt: donation.created_at,
          project: {
            id: project.id,
            projectName: project.projectName,
            projectCategory: project.projectCategory,
            thumbnailImage: project.thumbnailImage,
            fundRaisingGoal: project.fundRaisingGoal,
            fundsCollected: project.fundsCollected,
          },

          donor: {
            id: donation.donor.id,
            name: donation.donor.profile?.fullName || donation.donor.email,
            email: donation.donor.email,
            profilePicture: donation.donor.profile?.profilePicture,
          },
        }));
      });
      console.log('Total donations found:', donations.length);
      return donations.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
      );
    } catch (error) {
      throw new HttpException(
        error.message,
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
