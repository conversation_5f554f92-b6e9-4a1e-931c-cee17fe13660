import {
  Controller,
  Get,
  Put,
  Post,
  Param,
  Body,
  UseGuards,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import { ProfileService } from './profile.service';
import { UpdateProfileDto } from './dtos/create-profile.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
} from '@nestjs/swagger';

interface AuthenticatedRequest extends Request {
  user: {
    id: number;
  };
}

@ApiTags('Profile')
@Controller('profile')
export class ProfileController {
  constructor(private readonly profileService: ProfileService) {}

  @Get('connections')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get connections for the authenticated user' })
  @ApiResponse({ status: 200, description: 'Connections found.' })
  async getMyConnections(@Req() req: AuthenticatedRequest) {
    const userId = req.user.id;
    console.log(userId);
    return this.profileService.getConnections(userId);
  }

  @Get('getMyProfile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get my profile using token' })
  @ApiResponse({ status: 200, description: 'Profile found.' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  async getMyProfile(@Req() req: AuthenticatedRequest) {
    const userId = req.user.id;
    return await this.profileService.getByUserId(userId);
  }

  @Get(':userId')
  @ApiOperation({ summary: 'Get profile by user ID' })
  @ApiParam({ name: 'userId', type: Number })
  async getProfileByUserId(@Param('userId', ParseIntPipe) userId: number) {
    return await this.profileService.getByUserId(userId, true);
  }

  @Put('update-my-profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update my profile using token' })
  @ApiResponse({ status: 200, description: 'Profile updated successfully' })
  @ApiResponse({ status: 404, description: 'Profile not found' })
  updateMyProfile(
    @Req() req: AuthenticatedRequest,
    @Body() updateProfileDto: UpdateProfileDto,
  ) {
    const userId = req.user.id;
    return this.profileService.updateByUserId(userId, updateProfileDto);
  }

  @Post(':receiverUserId/connect')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  async sendConnectionRequest(
    @Param('receiverUserId', ParseIntPipe) receiverUserId: number,
    @Req() req: AuthenticatedRequest,
  ) {
    console.log('i am requester id', req.user.id);
    console.log('i am receiver id', receiverUserId);

    return this.profileService.connectProfiles(req.user.id, receiverUserId);
  }

  @Post('connection/:connectionId/accept')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Accept a connection request' })
  @ApiParam({ name: 'connectionId', type: Number })
  acceptConnection(@Param('connectionId', ParseIntPipe) connectionId: number) {
    return this.profileService.acceptConnection(connectionId);
  }

  @Post('connection/:connectionId/reject')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Reject a connection request' })
  @ApiParam({ name: 'connectionId', type: Number })
  rejectConnection(@Param('connectionId', ParseIntPipe) connectionId: number) {
    return this.profileService.rejectConnection(connectionId);
  }
}
