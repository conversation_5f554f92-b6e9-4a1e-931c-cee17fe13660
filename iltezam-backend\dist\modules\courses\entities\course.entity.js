"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Course = void 0;
const class_validator_1 = require("class-validator");
const typeorm_1 = require("typeorm");
const topic_entity_1 = require("./topic.entity");
const base_entity_1 = require("../../auth/entities/base.entity");
const user_entity_1 = require("../../auth/entities/user.entity");
const enrollment_entity_1 = require("./enrollment.entity");
let Course = class Course extends base_entity_1.BaseEntityClass {
    courseName;
    language;
    whatTheyLearn;
    topicsTag;
    tags;
    thumbnailUrl;
    status;
    estimatedDuration;
    previewVideoUrl;
    createdBy;
    topics;
    enrollments;
};
exports.Course = Course;
__decorate([
    (0, typeorm_1.Column)(),
    (0, class_validator_1.IsNotEmpty)({ message: 'Course name is required' }),
    __metadata("design:type", String)
], Course.prototype, "courseName", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'english' }),
    (0, class_validator_1.IsNotEmpty)({ message: 'language is required' }),
    __metadata("design:type", String)
], Course.prototype, "language", void 0);
__decorate([
    (0, typeorm_1.Column)('text'),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], Course.prototype, "whatTheyLearn", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { array: true, nullable: true }),
    __metadata("design:type", Array)
], Course.prototype, "topicsTag", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { array: true, nullable: true }),
    __metadata("design:type", Array)
], Course.prototype, "tags", void 0);
__decorate([
    (0, typeorm_1.Column)('text', { nullable: true }),
    __metadata("design:type", String)
], Course.prototype, "thumbnailUrl", void 0);
__decorate([
    (0, typeorm_1.Column)({ default: 'active' }),
    __metadata("design:type", String)
], Course.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Course.prototype, "estimatedDuration", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], Course.prototype, "previewVideoUrl", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.courses, { onDelete: 'CASCADE' }),
    __metadata("design:type", user_entity_1.User)
], Course.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => topic_entity_1.Topic, (topic) => topic.courses, { cascade: true }),
    __metadata("design:type", Array)
], Course.prototype, "topics", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => enrollment_entity_1.Enrollment, (enrollment) => enrollment.course),
    __metadata("design:type", Array)
], Course.prototype, "enrollments", void 0);
exports.Course = Course = __decorate([
    (0, typeorm_1.Entity)()
], Course);
//# sourceMappingURL=course.entity.js.map