import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CodeOfEthicsController } from './codeOfEthics.controller';
import { CodeOfEthicsService } from './codeOfEthics.services';
import { CodeOfEthics } from './Entity/codeOfEthics.entity';

@Module({
  imports: [TypeOrmModule.forFeature([CodeOfEthics])],
  controllers: [CodeOfEthicsController],
  providers: [CodeOfEthicsService],
  exports: [CodeOfEthicsService],
})
export class CodeOfEthicsModule {}
