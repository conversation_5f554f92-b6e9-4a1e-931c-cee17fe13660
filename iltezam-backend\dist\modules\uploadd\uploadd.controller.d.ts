import { UploaddService } from './uploadd.service';
export declare class UploaddController {
    private readonly uploadService;
    constructor(uploadService: UploaddService);
    uploadPhoto(file: Express.Multer.File): Promise<{
        message: string;
        url: string;
        fileName: string;
        size: number;
    }>;
    uploadMultiplePhotos(files: Express.Multer.File[]): Promise<{
        message: string;
        files: {
            url: string;
            fileName: string;
            size: number;
        }[];
    }>;
}
