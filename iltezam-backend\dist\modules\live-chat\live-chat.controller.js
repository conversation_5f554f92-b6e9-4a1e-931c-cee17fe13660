"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LiveChatController = void 0;
const common_1 = require("@nestjs/common");
const live_chat_service_1 = require("./live-chat.service");
const passport_1 = require("@nestjs/passport");
let LiveChatController = class LiveChatController {
    liveChatService;
    constructor(liveChatService) {
        this.liveChatService = liveChatService;
    }
    async createRoom(req, body) {
        const sender = req.user.id;
        const { receiver } = body;
        return this.liveChatService.createRoom(sender, receiver);
    }
    async sendMessage(req, body) {
        const sender = req.user.id;
        const { roomId, receiver, message } = body;
        console.log('sender', sender);
        console.log('roomId', roomId);
        console.log('receiver', receiver);
        console.log('message', message);
        return this.liveChatService.sendMessage(sender, roomId, receiver, message);
    }
    async getMessages(roomId, req) {
        const userId = req.user.id;
        return this.liveChatService.getMessages(roomId, userId);
    }
    async getInbox(req) {
        const userId = req.user.id;
        return this.liveChatService.getInbox(userId);
    }
    async getRoom(req, partnerId) {
        const userId = req.user.id;
        return this.liveChatService.getRoom(userId, Number(partnerId));
    }
    async createGroupRoom(req, body) {
        const creator = req.user.id;
        const { users, groupName, groupPicture } = body;
        return this.liveChatService.createGroupRoom(creator, users, groupName, groupPicture);
    }
    async sendGroupMessage(req, body) {
        const sender = req.user.id;
        const { roomId, message } = body;
        return this.liveChatService.sendGroupMessage(sender, roomId, message);
    }
    async getAllGroups(req) {
        const userId = req.user.id;
        return this.liveChatService.getAllGroups(userId);
    }
    async joinGroup(roomId, req) {
        const userId = req.user.id;
        return this.liveChatService.joinGroup(roomId, userId);
    }
};
exports.LiveChatController = LiveChatController;
__decorate([
    (0, common_1.Post)('create-room'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "createRoom", null);
__decorate([
    (0, common_1.Post)('send-message'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Get)('messages/:roomId'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Param)('roomId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "getMessages", null);
__decorate([
    (0, common_1.Get)('inbox'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "getInbox", null);
__decorate([
    (0, common_1.Post)('room/:id'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "getRoom", null);
__decorate([
    (0, common_1.Post)('create-group'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "createGroupRoom", null);
__decorate([
    (0, common_1.Post)('send-group-message'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "sendGroupMessage", null);
__decorate([
    (0, common_1.Get)('groups'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "getAllGroups", null);
__decorate([
    (0, common_1.Post)('groups/:roomId/join'),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('jwt')),
    __param(0, (0, common_1.Param)('roomId')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], LiveChatController.prototype, "joinGroup", null);
exports.LiveChatController = LiveChatController = __decorate([
    (0, common_1.Controller)('live-chat'),
    __metadata("design:paramtypes", [live_chat_service_1.LiveChatService])
], LiveChatController);
//# sourceMappingURL=live-chat.controller.js.map